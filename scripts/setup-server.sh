#!/bin/bash

# =============================================================================
# Mirage API Server Setup Script
# =============================================================================
# This script sets up a VPS for deploying the Mirage API server
# Run this script on your VPS as root or with sudo privileges
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NODE_VERSION="18"
DEPLOY_USER="deploy"
APP_NAME="mirage-api"
DEPLOY_PATH="/opt/mirage-api"
LOG_PATH="/var/log/mirage-api"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root or with sudo"
        exit 1
    fi
}

# Update system packages
update_system() {
    log_info "Updating system packages..."
    apt-get update -y
    apt-get upgrade -y
    apt-get install -y curl wget git unzip software-properties-common
    log_success "System packages updated"
}

# Install Node.js
install_nodejs() {
    log_info "Installing Node.js ${NODE_VERSION}..."
    
    # Remove existing Node.js installations
    apt-get remove -y nodejs npm || true
    
    # Install Node.js using NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    apt-get install -y nodejs
    
    # Verify installation
    node_version=$(node --version)
    npm_version=$(npm --version)
    
    log_success "Node.js ${node_version} and npm ${npm_version} installed"
}

# Install PM2
install_pm2() {
    log_info "Installing PM2 process manager..."
    npm install -g pm2
    
    # Setup PM2 startup script
    pm2 startup systemd -u $DEPLOY_USER --hp /home/<USER>
    
    log_success "PM2 installed and configured"
}

# Create deploy user
create_deploy_user() {
    log_info "Creating deploy user..."
    
    # Create user if doesn't exist
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -m -s /bin/bash $DEPLOY_USER
        log_success "Deploy user '$DEPLOY_USER' created"
    else
        log_warning "Deploy user '$DEPLOY_USER' already exists"
    fi
    
    # Add to sudo group
    usermod -aG sudo $DEPLOY_USER
    
    # Create SSH directory
    mkdir -p /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    chown $DEPLOY_USER:$DEPLOY_USER /home/<USER>/.ssh
    
    log_success "Deploy user configured"
}

# Setup directories
setup_directories() {
    log_info "Setting up application directories..."
    
    # Create deployment directory
    mkdir -p $DEPLOY_PATH
    chown $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH
    chmod 755 $DEPLOY_PATH
    
    # Create log directory
    mkdir -p $LOG_PATH
    chown $DEPLOY_USER:$DEPLOY_USER $LOG_PATH
    chmod 755 $LOG_PATH
    
    # Create backup directory
    mkdir -p /opt/backups/mirage-api
    chown $DEPLOY_USER:$DEPLOY_USER /opt/backups/mirage-api
    
    log_success "Directories created and configured"
}

# Configure firewall
configure_firewall() {
    log_info "Configuring firewall..."
    
    # Install UFW if not present
    apt-get install -y ufw
    
    # Reset UFW to defaults
    ufw --force reset
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow SSH
    ufw allow ssh
    ufw allow 22
    
    # Allow HTTP and HTTPS
    ufw allow 80
    ufw allow 443
    
    # Allow application port
    ufw allow 3000
    
    # Enable firewall
    ufw --force enable
    
    log_success "Firewall configured"
}

# Install Nginx (optional reverse proxy)
install_nginx() {
    log_info "Installing Nginx..."
    
    apt-get install -y nginx
    
    # Create Nginx configuration for Mirage API
    cat > /etc/nginx/sites-available/mirage-api << EOF
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # WebSocket support
    location /ws {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # Enable the site
    ln -sf /etc/nginx/sites-available/mirage-api /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    nginx -t
    
    # Start and enable Nginx
    systemctl start nginx
    systemctl enable nginx
    
    log_success "Nginx installed and configured"
}

# Setup log rotation
setup_log_rotation() {
    log_info "Setting up log rotation..."
    
    cat > /etc/logrotate.d/mirage-api << EOF
$LOG_PATH/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 $DEPLOY_USER $DEPLOY_USER
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
    
    log_success "Log rotation configured"
}

# Setup monitoring (optional)
setup_monitoring() {
    log_info "Setting up basic monitoring..."
    
    # Install htop for process monitoring
    apt-get install -y htop
    
    # Create a simple health check script
    cat > /usr/local/bin/mirage-health-check << 'EOF'
#!/bin/bash
# Simple health check for Mirage API

HEALTH_URL="http://localhost:3000/"
LOG_FILE="/var/log/mirage-api/health-check.log"

# Check if the API is responding
if curl -f -s $HEALTH_URL > /dev/null; then
    echo "$(date): API is healthy" >> $LOG_FILE
    exit 0
else
    echo "$(date): API is down, attempting restart" >> $LOG_FILE
    pm2 restart mirage-api
    exit 1
fi
EOF
    
    chmod +x /usr/local/bin/mirage-health-check
    
    # Add cron job for health checks (every 5 minutes)
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/mirage-health-check") | crontab -
    
    log_success "Basic monitoring configured"
}

# Main installation function
main() {
    log_info "Starting Mirage API server setup..."
    
    check_root
    update_system
    install_nodejs
    install_pm2
    create_deploy_user
    setup_directories
    configure_firewall
    
    # Ask if user wants Nginx
    read -p "Do you want to install Nginx as reverse proxy? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        install_nginx
    fi
    
    setup_log_rotation
    setup_monitoring
    
    log_success "Server setup completed successfully!"
    log_info "Next steps:"
    echo "1. Add your SSH public key to /home/<USER>/.ssh/authorized_keys"
    echo "2. Configure GitLab CI/CD variables"
    echo "3. Update Nginx server_name if you installed Nginx"
    echo "4. Test the deployment pipeline"
    
    log_info "Server Information:"
    echo "- Deploy user: $DEPLOY_USER"
    echo "- Deploy path: $DEPLOY_PATH"
    echo "- Log path: $LOG_PATH"
    echo "- Node.js version: $(node --version)"
    echo "- PM2 version: $(pm2 --version)"
}

# Run main function
main "$@"
