/**
 * Common error handling functions
 * 
 * This file contains functions for handling common error scenarios
 * and returning appropriate error responses.
 */

/**
 * Handle server errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.serverError = (res, message = 'Internal server error') => {
  return res.status(500).json({
    success: false,
    message
  });
};

/**
 * Handle not found errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.notFound = (res, message = 'Resource not found') => {
  return res.status(404).json({
    success: false,
    message
  });
};

/**
 * Handle bad request errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 * @param {Array} errors - Optional validation errors
 */
exports.badRequest = (res, message = 'Bad request', errors = null) => {
  const response = {
    success: false,
    message
  };
  
  if (errors) {
    response.errors = errors;
  }
  
  return res.status(400).json(response);
};

/**
 * <PERSON>le unauthorized errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.unauthorized = (res, message = 'Unauthorized') => {
  return res.status(401).json({
    success: false,
    message
  });
};

/**
 * Handle forbidden errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.forbidden = (res, message = 'Forbidden') => {
  return res.status(403).json({
    success: false,
    message
  });
};

/**
 * Handle validation errors
 * @param {Object} res - Express response object
 * @param {Array} errors - Validation errors
 */
exports.validationError = (res, errors) => {
  return res.status(422).json({
    success: false,
    message: 'Validation failed',
    errors
  });
};
