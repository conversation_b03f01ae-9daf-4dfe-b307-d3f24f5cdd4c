/**
 * Deployment Validation Script
 * 
 * This script validates that all deployment files are present and properly configured
 */

const fs = require('fs');
const path = require('path');

// Required deployment files
const requiredFiles = [
  '.gitlab-ci.yml',
  'ecosystem.config.js',
  'Dockerfile',
  'docker-compose.yml',
  '.dockerignore',
  'DEPLOYMENT_GUIDE.md',
  'QUICK_SETUP_GUIDE.md',
  'DEPLOYMENT_SUMMARY.md',
  'scripts/setup-server.sh'
];

// Optional files
const optionalFiles = [
  'nginx/nginx.conf',
  'monitoring/prometheus.yml'
];

function validateFile(filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);
    const stats = fs.statSync(fullPath);
    
    if (stats.isFile()) {
      const size = stats.size;
      console.log(`✅ ${filePath} (${size} bytes)`);
      return true;
    } else {
      console.log(`❌ ${filePath} - Not a file`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${filePath} - Missing`);
    return false;
  }
}

function validateGitLabCI() {
  try {
    const content = fs.readFileSync('.gitlab-ci.yml', 'utf8');
    
    // Check for required stages
    const requiredStages = ['validate', 'test', 'build', 'deploy', 'verify'];
    const hasAllStages = requiredStages.every(stage => content.includes(stage));
    
    if (hasAllStages) {
      console.log('✅ GitLab CI/CD pipeline has all required stages');
    } else {
      console.log('❌ GitLab CI/CD pipeline missing required stages');
    }
    
    // Check for required variables
    const requiredVars = ['SSH_PRIVATE_KEY', 'DEPLOY_USER', 'STAGING_SERVER_IP', 'PRODUCTION_SERVER_IP'];
    const hasAllVars = requiredVars.every(variable => content.includes(variable));
    
    if (hasAllVars) {
      console.log('✅ GitLab CI/CD pipeline references all required variables');
    } else {
      console.log('❌ GitLab CI/CD pipeline missing required variable references');
    }
    
    return hasAllStages && hasAllVars;
  } catch (error) {
    console.log('❌ Failed to validate GitLab CI/CD file');
    return false;
  }
}

function validateDockerfile() {
  try {
    const content = fs.readFileSync('Dockerfile', 'utf8');
    
    // Check for multi-stage build
    const hasMultiStage = content.includes('FROM node:18-alpine AS builder') && 
                         content.includes('FROM node:18-alpine AS production');
    
    if (hasMultiStage) {
      console.log('✅ Dockerfile uses multi-stage build');
    } else {
      console.log('❌ Dockerfile missing multi-stage build');
    }
    
    // Check for health check
    const hasHealthCheck = content.includes('HEALTHCHECK');
    
    if (hasHealthCheck) {
      console.log('✅ Dockerfile includes health check');
    } else {
      console.log('❌ Dockerfile missing health check');
    }
    
    return hasMultiStage && hasHealthCheck;
  } catch (error) {
    console.log('❌ Failed to validate Dockerfile');
    return false;
  }
}

function validatePackageJson() {
  try {
    const content = fs.readFileSync('package.json', 'utf8');
    const packageJson = JSON.parse(content);
    
    // Check for required scripts
    const requiredScripts = ['start'];
    const hasAllScripts = requiredScripts.every(script => packageJson.scripts && packageJson.scripts[script]);
    
    if (hasAllScripts) {
      console.log('✅ package.json has all required scripts');
    } else {
      console.log('❌ package.json missing required scripts');
    }
    
    // Check Node.js version requirement
    const nodeVersion = packageJson.engines && packageJson.engines.node;
    if (nodeVersion && nodeVersion.includes('18')) {
      console.log('✅ package.json specifies Node.js 18');
    } else {
      console.log('⚠️  package.json should specify Node.js 18 requirement');
    }
    
    return hasAllScripts;
  } catch (error) {
    console.log('❌ Failed to validate package.json');
    return false;
  }
}

function main() {
  console.log('🔍 Validating Mirage API deployment configuration...\n');
  
  let allValid = true;
  
  // Check required files
  console.log('📁 Checking required files:');
  requiredFiles.forEach(file => {
    if (!validateFile(file)) {
      allValid = false;
    }
  });
  
  console.log('\n📁 Checking optional files:');
  optionalFiles.forEach(file => {
    validateFile(file);
  });
  
  console.log('\n🔧 Validating configuration files:');
  
  // Validate GitLab CI/CD
  if (!validateGitLabCI()) {
    allValid = false;
  }
  
  // Validate Dockerfile
  if (!validateDockerfile()) {
    allValid = false;
  }
  
  // Validate package.json
  if (!validatePackageJson()) {
    allValid = false;
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (allValid) {
    console.log('🎉 All deployment files are present and properly configured!');
    console.log('\n📋 Next steps:');
    console.log('1. Configure GitLab CI/CD variables');
    console.log('2. Setup your VPS using scripts/setup-server.sh');
    console.log('3. Push to develop branch to test staging deployment');
    console.log('4. Push to main branch for production deployment');
    console.log('\n📚 See DEPLOYMENT_GUIDE.md for detailed instructions');
    process.exit(0);
  } else {
    console.log('❌ Some deployment files are missing or misconfigured');
    console.log('\n📚 Please check the deployment documentation and fix the issues above');
    process.exit(1);
  }
}

// Run validation
main();
