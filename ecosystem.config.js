/**
 * PM2 Ecosystem Configuration for Mirage API Server
 * 
 * This configuration file defines how PM<PERSON> should manage the Mirage API server
 * in different environments (staging and production).
 */

module.exports = {
  apps: [
    {
      // Production Configuration
      name: 'mirage-api-prod',
      script: 'app.js',
      instances: 'max', // Use all available CPU cores
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        LOG_LEVEL: 'info'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        LOG_LEVEL: 'warn'
      },
      // Logging
      log_file: '/var/log/mirage-api/combined.log',
      out_file: '/var/log/mirage-api/out.log',
      error_file: '/var/log/mirage-api/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Advanced features
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Health monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true
    },
    {
      // Staging Configuration
      name: 'mirage-api-staging',
      script: 'app.js',
      instances: 1, // Single instance for staging
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'staging',
        PORT: 3000,
        LOG_LEVEL: 'debug'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000,
        LOG_LEVEL: 'debug'
      },
      // Logging
      log_file: '/var/log/mirage-api/staging-combined.log',
      out_file: '/var/log/mirage-api/staging-out.log',
      error_file: '/var/log/mirage-api/staging-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      autorestart: true,
      watch: true, // Enable file watching in staging
      ignore_watch: ['node_modules', 'logs', '*.log'],
      max_memory_restart: '512M',
      restart_delay: 2000,
      max_restarts: 15,
      min_uptime: '5s'
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['production-server-ip'],
      ref: 'origin/main',
      repo: '**************:your-username/mirage-api.git',
      path: '/opt/mirage-api',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    staging: {
      user: 'deploy',
      host: ['staging-server-ip'],
      ref: 'origin/develop',
      repo: '**************:your-username/mirage-api.git',
      path: '/opt/mirage-api-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --production && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
