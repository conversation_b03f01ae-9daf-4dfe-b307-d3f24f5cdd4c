import { MaybePromise } from '@whatwg-node/promise-helpers';
import type { ServerAdapterPlugin } from './types.cjs';
export declare function createDefaultErrorHandler<TServerContext = {}>(ResponseCtor?: typeof Response): <PERSON>rror<PERSON><PERSON>ler<TServerContext>;
export declare class HTTPError extends Error {
    status: number;
    message: string;
    headers: HeadersInit;
    details?: any | undefined;
    name: string;
    constructor(status: number | undefined, message: string, headers?: HeadersInit, details?: any | undefined);
}
export type ErrorHandler<TServerContext> = (e: any, request: Request, ctx: TServerContext) => MaybePromise<Response> | void;
export declare function useErrorHandling<TServerContext>(onError?: Error<PERSON>andler<TServerContext>): ServerAdapterPlugin<TServerContext>;
