{"name": "@whatwg-node/promise-helpers", "version": "1.3.2", "description": "Promise helpers", "sideEffects": false, "dependencies": {"tslib": "^2.6.3"}, "repository": {"type": "git", "url": "ardatan/whatwg-node", "directory": "packages/promise-helpers"}, "author": "Arda TANRIKULU <<EMAIL>>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}