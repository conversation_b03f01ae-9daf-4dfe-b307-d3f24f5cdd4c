"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuppressedError = exports.AsyncDisposableStack = exports.DisposableStack = void 0;
const tslib_1 = require("tslib");
const AsyncDisposableStack_js_1 = require("./AsyncDisposableStack.js");
const DisposableStack_js_1 = require("./DisposableStack.js");
const SupressedError_js_1 = require("./SupressedError.js");
exports.DisposableStack = globalThis.DisposableStack || DisposableStack_js_1.PonyfillDisposableStack;
exports.AsyncDisposableStack = globalThis.AsyncDisposableStack || AsyncDisposableStack_js_1.PonyfillAsyncDisposableStack;
exports.SuppressedError = globalThis.SuppressedError || SupressedError_js_1.PonyfillSuppressedError;
tslib_1.__exportStar(require("./symbols.js"), exports);
