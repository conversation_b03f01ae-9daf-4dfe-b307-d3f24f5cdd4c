import { F as Faker, L as LocaleDefinition, R as Randomizer } from './airline-BUL6NtOJ.js';
export { r as Aircraft, s as AircraftType, A as AirlineDefinition, t as AirlineModule, a as AnimalDefinition, u as AnimalModule, $ as BitcoinAddressFamily, a1 as BitcoinAddressFamilyType, a0 as BitcoinNetwork, a2 as BitcoinNetworkType, B as BookDefinition, v as BookModule, y as Casing, ak as ChemicalElement, C as ColorDefinition, z as ColorFormat, E as ColorModule, b as CommerceDefinition, O as CommerceModule, c as CommerceProductNameDefinition, d as CompanyDefinition, Q as CompanyModule, w as CssFunction, G as CssFunctionType, x as CssSpace, J as CssSpaceType, Z as Currency, D as DatabaseDefinition, T as DatabaseModule, U as DatatypeModule, e as DateDefinition, f as DateEntryDefinition, X as DateModule, q as FakerOptions, g as FinanceDefinition, _ as FinanceModule, h as FoodDefinition, a3 as FoodModule, a4 as GitModule, H as HackerDefinition, a5 as HackerModule, a6 as HelpersModule, a9 as IPv4Network, aa as IPv4NetworkType, a8 as ImageModule, I as InternetDefinition, ab as InternetModule, i as LocaleEntry, j as LocationDefinition, ac as LocationModule, k as LoremDefinition, ad as LoremModule, M as MetadataDefinition, l as MusicDefinition, ae as MusicModule, N as NumberColorFormat, af as NumberModule, P as PersonDefinition, m as PersonEntryDefinition, ah as PersonModule, aj as PhoneModule, n as PhoneNumberDefinition, S as ScienceDefinition, al as ScienceModule, ag as Sex, ai as SexType, Y as SimpleDateModule, ar as SimpleFaker, a7 as SimpleHelpersModule, K as StringColorFormat, an as StringModule, o as SystemDefinition, p as SystemMimeTypeEntryDefinition, ao as SystemModule, am as Unit, V as VehicleDefinition, ap as VehicleModule, W as WordDefinition, aq as WordModule, as as simpleFaker } from './airline-BUL6NtOJ.js';
export { faker, faker as fakerEN } from './locale/en.js';
export { faker as fakerAF_ZA } from './locale/af_ZA.js';
export { faker as fakerAR } from './locale/ar.js';
export { faker as fakerAZ } from './locale/az.js';
export { faker as fakerBASE } from './locale/base.js';
export { faker as fakerBN_BD } from './locale/bn_BD.js';
export { faker as fakerCS_CZ } from './locale/cs_CZ.js';
export { faker as fakerCY } from './locale/cy.js';
export { faker as fakerDA } from './locale/da.js';
export { faker as fakerDE } from './locale/de.js';
export { faker as fakerDE_AT } from './locale/de_AT.js';
export { faker as fakerDE_CH } from './locale/de_CH.js';
export { faker as fakerDV } from './locale/dv.js';
export { faker as fakerEL } from './locale/el.js';
export { faker as fakerEN_AU } from './locale/en_AU.js';
export { faker as fakerEN_AU_ocker } from './locale/en_AU_ocker.js';
export { faker as fakerEN_BORK } from './locale/en_BORK.js';
export { faker as fakerEN_CA } from './locale/en_CA.js';
export { faker as fakerEN_GB } from './locale/en_GB.js';
export { faker as fakerEN_GH } from './locale/en_GH.js';
export { faker as fakerEN_HK } from './locale/en_HK.js';
export { faker as fakerEN_IE } from './locale/en_IE.js';
export { faker as fakerEN_IN } from './locale/en_IN.js';
export { faker as fakerEN_NG } from './locale/en_NG.js';
export { faker as fakerEN_US } from './locale/en_US.js';
export { faker as fakerEN_ZA } from './locale/en_ZA.js';
export { faker as fakerEO } from './locale/eo.js';
export { faker as fakerES } from './locale/es.js';
export { faker as fakerES_MX } from './locale/es_MX.js';
export { faker as fakerFA } from './locale/fa.js';
export { faker as fakerFI } from './locale/fi.js';
export { faker as fakerFR } from './locale/fr.js';
export { faker as fakerFR_BE } from './locale/fr_BE.js';
export { faker as fakerFR_CA } from './locale/fr_CA.js';
export { faker as fakerFR_CH } from './locale/fr_CH.js';
export { faker as fakerFR_LU } from './locale/fr_LU.js';
export { faker as fakerFR_SN } from './locale/fr_SN.js';
export { faker as fakerHE } from './locale/he.js';
export { faker as fakerHR } from './locale/hr.js';
export { faker as fakerHU } from './locale/hu.js';
export { faker as fakerHY } from './locale/hy.js';
export { faker as fakerID_ID } from './locale/id_ID.js';
export { faker as fakerIT } from './locale/it.js';
export { faker as fakerJA } from './locale/ja.js';
export { faker as fakerKA_GE } from './locale/ka_GE.js';
export { faker as fakerKO } from './locale/ko.js';
export { faker as fakerLV } from './locale/lv.js';
export { faker as fakerMK } from './locale/mk.js';
export { faker as fakerNB_NO } from './locale/nb_NO.js';
export { faker as fakerNE } from './locale/ne.js';
export { faker as fakerNL } from './locale/nl.js';
export { faker as fakerNL_BE } from './locale/nl_BE.js';
export { faker as fakerPL } from './locale/pl.js';
export { faker as fakerPT_BR } from './locale/pt_BR.js';
export { faker as fakerPT_PT } from './locale/pt_PT.js';
export { faker as fakerRO } from './locale/ro.js';
export { faker as fakerRO_MD } from './locale/ro_MD.js';
export { faker as fakerRU } from './locale/ru.js';
export { faker as fakerSK } from './locale/sk.js';
export { faker as fakerSR_RS_latin } from './locale/sr_RS_latin.js';
export { faker as fakerSV } from './locale/sv.js';
export { faker as fakerTA_IN } from './locale/ta_IN.js';
export { faker as fakerTH } from './locale/th.js';
export { faker as fakerTR } from './locale/tr.js';
export { faker as fakerUK } from './locale/uk.js';
export { faker as fakerUR } from './locale/ur.js';
export { faker as fakerUZ_UZ_latin } from './locale/uz_UZ_latin.js';
export { faker as fakerVI } from './locale/vi.js';
export { faker as fakerYO_NG } from './locale/yo_NG.js';
export { faker as fakerZH_CN } from './locale/zh_CN.js';
export { faker as fakerZH_TW } from './locale/zh_TW.js';
export { faker as fakerZU_ZA } from './locale/zu_ZA.js';

/**
 * An error instance that will be thrown by faker.
 */
declare class FakerError extends Error {
}

declare const allFakers: {
    readonly af_ZA: Faker;
    readonly ar: Faker;
    readonly az: Faker;
    readonly base: Faker;
    readonly bn_BD: Faker;
    readonly cs_CZ: Faker;
    readonly cy: Faker;
    readonly da: Faker;
    readonly de: Faker;
    readonly de_AT: Faker;
    readonly de_CH: Faker;
    readonly dv: Faker;
    readonly el: Faker;
    readonly en: Faker;
    readonly en_AU: Faker;
    readonly en_AU_ocker: Faker;
    readonly en_BORK: Faker;
    readonly en_CA: Faker;
    readonly en_GB: Faker;
    readonly en_GH: Faker;
    readonly en_HK: Faker;
    readonly en_IE: Faker;
    readonly en_IN: Faker;
    readonly en_NG: Faker;
    readonly en_US: Faker;
    readonly en_ZA: Faker;
    readonly eo: Faker;
    readonly es: Faker;
    readonly es_MX: Faker;
    readonly fa: Faker;
    readonly fi: Faker;
    readonly fr: Faker;
    readonly fr_BE: Faker;
    readonly fr_CA: Faker;
    readonly fr_CH: Faker;
    readonly fr_LU: Faker;
    readonly fr_SN: Faker;
    readonly he: Faker;
    readonly hr: Faker;
    readonly hu: Faker;
    readonly hy: Faker;
    readonly id_ID: Faker;
    readonly it: Faker;
    readonly ja: Faker;
    readonly ka_GE: Faker;
    readonly ko: Faker;
    readonly lv: Faker;
    readonly mk: Faker;
    readonly nb_NO: Faker;
    readonly ne: Faker;
    readonly nl: Faker;
    readonly nl_BE: Faker;
    readonly pl: Faker;
    readonly pt_BR: Faker;
    readonly pt_PT: Faker;
    readonly ro: Faker;
    readonly ro_MD: Faker;
    readonly ru: Faker;
    readonly sk: Faker;
    readonly sr_RS_latin: Faker;
    readonly sv: Faker;
    readonly ta_IN: Faker;
    readonly th: Faker;
    readonly tr: Faker;
    readonly uk: Faker;
    readonly ur: Faker;
    readonly uz_UZ_latin: Faker;
    readonly vi: Faker;
    readonly yo_NG: Faker;
    readonly zh_CN: Faker;
    readonly zh_TW: Faker;
    readonly zu_ZA: Faker;
};

/**
 * The locale data for the `af_ZA` locale.
 *
 * - Language: Afrikaans (South Africa)
 * - Endonym: Afrikaans (Suid-Afrika)
 */
declare const af_ZA: LocaleDefinition;

/**
 * The locale data for the `ar` locale.
 *
 * - Language: Arabic
 * - Endonym: اَلْعَرَبِيَّةُ
 */
declare const ar: LocaleDefinition;

/**
 * The locale data for the `az` locale.
 *
 * - Language: Azerbaijani
 * - Endonym: azərbaycan dili
 */
declare const az: LocaleDefinition;

/**
 * The locale data for the `base` locale.
 *
 * The base locale contains data that is shared across all locales such as ISO codes, time zones, and more.
 */
declare const base: LocaleDefinition;

/**
 * The locale data for the `bn_BD` locale.
 *
 * - Language: Bengali (Bangladesh)
 * - Endonym: বাংলা (বাংলাদেশ)
 */
declare const bn_BD: LocaleDefinition;

/**
 * The locale data for the `cs_CZ` locale.
 *
 * - Language: Czech (Czechia)
 * - Endonym: čeština (Česká republika)
 */
declare const cs_CZ: LocaleDefinition;

/**
 * The locale data for the `cy` locale.
 *
 * - Language: Welsh
 * - Endonym: Cymraeg
 */
declare const cy: LocaleDefinition;

/**
 * The locale data for the `da` locale.
 *
 * - Language: Danish
 * - Endonym: Dansk
 */
declare const da: LocaleDefinition;

/**
 * The locale data for the `de` locale.
 *
 * - Language: German
 * - Endonym: Deutsch
 */
declare const de: LocaleDefinition;

/**
 * The locale data for the `de_AT` locale.
 *
 * - Language: German (Austria)
 * - Endonym: Deutsch (Österreich)
 */
declare const de_AT: LocaleDefinition;

/**
 * The locale data for the `de_CH` locale.
 *
 * - Language: German (Switzerland)
 * - Endonym: Deutsch (Schweiz)
 */
declare const de_CH: LocaleDefinition;

/**
 * The locale data for the `dv` locale.
 *
 * - Language: Maldivian
 * - Endonym: ދިވެހި
 */
declare const dv: LocaleDefinition;

/**
 * The locale data for the `el` locale.
 *
 * - Language: Greek
 * - Endonym: Ελληνικά
 */
declare const el: LocaleDefinition;

/**
 * The locale data for the `en` locale.
 *
 * - Language: English
 * - Endonym: English
 */
declare const en: LocaleDefinition;

/**
 * The locale data for the `en_AU` locale.
 *
 * - Language: English (Australia)
 * - Endonym: English (Australia)
 */
declare const en_AU: LocaleDefinition;

/**
 * The locale data for the `en_AU_ocker` locale.
 *
 * - Language: English (Australia Ocker)
 * - Endonym: English (Australia)
 */
declare const en_AU_ocker: LocaleDefinition;

/**
 * The locale data for the `en_BORK` locale.
 *
 * - Language: English (Bork)
 * - Endonym: English (Bork)
 */
declare const en_BORK: LocaleDefinition;

/**
 * The locale data for the `en_CA` locale.
 *
 * - Language: English (Canada)
 * - Endonym: English (Canada)
 */
declare const en_CA: LocaleDefinition;

/**
 * The locale data for the `en_GB` locale.
 *
 * - Language: English (Great Britain)
 * - Endonym: English (Great Britain)
 */
declare const en_GB: LocaleDefinition;

/**
 * The locale data for the `en_GH` locale.
 *
 * - Language: English (Ghana)
 * - Endonym: English (Ghana)
 */
declare const en_GH: LocaleDefinition;

/**
 * The locale data for the `en_HK` locale.
 *
 * - Language: English (Hong Kong)
 * - Endonym: English (Hong Kong)
 */
declare const en_HK: LocaleDefinition;

/**
 * The locale data for the `en_IE` locale.
 *
 * - Language: English (Ireland)
 * - Endonym: English (Ireland)
 */
declare const en_IE: LocaleDefinition;

/**
 * The locale data for the `en_IN` locale.
 *
 * - Language: English (India)
 * - Endonym: English (India)
 */
declare const en_IN: LocaleDefinition;

/**
 * The locale data for the `en_NG` locale.
 *
 * - Language: English (Nigeria)
 * - Endonym: English (Nigeria)
 */
declare const en_NG: LocaleDefinition;

/**
 * The locale data for the `en_US` locale.
 *
 * - Language: English (United States)
 * - Endonym: English (United States)
 */
declare const en_US: LocaleDefinition;

/**
 * The locale data for the `en_ZA` locale.
 *
 * - Language: English (South Africa)
 * - Endonym: English (South Africa)
 */
declare const en_ZA: LocaleDefinition;

/**
 * The locale data for the `eo` locale.
 *
 * - Language: Esperanto
 * - Endonym: Esperanto
 */
declare const eo: LocaleDefinition;

/**
 * The locale data for the `es` locale.
 *
 * - Language: Spanish
 * - Endonym: Español
 */
declare const es: LocaleDefinition;

/**
 * The locale data for the `es_MX` locale.
 *
 * - Language: Spanish (Mexico)
 * - Endonym: Español (México)
 */
declare const es_MX: LocaleDefinition;

/**
 * The locale data for the `fa` locale.
 *
 * - Language: Farsi/Persian
 * - Endonym: فارسی
 */
declare const fa: LocaleDefinition;

/**
 * The locale data for the `fi` locale.
 *
 * - Language: Finnish
 * - Endonym: suomi
 */
declare const fi: LocaleDefinition;

/**
 * The locale data for the `fr` locale.
 *
 * - Language: French
 * - Endonym: Français
 */
declare const fr: LocaleDefinition;

/**
 * The locale data for the `fr_BE` locale.
 *
 * - Language: French (Belgium)
 * - Endonym: Français (Belgique)
 */
declare const fr_BE: LocaleDefinition;

/**
 * The locale data for the `fr_CA` locale.
 *
 * - Language: French (Canada)
 * - Endonym: Français (Canada)
 */
declare const fr_CA: LocaleDefinition;

/**
 * The locale data for the `fr_CH` locale.
 *
 * - Language: French (Switzerland)
 * - Endonym: Français (Suisse)
 */
declare const fr_CH: LocaleDefinition;

/**
 * The locale data for the `fr_LU` locale.
 *
 * - Language: French (Luxembourg)
 * - Endonym: Français (Luxembourg)
 */
declare const fr_LU: LocaleDefinition;

/**
 * The locale data for the `fr_SN` locale.
 *
 * - Language: French (Senegal)
 * - Endonym: Français (Sénégal)
 */
declare const fr_SN: LocaleDefinition;

/**
 * The locale data for the `he` locale.
 *
 * - Language: Hebrew
 * - Endonym: עברית
 */
declare const he: LocaleDefinition;

/**
 * The locale data for the `hr` locale.
 *
 * - Language: Croatian
 * - Endonym: Hrvatski
 */
declare const hr: LocaleDefinition;

/**
 * The locale data for the `hu` locale.
 *
 * - Language: Hungarian
 * - Endonym: magyar
 */
declare const hu: LocaleDefinition;

/**
 * The locale data for the `hy` locale.
 *
 * - Language: Armenian
 * - Endonym: Հայերեն
 */
declare const hy: LocaleDefinition;

/**
 * The locale data for the `id_ID` locale.
 *
 * - Language: Indonesian (Indonesia)
 * - Endonym: Bahasa Indonesia (Indonesia)
 */
declare const id_ID: LocaleDefinition;

/**
 * The locale data for the `it` locale.
 *
 * - Language: Italian
 * - Endonym: Italiano
 */
declare const it: LocaleDefinition;

/**
 * The locale data for the `ja` locale.
 *
 * - Language: Japanese
 * - Endonym: 日本語
 */
declare const ja: LocaleDefinition;

/**
 * The locale data for the `ka_GE` locale.
 *
 * - Language: Georgian (Georgia)
 * - Endonym: ქართული (საქართველო)
 */
declare const ka_GE: LocaleDefinition;

/**
 * The locale data for the `ko` locale.
 *
 * - Language: Korean
 * - Endonym: 한국어
 */
declare const ko: LocaleDefinition;

/**
 * The locale data for the `lv` locale.
 *
 * - Language: Latvian
 * - Endonym: latviešu valoda
 */
declare const lv: LocaleDefinition;

/**
 * The locale data for the `mk` locale.
 *
 * - Language: Macedonian
 * - Endonym: македонски јазик
 */
declare const mk: LocaleDefinition;

/**
 * The locale data for the `nb_NO` locale.
 *
 * - Language: Norwegian (Norway)
 * - Endonym: Norsk bokmål (Norge)
 */
declare const nb_NO: LocaleDefinition;

/**
 * The locale data for the `ne` locale.
 *
 * - Language: Nepali
 * - Endonym: नेपाली
 */
declare const ne: LocaleDefinition;

/**
 * The locale data for the `nl` locale.
 *
 * - Language: Dutch
 * - Endonym: Nederlands
 */
declare const nl: LocaleDefinition;

/**
 * The locale data for the `nl_BE` locale.
 *
 * - Language: Dutch (Belgium)
 * - Endonym: Nederlands (België)
 */
declare const nl_BE: LocaleDefinition;

/**
 * The locale data for the `pl` locale.
 *
 * - Language: Polish
 * - Endonym: Polski
 */
declare const pl: LocaleDefinition;

/**
 * The locale data for the `pt_BR` locale.
 *
 * - Language: Portuguese (Brazil)
 * - Endonym: Português (Brasil)
 */
declare const pt_BR: LocaleDefinition;

/**
 * The locale data for the `pt_PT` locale.
 *
 * - Language: Portuguese (Portugal)
 * - Endonym: Português (Portugal)
 */
declare const pt_PT: LocaleDefinition;

/**
 * The locale data for the `ro` locale.
 *
 * - Language: Romanian
 * - Endonym: Română
 */
declare const ro: LocaleDefinition;

/**
 * The locale data for the `ro_MD` locale.
 *
 * - Language: Romanian (Moldova)
 * - Endonym: Română (Moldova)
 */
declare const ro_MD: LocaleDefinition;

/**
 * The locale data for the `ru` locale.
 *
 * - Language: Russian
 * - Endonym: Русский
 */
declare const ru: LocaleDefinition;

/**
 * The locale data for the `sk` locale.
 *
 * - Language: Slovak
 * - Endonym: slovenčina
 */
declare const sk: LocaleDefinition;

/**
 * The locale data for the `sr_RS_latin` locale.
 *
 * - Language: Serbian (Serbia, Latin)
 * - Endonym: srpski (Srbija, latinica)
 */
declare const sr_RS_latin: LocaleDefinition;

/**
 * The locale data for the `sv` locale.
 *
 * - Language: Swedish
 * - Endonym: Svenska
 */
declare const sv: LocaleDefinition;

/**
 * The locale data for the `ta_IN` locale.
 *
 * - Language: Tamil (India)
 * - Endonym: தமிழ் (இந்தியா)
 */
declare const ta_IN: LocaleDefinition;

/**
 * The locale data for the `th` locale.
 *
 * - Language: Thai
 * - Endonym: ไทย
 */
declare const th: LocaleDefinition;

/**
 * The locale data for the `tr` locale.
 *
 * - Language: Turkish
 * - Endonym: Türkçe
 */
declare const tr: LocaleDefinition;

/**
 * The locale data for the `uk` locale.
 *
 * - Language: Ukrainian
 * - Endonym: Українська
 */
declare const uk: LocaleDefinition;

/**
 * The locale data for the `ur` locale.
 *
 * - Language: Urdu
 * - Endonym: اردو
 */
declare const ur: LocaleDefinition;

/**
 * The locale data for the `uz_UZ_latin` locale.
 *
 * - Language: Uzbek (Uzbekistan, Latin)
 * - Endonym: O'zbekcha
 */
declare const uz_UZ_latin: LocaleDefinition;

/**
 * The locale data for the `vi` locale.
 *
 * - Language: Vietnamese
 * - Endonym: Tiếng Việt
 */
declare const vi: LocaleDefinition;

/**
 * The locale data for the `yo_NG` locale.
 *
 * - Language: Yoruba (Nigeria)
 * - Endonym: Yoruba (Naijiria)
 */
declare const yo_NG: LocaleDefinition;

/**
 * The locale data for the `zh_CN` locale.
 *
 * - Language: Chinese (China)
 * - Endonym: 中文 (中国)
 */
declare const zh_CN: LocaleDefinition;

/**
 * The locale data for the `zh_TW` locale.
 *
 * - Language: Chinese (Taiwan)
 * - Endonym: 中文 (臺灣)
 */
declare const zh_TW: LocaleDefinition;

/**
 * The locale data for the `zu_ZA` locale.
 *
 * - Language: Zulu (South Africa)
 * - Endonym: isiZulu (Iningizimu Afrika)
 */
declare const zu_ZA: LocaleDefinition;

declare const allLocales: {
    readonly af_ZA: LocaleDefinition;
    readonly ar: LocaleDefinition;
    readonly az: LocaleDefinition;
    readonly base: LocaleDefinition;
    readonly bn_BD: LocaleDefinition;
    readonly cs_CZ: LocaleDefinition;
    readonly cy: LocaleDefinition;
    readonly da: LocaleDefinition;
    readonly de: LocaleDefinition;
    readonly de_AT: LocaleDefinition;
    readonly de_CH: LocaleDefinition;
    readonly dv: LocaleDefinition;
    readonly el: LocaleDefinition;
    readonly en: LocaleDefinition;
    readonly en_AU: LocaleDefinition;
    readonly en_AU_ocker: LocaleDefinition;
    readonly en_BORK: LocaleDefinition;
    readonly en_CA: LocaleDefinition;
    readonly en_GB: LocaleDefinition;
    readonly en_GH: LocaleDefinition;
    readonly en_HK: LocaleDefinition;
    readonly en_IE: LocaleDefinition;
    readonly en_IN: LocaleDefinition;
    readonly en_NG: LocaleDefinition;
    readonly en_US: LocaleDefinition;
    readonly en_ZA: LocaleDefinition;
    readonly eo: LocaleDefinition;
    readonly es: LocaleDefinition;
    readonly es_MX: LocaleDefinition;
    readonly fa: LocaleDefinition;
    readonly fi: LocaleDefinition;
    readonly fr: LocaleDefinition;
    readonly fr_BE: LocaleDefinition;
    readonly fr_CA: LocaleDefinition;
    readonly fr_CH: LocaleDefinition;
    readonly fr_LU: LocaleDefinition;
    readonly fr_SN: LocaleDefinition;
    readonly he: LocaleDefinition;
    readonly hr: LocaleDefinition;
    readonly hu: LocaleDefinition;
    readonly hy: LocaleDefinition;
    readonly id_ID: LocaleDefinition;
    readonly it: LocaleDefinition;
    readonly ja: LocaleDefinition;
    readonly ka_GE: LocaleDefinition;
    readonly ko: LocaleDefinition;
    readonly lv: LocaleDefinition;
    readonly mk: LocaleDefinition;
    readonly nb_NO: LocaleDefinition;
    readonly ne: LocaleDefinition;
    readonly nl: LocaleDefinition;
    readonly nl_BE: LocaleDefinition;
    readonly pl: LocaleDefinition;
    readonly pt_BR: LocaleDefinition;
    readonly pt_PT: LocaleDefinition;
    readonly ro: LocaleDefinition;
    readonly ro_MD: LocaleDefinition;
    readonly ru: LocaleDefinition;
    readonly sk: LocaleDefinition;
    readonly sr_RS_latin: LocaleDefinition;
    readonly sv: LocaleDefinition;
    readonly ta_IN: LocaleDefinition;
    readonly th: LocaleDefinition;
    readonly tr: LocaleDefinition;
    readonly uk: LocaleDefinition;
    readonly ur: LocaleDefinition;
    readonly uz_UZ_latin: LocaleDefinition;
    readonly vi: LocaleDefinition;
    readonly yo_NG: LocaleDefinition;
    readonly zh_CN: LocaleDefinition;
    readonly zh_TW: LocaleDefinition;
    readonly zu_ZA: LocaleDefinition;
};

/**
 * Merges the given locales into one locale.
 * The locales are merged in the order they are given.
 * The first locale that provides an entry for a category will be used for that.
 * Mutating the category entries in the returned locale will also mutate the entries in the respective source locale.
 *
 * @param locales The locales to merge.
 *
 * @returns The newly merged locale.
 *
 * @example
 * import { de_CH, de, en, mergeLocales } from '@faker-js/faker';
 *
 * const de_CH_with_fallbacks = mergeLocales([ de_CH, de, en ]);
 *
 * @since 8.0.0
 */
declare function mergeLocales(locales: LocaleDefinition[]): LocaleDefinition;

/**
 * Generates a MersenneTwister19937 randomizer with 32 bits of precision.
 * This is the default randomizer used by faker prior to v9.0.
 *
 * @param seed The initial seed to use. Defaults to a random number.
 *
 * @example
 * import { de, en, generateMersenne32Randomizer, Faker } from '@faker-js/faker';
 *
 * const randomizer = generateMersenne32Randomizer();
 * randomizer.seed(42);
 * // Share the same randomizer between multiple instances
 * const customFaker1 = new Faker({ locale: de, randomizer });
 * const customFaker2 = new Faker({ locale: en, randomizer });
 *
 * @since 8.2.0
 */
declare function generateMersenne32Randomizer(seed?: number): Randomizer;
/**
 * Generates a MersenneTwister19937 randomizer with 53 bits of precision.
 * This is the default randomizer used by faker starting with v9.0.
 *
 * @param seed The initial seed to use. Defaults to a random number.
 *
 * @example
 * import { de, en, generateMersenne53Randomizer, Faker } from '@faker-js/faker';
 *
 * const randomizer = generateMersenne53Randomizer();
 * randomizer.seed(42);
 * // Share the same randomizer between multiple instances
 * const customFaker1 = new Faker({ locale: de, randomizer });
 * const customFaker2 = new Faker({ locale: en, randomizer });
 *
 * @since 9.0.0
 */
declare function generateMersenne53Randomizer(seed?: number): Randomizer;

export { Faker, FakerError, LocaleDefinition, Randomizer, af_ZA, allFakers, allLocales, ar, az, base, bn_BD, cs_CZ, cy, da, de, de_AT, de_CH, dv, el, en, en_AU, en_AU_ocker, en_BORK, en_CA, en_GB, en_GH, en_HK, en_IE, en_IN, en_NG, en_US, en_ZA, eo, es, es_MX, fa, fi, fr, fr_BE, fr_CA, fr_CH, fr_LU, fr_SN, generateMersenne32Randomizer, generateMersenne53Randomizer, he, hr, hu, hy, id_ID, it, ja, ka_GE, ko, lv, mergeLocales, mk, nb_NO, ne, nl, nl_BE, pl, pt_BR, pt_PT, ro, ro_MD, ru, sk, sr_RS_latin, sv, ta_IN, th, tr, uk, ur, uz_UZ_latin, vi, yo_NG, zh_CN, zh_TW, zu_ZA };
