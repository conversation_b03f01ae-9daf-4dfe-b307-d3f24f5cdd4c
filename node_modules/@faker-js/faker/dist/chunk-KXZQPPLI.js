import{a as i}from"./chunk-KERBADJJ.js";import{n as a,o as t}from"./chunk-PC2QB7VM.js";var r=["Group","Limited","Ltd","Pvt Ltd"];var b={legal_entity_type:r},e=b;var o=["com","info","net","np","org"];var n=["gmail.com","hotmail.com","worldlink.com.np","yahoo.com"];var N={domain_suffix:o,free_email:n},m=N;var p=["Bhaktapur","Biratnagar","Birendranagar","Birgunj","Butwal","Damak","Dharan","Gaur","Gorkha","He<PERSON><PERSON>","Itahari","Janakpur","Kathmandu","Lahan","Nepalgunj","Pokhara"];var h=["{{location.city_name}}"];var u=["1####","2####","3####","4####","5####"];var l=["Baglung","Banke","Bara","Bardiya","Bhaktapur","Bhoju<PERSON>","Chit<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>g","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Do<PERSON><PERSON>","Do<PERSON><PERSON>","<PERSON>rk<PERSON>","<PERSON>ul<PERSON>","<PERSON>m<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>rk<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>m<PERSON>","<PERSON>b<PERSON><PERSON><PERSON><PERSON>k","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Lamjung","Manang","Mohottari","Morang","Mugu","Mustang","Myagdi","Nawalparasi","Nuwakot","Palpa","Parbat","Parsa","Ramechhap","Rauswa","Rautahat","Rolpa","Rupandehi","Sankhuwasabha","Sarlahi","Sindhuli","Sindhupalchok","Sunsari","Surket","Syangja","Tanahu","Terhathum"];var f=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}"];var R={city_name:p,city_pattern:h,postcode:u,state:l,street_pattern:f},s=R;var A={title:"Nepali",code:"ne",language:"ne",endonym:"\u0928\u0947\u092A\u093E\u0932\u0940",dir:"ltr",script:"Deva"},d=A;var S={generic:["Aarav","Ajita","Amit","Amita","Amrit","Arijit","Ashmi","Asmita","Bibek","Bijay","Bikash","Bina","Bishal","Bishnu","Buddha","Deepika","Dipendra","Gagan","Ganesh","Khem","Krishna","Laxmi","Manisha","Nabin","Nikita","Niraj","Nischal","Padam","Pooja","Prabin","Prakash","Prashant","Prem","Purna","Rajendra","Rajina","Raju","Rakesh","Ranjan","Ratna","Sagar","Sandeep","Sanjay","Santosh","Sarita","Shilpa","Shirisha","Shristi","Siddhartha","Subash","Sumeet","Sunita","Suraj","Susan","Sushant"],female:["Ajita","Amita","Ashmi","Asmita","Bina","Deepika","Laxmi","Manisha","Nikita","Pooja","Rajina","Ratna","Sarita","Shilpa","Shirisha","Shristi","Sunita","Susan"],male:["Aarav","Amit","Amrit","Arijit","Bibek","Bijay","Bikash","Bishal","Bishnu","Buddha","Dipendra","Gagan","Ganesh","Khem","Krishna","Nabin","Niraj","Nischal","Padam","Prabin","Prakash","Prashant","Prem","Purna","Rajendra","Raju","Rakesh","Ranjan","Sagar","Sandeep","Sanjay","Santosh","Siddhartha","Subash","Sumeet","Suraj","Sushant"]};var c={generic:["Adhikari","Aryal","Baral","Basnet","Bastola","Basynat","Bhandari","Bhattarai","Chettri","Devkota","Dhakal","Dongol","Ghale","Gurung","Gyawali","Hamal","Jung","KC","Kafle","Karki","Khadka","Koirala","Lama","Limbu","Magar","Maharjan","Niroula","Pandey","Pradhan","Rana","Raut","Sai","Shai","Shakya","Sherpa","Shrestha","Subedi","Tamang","Thapa"]};var k={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var _={first_name:S,last_name:c,last_name_pattern:k},g=_;var B=["##-#######","+977-#-#######","+977########"];var D=["+977#########","+977########"];var y=["#########","0##-######"];var K={human:B,international:D,national:y},j=K;var L={format:j},P=L;var G={company:e,internet:m,location:s,metadata:d,person:g,phone_number:P},x=G;var Na=new a({locale:[x,i,t]});export{x as a,Na as b};
