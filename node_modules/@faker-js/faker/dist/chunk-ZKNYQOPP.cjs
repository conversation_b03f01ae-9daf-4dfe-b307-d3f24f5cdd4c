"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } } function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; } var _class; var _class2;var m=class extends Error{};function Ce(i){let e=Object.getPrototypeOf(i);do{for(let r of Object.getOwnPropertyNames(e))typeof i[r]=="function"&&r!=="constructor"&&(i[r]=i[r].bind(i));e=Object.getPrototypeOf(e)}while(e!==Object.prototype)}var x=class{constructor(e){this.faker=e;Ce(this)}},p=class extends x{constructor(r){super(r);this.faker=r}};var Ne=(t=>(t.Narrowbody="narrowbody",t.Regional="regional",t.Widebody="widebody",t))(Ne||{}),kr=["0","1","2","3","4","5","6","7","8","9"],xr=["0","O","1","I","L"],Ar={regional:20,narrowbody:35,widebody:60},Er={regional:["A","B","C","D"],narrowbody:["A","B","C","D","E","F"],widebody:["A","B","C","D","E","F","G","H","J","K"]},F=class extends p{airport(){return this.faker.helpers.arrayElement(this.faker.definitions.airline.airport)}airline(){return this.faker.helpers.arrayElement(this.faker.definitions.airline.airline)}airplane(){return this.faker.helpers.arrayElement(this.faker.definitions.airline.airplane)}recordLocator(e={}){let{allowNumerics:r=!1,allowVisuallySimilarCharacters:t=!1}=e,a=[];return r||a.push(...kr),t||a.push(...xr),this.faker.string.alphanumeric({length:6,casing:"upper",exclude:a})}seat(e={}){let{aircraftType:r="narrowbody"}=e,t=Ar[r],a=Er[r],n=this.faker.number.int({min:1,max:t}),o=this.faker.helpers.arrayElement(a);return`${n}${o}`}aircraftType(){return this.faker.helpers.enumValue(Ne)}flightNumber(e={}){let{length:r={min:1,max:4},addLeadingZeros:t=!1}=e,a=this.faker.string.numeric({length:r,allowLeadingZeros:!1});return t?a.padStart(4,"0"):a}};var De=(n=>(n.SRGB="sRGB",n.DisplayP3="display-p3",n.REC2020="rec2020",n.A98RGB="a98-rgb",n.ProphotoRGB="prophoto-rgb",n))(De||{}),Re= exports.d =(c=>(c.RGB="rgb",c.RGBA="rgba",c.HSL="hsl",c.HSLA="hsla",c.HWB="hwb",c.CMYK="cmyk",c.LAB="lab",c.LCH="lch",c.COLOR="color",c))(Re||{});function wr(i,e){let{prefix:r,casing:t}=e;switch(t){case"upper":{i=i.toUpperCase();break}case"lower":{i=i.toLowerCase();break}case"mixed":}return r&&(i=r+i),i}function Le(i){return i.map(r=>{if(r%1!==0){let a=new ArrayBuffer(4);new DataView(a).setFloat32(0,r);let n=new Uint8Array(a);return Le([...n]).replaceAll(" ","")}return(r>>>0).toString(2).padStart(8,"0")}).join(" ")}function A(i){return Math.round(i*100)}function Sr(i,e="rgb",r="sRGB"){switch(e){case"rgba":return`rgba(${i[0]}, ${i[1]}, ${i[2]}, ${i[3]})`;case"color":return`color(${r} ${i[0]} ${i[1]} ${i[2]})`;case"cmyk":return`cmyk(${A(i[0])}%, ${A(i[1])}%, ${A(i[2])}%, ${A(i[3])}%)`;case"hsl":return`hsl(${i[0]}deg ${A(i[1])}% ${A(i[2])}%)`;case"hsla":return`hsl(${i[0]}deg ${A(i[1])}% ${A(i[2])}% / ${A(i[3])})`;case"hwb":return`hwb(${i[0]} ${A(i[1])}% ${A(i[2])}%)`;case"lab":return`lab(${A(i[0])}% ${i[1]} ${i[2]})`;case"lch":return`lch(${A(i[0])}% ${i[1]} ${i[2]})`;case"rgb":return`rgb(${i[0]}, ${i[1]}, ${i[2]})`}}function D(i,e,r="rgb",t="sRGB"){switch(e){case"css":return Sr(i,r,t);case"binary":return Le(i);case"decimal":return i}}var G=class extends p{human(){return this.faker.helpers.arrayElement(this.faker.definitions.color.human)}space(){return this.faker.helpers.arrayElement(this.faker.definitions.color.space)}cssSupportedFunction(){return this.faker.helpers.enumValue(Re)}cssSupportedSpace(){return this.faker.helpers.enumValue(De)}rgb(e={}){let{format:r="hex",includeAlpha:t=!1,prefix:a="#",casing:n="lower"}=e,o,s="rgb";return r==="hex"?(o=this.faker.string.hexadecimal({length:t?8:6,prefix:""}),o=wr(o,{prefix:a,casing:n}),o):(o=Array.from({length:3},()=>this.faker.number.int(255)),t&&(o.push(this.faker.number.float({multipleOf:.01})),s="rgba"),D(o,r,s))}cmyk(e={}){let{format:r="decimal"}=e,t=Array.from({length:4},()=>this.faker.number.float({multipleOf:.01}));return D(t,r,"cmyk")}hsl(e={}){let{format:r="decimal",includeAlpha:t=!1}=e,a=[this.faker.number.int(360)];for(let n=0;n<(_optionalChain([e, 'optionalAccess', _2 => _2.includeAlpha])?3:2);n++)a.push(this.faker.number.float({multipleOf:.01}));return D(a,r,t?"hsla":"hsl")}hwb(e={}){let{format:r="decimal"}=e,t=[this.faker.number.int(360)];for(let a=0;a<2;a++)t.push(this.faker.number.float({multipleOf:.01}));return D(t,r,"hwb")}lab(e={}){let{format:r="decimal"}=e,t=[this.faker.number.float({multipleOf:1e-6})];for(let a=0;a<2;a++)t.push(this.faker.number.float({min:-100,max:100,multipleOf:1e-4}));return D(t,r,"lab")}lch(e={}){let{format:r="decimal"}=e,t=[this.faker.number.float({multipleOf:1e-6})];for(let a=0;a<2;a++)t.push(this.faker.number.float({max:230,multipleOf:.1}));return D(t,r,"lch")}colorByCSSColorSpace(e={}){let{format:r="decimal",space:t="sRGB"}=e,a=Array.from({length:3},()=>this.faker.number.float({multipleOf:1e-4}));return D(a,r,"color",t)}};var be=(a=>(a.Legacy="legacy",a.Segwit="segwit",a.Bech32="bech32",a.Taproot="taproot",a))(be||{}),Pe= exports.f =(r=>(r.Mainnet="mainnet",r.Testnet="testnet",r))(Pe||{}),Be={legacy:{prefix:{mainnet:"1",testnet:"m"},length:{min:26,max:34},casing:"mixed",exclude:"0OIl"},segwit:{prefix:{mainnet:"3",testnet:"2"},length:{min:26,max:34},casing:"mixed",exclude:"0OIl"},bech32:{prefix:{mainnet:"bc1",testnet:"tb1"},length:{min:42,max:42},casing:"lower",exclude:"1bBiIoO"},taproot:{prefix:{mainnet:"bc1p",testnet:"tb1p"},length:{min:62,max:62},casing:"lower",exclude:"1bBiIoO"}};var de=typeof Buffer>"u"||!ve("base64")?i=>{let e=new TextEncoder().encode(i),r=Array.from(e,t=>String.fromCodePoint(t)).join("");return btoa(r)}:i=>Buffer.from(i).toString("base64"),ge=typeof Buffer>"u"||!ve("base64url")?i=>de(i).replaceAll("+","-").replaceAll("/","_").replaceAll(/=+$/g,""):i=>Buffer.from(i).toString("base64url");function ve(i){try{return typeof Buffer.from("test").toString(i)=="string"}catch (e2){return!1}}function w(i){let{deprecated:e,since:r,until:t,proposed:a}=i,n=`[@faker-js/faker]: ${e} is deprecated`;r&&(n+=` since v${r}`),t&&(n+=` and will be removed in v${t}`),a&&(n+=`. Please use ${a} instead`),console.warn(`${n}.`)}var Mr=Object.fromEntries([["\u0410","A"],["\u0430","a"],["\u0411","B"],["\u0431","b"],["\u0412","V"],["\u0432","v"],["\u0413","G"],["\u0433","g"],["\u0414","D"],["\u0434","d"],["\u044A\u0435","ye"],["\u042A\u0435","Ye"],["\u044A\u0415","yE"],["\u042A\u0415","YE"],["\u0415","E"],["\u0435","e"],["\u0401","Yo"],["\u0451","yo"],["\u0416","Zh"],["\u0436","zh"],["\u0417","Z"],["\u0437","z"],["\u0418","I"],["\u0438","i"],["\u044B\u0439","iy"],["\u042B\u0439","Iy"],["\u042B\u0419","IY"],["\u044B\u0419","iY"],["\u0419","Y"],["\u0439","y"],["\u041A","K"],["\u043A","k"],["\u041B","L"],["\u043B","l"],["\u041C","M"],["\u043C","m"],["\u041D","N"],["\u043D","n"],["\u041E","O"],["\u043E","o"],["\u041F","P"],["\u043F","p"],["\u0420","R"],["\u0440","r"],["\u0421","S"],["\u0441","s"],["\u0422","T"],["\u0442","t"],["\u0423","U"],["\u0443","u"],["\u0424","F"],["\u0444","f"],["\u0425","Kh"],["\u0445","kh"],["\u0426","Ts"],["\u0446","ts"],["\u0427","Ch"],["\u0447","ch"],["\u0428","Sh"],["\u0448","sh"],["\u0429","Sch"],["\u0449","sch"],["\u042A",""],["\u044A",""],["\u042B","Y"],["\u044B","y"],["\u042C",""],["\u044C",""],["\u042D","E"],["\u044D","e"],["\u042E","Yu"],["\u044E","yu"],["\u042F","Ya"],["\u044F","ya"]]),Tr=Object.fromEntries([["\u03B1","a"],["\u03B2","v"],["\u03B3","g"],["\u03B4","d"],["\u03B5","e"],["\u03B6","z"],["\u03B7","i"],["\u03B8","th"],["\u03B9","i"],["\u03BA","k"],["\u03BB","l"],["\u03BC","m"],["\u03BD","n"],["\u03BE","ks"],["\u03BF","o"],["\u03C0","p"],["\u03C1","r"],["\u03C3","s"],["\u03C4","t"],["\u03C5","y"],["\u03C6","f"],["\u03C7","x"],["\u03C8","ps"],["\u03C9","o"],["\u03AC","a"],["\u03AD","e"],["\u03AF","i"],["\u03CC","o"],["\u03CD","y"],["\u03AE","i"],["\u03CE","o"],["\u03C2","s"],["\u03CA","i"],["\u03B0","y"],["\u03CB","y"],["\u0390","i"],["\u0391","A"],["\u0392","B"],["\u0393","G"],["\u0394","D"],["\u0395","E"],["\u0396","Z"],["\u0397","I"],["\u0398","TH"],["\u0399","I"],["\u039A","K"],["\u039B","L"],["\u039C","M"],["\u039D","N"],["\u039E","KS"],["\u039F","O"],["\u03A0","P"],["\u03A1","R"],["\u03A3","S"],["\u03A4","T"],["\u03A5","Y"],["\u03A6","F"],["\u03A7","X"],["\u03A8","PS"],["\u03A9","O"],["\u0386","A"],["\u0388","E"],["\u038A","I"],["\u038C","O"],["\u038E","Y"],["\u0389","I"],["\u038F","O"],["\u03AA","I"],["\u03AB","Y"]]),Cr=Object.fromEntries([["\u0621","e"],["\u0622","a"],["\u0623","a"],["\u0624","w"],["\u0625","i"],["\u0626","y"],["\u0627","a"],["\u0628","b"],["\u0629","t"],["\u062A","t"],["\u062B","th"],["\u062C","j"],["\u062D","h"],["\u062E","kh"],["\u062F","d"],["\u0630","dh"],["\u0631","r"],["\u0632","z"],["\u0633","s"],["\u0634","sh"],["\u0635","s"],["\u0636","d"],["\u0637","t"],["\u0638","z"],["\u0639","e"],["\u063A","gh"],["\u0640","_"],["\u0641","f"],["\u0642","q"],["\u0643","k"],["\u0644","l"],["\u0645","m"],["\u0646","n"],["\u0647","h"],["\u0648","w"],["\u0649","a"],["\u064A","y"],["\u064E\u200E","a"],["\u064F","u"],["\u0650\u200E","i"]]),Nr=Object.fromEntries([["\u0561","a"],["\u0531","A"],["\u0562","b"],["\u0532","B"],["\u0563","g"],["\u0533","G"],["\u0564","d"],["\u0534","D"],["\u0565","ye"],["\u0535","Ye"],["\u0566","z"],["\u0536","Z"],["\u0567","e"],["\u0537","E"],["\u0568","y"],["\u0538","Y"],["\u0569","t"],["\u0539","T"],["\u056A","zh"],["\u053A","Zh"],["\u056B","i"],["\u053B","I"],["\u056C","l"],["\u053C","L"],["\u056D","kh"],["\u053D","Kh"],["\u056E","ts"],["\u053E","Ts"],["\u056F","k"],["\u053F","K"],["\u0570","h"],["\u0540","H"],["\u0571","dz"],["\u0541","Dz"],["\u0572","gh"],["\u0542","Gh"],["\u0573","tch"],["\u0543","Tch"],["\u0574","m"],["\u0544","M"],["\u0575","y"],["\u0545","Y"],["\u0576","n"],["\u0546","N"],["\u0577","sh"],["\u0547","Sh"],["\u0578","vo"],["\u0548","Vo"],["\u0579","ch"],["\u0549","Ch"],["\u057A","p"],["\u054A","P"],["\u057B","j"],["\u054B","J"],["\u057C","r"],["\u054C","R"],["\u057D","s"],["\u054D","S"],["\u057E","v"],["\u054E","V"],["\u057F","t"],["\u054F","T"],["\u0580","r"],["\u0550","R"],["\u0581","c"],["\u0551","C"],["\u0578\u0582","u"],["\u0548\u0552","U"],["\u0548\u0582","U"],["\u0583","p"],["\u0553","P"],["\u0584","q"],["\u0554","Q"],["\u0585","o"],["\u0555","O"],["\u0586","f"],["\u0556","F"],["\u0587","yev"]]),Dr=Object.fromEntries([["\u0686","ch"],["\u06A9","k"],["\u06AF","g"],["\u067E","p"],["\u0698","zh"],["\u06CC","y"]]),Rr=Object.fromEntries([["\u05D0","a"],["\u05D1","b"],["\u05D2","g"],["\u05D3","d"],["\u05D4","h"],["\u05D5","v"],["\u05D6","z"],["\u05D7","ch"],["\u05D8","t"],["\u05D9","y"],["\u05DB","k"],["\u05DA","kh"],["\u05DC","l"],["\u05DD","m"],["\u05DE","m"],["\u05DF","n"],["\u05E0","n"],["\u05E1","s"],["\u05E2","a"],["\u05E4","f"],["\u05E3","ph"],["\u05E6","ts"],["\u05E5","ts"],["\u05E7","k"],["\u05E8","r"],["\u05E9","sh"],["\u05EA","t"],["\u05D5","v"]]),ye={...Mr,...Tr,...Cr,...Dr,...Nr,...Rr};var Lr=(u=>(u.Any="any",u.Loopback="loopback",u.PrivateA="private-a",u.PrivateB="private-b",u.PrivateC="private-c",u.TestNet1="test-net-1",u.TestNet2="test-net-2",u.TestNet3="test-net-3",u.LinkLocal="link-local",u.Multicast="multicast",u))(Lr||{}),Pr={any:"0.0.0.0/0",loopback:"127.0.0.0/8","private-a":"10.0.0.0/8","private-b":"172.16.0.0/12","private-c":"192.168.0.0/16","test-net-1":"192.0.2.0/24","test-net-2":"198.51.100.0/24","test-net-3":"203.0.113.0/24","link-local":"169.254.0.0/16",multicast:"224.0.0.0/4"};function $e(i){return/^[a-z][a-z-]*[a-z]$/i.exec(i)!==null}function Ie(i,e){let r=i.helpers.slugify(e);if($e(r))return r;let t=i.helpers.slugify(i.lorem.word());return $e(t)?t:i.string.alpha({casing:"lower",length:i.number.int({min:4,max:8})})}function ke(i,e){return Math.floor((i.number.int(256)+e)/2).toString(16).padStart(2,"0")}var O=class extends p{email(e={}){let{firstName:r,lastName:t,provider:a=this.faker.helpers.arrayElement(this.faker.definitions.internet.free_email),allowSpecialCharacters:n=!1}=e,o=this.username({firstName:r,lastName:t});if(o=o.replaceAll(/[^A-Za-z0-9._+-]+/g,""),o=o.substring(0,50),n){let s=[..."._-"],l=[...".!#$%&'*+-/=?^_`{|}~"];o=o.replace(this.faker.helpers.arrayElement(s),this.faker.helpers.arrayElement(l))}return o=o.replaceAll(/\.{2,}/g,"."),o=o.replace(/^\./,""),o=o.replace(/\.$/,""),`${o}@${a}`}exampleEmail(e={}){let{firstName:r,lastName:t,allowSpecialCharacters:a=!1}=e,n=this.faker.helpers.arrayElement(this.faker.definitions.internet.example_email);return this.email({firstName:r,lastName:t,provider:n,allowSpecialCharacters:a})}userName(e={}){return w({deprecated:"faker.internet.userName()",proposed:"faker.internet.username()",since:"9.1.0",until:"10.0.0"}),this.username(e)}username(e={}){let{firstName:r=this.faker.person.firstName(),lastName:t=this.faker.person.lastName(),lastName:a}=e,n=this.faker.helpers.arrayElement([".","_"]),o=this.faker.number.int(99),s=[()=>`${r}${n}${t}${o}`,()=>`${r}${n}${t}`];a||s.push(()=>`${r}${o}`);let l=this.faker.helpers.arrayElement(s)();return l=l.normalize("NFKD").replaceAll(/[\u0300-\u036F]/g,""),l=[...l].map(c=>{if(ye[c])return ye[c];let u=_nullishCoalesce(c.codePointAt(0), () => (Number.NaN));return u<128?c:u.toString(36)}).join(""),l=l.toString().replaceAll("'",""),l=l.replaceAll(" ",""),l}displayName(e={}){let{firstName:r=this.faker.person.firstName(),lastName:t=this.faker.person.lastName()}=e,a=this.faker.helpers.arrayElement([".","_"]),n=this.faker.number.int(99),o=[()=>`${r}${n}`,()=>`${r}${a}${t}`,()=>`${r}${a}${t}${n}`],s=this.faker.helpers.arrayElement(o)();return s=s.toString().replaceAll("'",""),s=s.replaceAll(" ",""),s}protocol(){let e=["http","https"];return this.faker.helpers.arrayElement(e)}httpMethod(){let e=["GET","POST","PUT","DELETE","PATCH"];return this.faker.helpers.arrayElement(e)}httpStatusCode(e={}){let{types:r=Object.keys(this.faker.definitions.internet.http_status_code)}=e,t=this.faker.helpers.arrayElement(r);return this.faker.helpers.arrayElement(this.faker.definitions.internet.http_status_code[t])}url(e={}){let{appendSlash:r=this.faker.datatype.boolean(),protocol:t="https"}=e;return`${t}://${this.domainName()}${r?"/":""}`}domainName(){return`${this.domainWord()}.${this.domainSuffix()}`}domainSuffix(){return this.faker.helpers.arrayElement(this.faker.definitions.internet.domain_suffix)}domainWord(){let e=Ie(this.faker,this.faker.word.adjective()),r=Ie(this.faker,this.faker.word.noun());return`${e}-${r}`.toLowerCase()}ip(){return this.faker.datatype.boolean()?this.ipv4():this.ipv6()}ipv4(e={}){let{network:r="any",cidrBlock:t=Pr[r]}=e;if(!/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,2}$/.test(t))throw new m(`Invalid CIDR block provided: ${t}. Must be in the format x.x.x.x/y.`);let[a,n]=t.split("/"),o=4294967295>>>Number.parseInt(n),[s,l,c,u]=a.split(".").map(Number),f=(s<<24|l<<16|c<<8|u)&~o,g=this.faker.number.int(o),b=f|g;return[b>>>24&255,b>>>16&255,b>>>8&255,b&255].join(".")}ipv6(){return Array.from({length:8},()=>this.faker.string.hexadecimal({length:4,casing:"lower",prefix:""})).join(":")}port(){return this.faker.number.int(65535)}userAgent(){return this.faker.helpers.fake(this.faker.definitions.internet.user_agent_pattern)}color(e={}){w({deprecated:"faker.internet.color()",proposed:"faker.color.rgb()",since:"9.6.0",until:"10.0.0"});let{redBase:r=0,greenBase:t=0,blueBase:a=0}=e,n=ke(this.faker,r),o=ke(this.faker,t),s=ke(this.faker,a);return`#${n}${o}${s}`}mac(e={}){typeof e=="string"&&(e={separator:e});let{separator:r=":"}=e,t,a="";for([":","-",""].includes(r)||(r=":"),t=0;t<12;t++)a+=this.faker.number.hex(15),t%2===1&&t!==11&&(a+=r);return a}password(e={}){let r=/[aeiouAEIOU]$/,t=/[bcdfghjklmnpqrstvwxyzBCDFGHJKLMNPQRSTVWXYZ]$/,a=(c,u,h,f)=>{if(f.length>=c)return f;u&&(h=t.test(f)?r:t);let g=this.faker.number.int(94)+33,b=String.fromCodePoint(g);return u&&(b=b.toLowerCase()),h.test(b)?a(c,u,h,f+b):a(c,u,h,f)},{length:n=15,memorable:o=!1,pattern:s=/\w/,prefix:l=""}=e;return a(n,o,s,l)}emoji(e={}){let{types:r=Object.keys(this.faker.definitions.internet.emoji)}=e,t=this.faker.helpers.arrayElement(r);return this.faker.helpers.arrayElement(this.faker.definitions.internet.emoji[t])}jwtAlgorithm(){return this.faker.helpers.arrayElement(this.faker.definitions.internet.jwt_algorithm)}jwt(e={}){let{refDate:r=this.faker.defaultRefDate()}=e,t=this.faker.date.recent({refDate:r}),{header:a={alg:this.jwtAlgorithm(),typ:"JWT"},payload:n={iat:Math.round(t.valueOf()/1e3),exp:Math.round(this.faker.date.soon({refDate:t}).valueOf()/1e3),nbf:Math.round(this.faker.date.anytime({refDate:r}).valueOf()/1e3),iss:this.faker.company.name(),sub:this.faker.string.uuid(),aud:this.faker.string.uuid(),jti:this.faker.string.uuid()}}=e,o=ge(JSON.stringify(a)),s=ge(JSON.stringify(n)),l=this.faker.string.alphanumeric(64);return`${o}.${s}.${l}`}};var _e=(r=>(r.Female="female",r.Male="male",r))(_e||{});function R(i,e,r){let{generic:t,female:a,male:n}=r;switch(e){case"female":return _nullishCoalesce(a, () => (t));case"male":return _nullishCoalesce(n, () => (t));default:return _nullishCoalesce(_nullishCoalesce(t, () => (i.helpers.arrayElement([a,n]))), () => ([]))}}var U=class extends p{firstName(e){return this.faker.helpers.arrayElement(R(this.faker,e,this.faker.definitions.person.first_name))}lastName(e){if(_optionalChain([this, 'access', _3 => _3.faker, 'access', _4 => _4.rawDefinitions, 'access', _5 => _5.person, 'optionalAccess', _6 => _6.last_name_pattern])!=null){let r=this.faker.helpers.weightedArrayElement(R(this.faker,e,this.faker.rawDefinitions.person.last_name_pattern));return this.faker.helpers.fake(r)}return this.faker.helpers.arrayElement(R(this.faker,e,this.faker.definitions.person.last_name))}middleName(e){return this.faker.helpers.arrayElement(R(this.faker,e,this.faker.definitions.person.middle_name))}fullName(e={}){let{sex:r=this.faker.helpers.arrayElement(["female","male"]),firstName:t=this.firstName(r),lastName:a=this.lastName(r)}=e,n=this.faker.helpers.weightedArrayElement(this.faker.definitions.person.name);return this.faker.helpers.mustache(n,{"person.prefix":()=>this.prefix(r),"person.firstName":()=>t,"person.middleName":()=>this.middleName(r),"person.lastName":()=>a,"person.suffix":()=>this.suffix()})}gender(){return this.faker.helpers.arrayElement(this.faker.definitions.person.gender)}sex(){return this.faker.helpers.arrayElement(this.faker.definitions.person.sex)}sexType(){return this.faker.helpers.enumValue(_e)}bio(){let{bio_pattern:e}=this.faker.definitions.person;return this.faker.helpers.fake(e)}prefix(e){return this.faker.helpers.arrayElement(R(this.faker,e,this.faker.definitions.person.prefix))}suffix(){return this.faker.helpers.arrayElement(this.faker.definitions.person.suffix)}jobTitle(){return this.faker.helpers.fake(this.faker.definitions.person.job_title_pattern)}jobDescriptor(){return this.faker.helpers.arrayElement(this.faker.definitions.person.job_descriptor)}jobArea(){return this.faker.helpers.arrayElement(this.faker.definitions.person.job_area)}jobType(){return this.faker.helpers.arrayElement(this.faker.definitions.person.job_type)}zodiacSign(){return this.faker.helpers.arrayElement(this.faker.definitions.person.western_zodiac_sign)}};var Br=23283064365386963e-26,vr=1/9007199254740992,{imul:Ae,trunc:Ee}=Math;function Fe(i){return typeof i=="number"?Ge(i):$r(i)}function Ge(i){let e=Array.from({length:624});e[0]=i;for(let r=1;r!==624;++r){let t=e[r-1]^e[r-1]>>>30;e[r]=Ee(Ae(1812433253,t)+r)}return e}function $r(i){let e=Ge(19650218),r=1,t=0;for(let a=Math.max(624,i.length);a!==0;--a){let n=e[r-1]^e[r-1]>>>30;e[r]=Ee((e[r]^Ae(n,1664525))+i[t]+t),r++,t++,r>=624&&(e[0]=e[623],r=1),t>=i.length&&(t=0)}for(let a=623;a!==0;a--)e[r]=Ee((e[r]^Ae(e[r-1]^e[r-1]>>>30,1566083941))-r),r++,r>=624&&(e[0]=e[623],r=1);return e[0]=2147483648,e}function xe(i){for(let r=0;r!==227;++r){let t=(i[r]&2147483648)+(i[r+1]&2147483647);i[r]=i[r+397]^t>>>1^-(t&1)&2567483615}for(let r=227;r!==623;++r){let t=(i[r]&2147483648)+(i[r+1]&2147483647);i[r]=i[r+397-624]^t>>>1^-(t&1)&2567483615}let e=(i[623]&2147483648)+(i[0]&2147483647);return i[623]=i[396]^e>>>1^-(e&1)&2567483615,i}var L=class{constructor(e=Math.random()*Number.MAX_SAFE_INTEGER,r=xe(Fe(e)),t=0){this.states=r;this.index=t}nextU32(){let e=this.states[this.index];return e^=this.states[this.index]>>>11,e^=e<<7&2636928640,e^=e<<15&4022730752,e^=e>>>18,++this.index>=624&&(this.states=xe(this.states),this.index=0),e>>>0}nextF32(){return this.nextU32()*Br}nextU53(){let e=this.nextU32()>>>5,r=this.nextU32()>>>6;return e*67108864+r}nextF53(){return this.nextU53()*vr}seed(e){this.states=xe(Fe(e)),this.index=0}};function P(){return Math.ceil(Math.random()*Number.MAX_SAFE_INTEGER)}function Lt(i=P()){let e=new L(i);return{next(){return e.nextF32()},seed(r){e.seed(r)}}}function Oe(i=P()){let e=new L(i);return{next(){return e.nextF53()},seed(r){e.seed(r)}}}var K=class extends x{boolean(e={}){typeof e=="number"&&(e={probability:e});let{probability:r=.5}=e;return r<=0?!1:r>=1?!0:this.faker.number.float()<r}};function S(i,e="refDate"){let r=new Date(i);if(Number.isNaN(r.valueOf()))throw new m(`Invalid ${e} date: ${i.toString()}`);return r}var j=()=>{throw new m("You cannot edit the locale data on the faker instance")};function Ue(i){let e={};return new Proxy(i,{has(){return!0},get(r,t){return typeof t=="symbol"||t==="nodeType"?r[t]:t in e?e[t]:e[t]=Ir(t,r[t])},set:j,deleteProperty:j})}function H(i,...e){if(i===null)throw new m(`The locale data for '${e.join(".")}' aren't applicable to this locale.
  If you think this is a bug, please report it at: https://github.com/faker-js/faker`);if(i===void 0)throw new m(`The locale data for '${e.join(".")}' are missing in this locale.
  If this is a custom Faker instance, please make sure all required locales are used e.g. '[de_AT, de, en, base]'.
  Please contribute the missing data to the project or use a locale/Faker instance that has these data.
  For more information see https://fakerjs.dev/guide/localization.html`)}function Ir(i,e={}){return new Proxy(e,{has(r,t){return r[t]!=null},get(r,t){let a=r[t];return typeof t=="symbol"||t==="nodeType"||H(a,i,t.toString()),a},set:j,deleteProperty:j})}var B=class extends x{anytime(e={}){let{refDate:r=this.faker.defaultRefDate()}=e,t=S(r).getTime();return this.between({from:t-1e3*60*60*24*365,to:t+1e3*60*60*24*365})}past(e={}){let{years:r=1,refDate:t=this.faker.defaultRefDate()}=e;if(r<=0)throw new m("Years must be greater than 0.");let a=S(t).getTime();return this.between({from:a-r*365*24*3600*1e3,to:a-1e3})}future(e={}){let{years:r=1,refDate:t=this.faker.defaultRefDate()}=e;if(r<=0)throw new m("Years must be greater than 0.");let a=S(t).getTime();return this.between({from:a+1e3,to:a+r*365*24*3600*1e3})}between(e){if(e==null||e.from==null||e.to==null)throw new m("Must pass an options object with `from` and `to` values.");let{from:r,to:t}=e,a=S(r,"from").getTime(),n=S(t,"to").getTime();if(a>n)throw new m("`from` date must be before `to` date.");return new Date(this.faker.number.int({min:a,max:n}))}betweens(e){if(e==null||e.from==null||e.to==null)throw new m("Must pass an options object with `from` and `to` values.");let{from:r,to:t,count:a=3}=e;return this.faker.helpers.multiple(()=>this.between({from:r,to:t}),{count:a}).sort((n,o)=>n.getTime()-o.getTime())}recent(e={}){let{days:r=1,refDate:t=this.faker.defaultRefDate()}=e;if(r<=0)throw new m("Days must be greater than 0.");let a=S(t).getTime();return this.between({from:a-r*24*3600*1e3,to:a-1e3})}soon(e={}){let{days:r=1,refDate:t=this.faker.defaultRefDate()}=e;if(r<=0)throw new m("Days must be greater than 0.");let a=S(t).getTime();return this.between({from:a+1e3,to:a+r*24*3600*1e3})}birthdate(e={}){let{mode:r="age",min:t=18,max:a=80,refDate:n=this.faker.defaultRefDate(),mode:o,min:s,max:l}=e;if([s,l,o].filter(f=>f!=null).length%3!==0)throw new m("The 'min', 'max', and 'mode' options must be set together.");let u=S(n),h=u.getUTCFullYear();switch(r){case"age":{let g=new Date(u).setUTCFullYear(h-a-1)+864e5,b=new Date(u).setUTCFullYear(h-t);if(g>b)throw new m(`Max age ${a} should be greater than or equal to min age ${t}.`);return this.between({from:g,to:b})}case"year":{let f=new Date(Date.UTC(0,0,2)).setUTCFullYear(t),g=new Date(Date.UTC(0,11,30)).setUTCFullYear(a);if(f>g)throw new m(`Max year ${a} should be greater than or equal to min year ${t}.`);return this.between({from:f,to:g})}}}},V=class extends B{constructor(r){super(r);this.faker=r}month(r={}){let{abbreviated:t=!1,context:a=!1}=r,n=this.faker.definitions.date.month,o;t?o=a&&n.abbr_context!=null?"abbr_context":"abbr":o=a&&n.wide_context!=null?"wide_context":"wide";let s=n[o];return H(s,"date.month",o),this.faker.helpers.arrayElement(s)}weekday(r={}){let{abbreviated:t=!1,context:a=!1}=r,n=this.faker.definitions.date.weekday,o;t?o=a&&n.abbr_context!=null?"abbr_context":"abbr":o=a&&n.wide_context!=null?"wide_context":"wide";let s=n[o];return H(s,"date.weekday",o),this.faker.helpers.arrayElement(s)}timeZone(){return this.faker.helpers.arrayElement(this.faker.definitions.date.time_zone)}};var _r=/\.|\(/;function Ke(i,e,r=[e,e.rawDefinitions]){if(i.length===0)throw new m("Eval expression cannot be empty.");if(r.length===0)throw new m("Eval entrypoints cannot be empty.");let t=r,a=i;do{let o;a.startsWith("(")?[o,t]=Fr(a,t,i):[o,t]=Or(a,t),a=a.substring(o),t=t.filter(s=>s!=null).map(s=>Array.isArray(s)?e.helpers.arrayElement(s):s)}while(a.length>0&&t.length>0);if(t.length===0)throw new m(`Cannot resolve expression '${i}'`);let n=t[0];return typeof n=="function"?n():n}function Fr(i,e,r){let[t,a]=Gr(i),n=i[t+1];switch(n){case".":case"(":case void 0:break;default:throw new m(`Expected dot ('.'), open parenthesis ('('), or nothing after function call but got '${n}'`)}return[t+(n==="."?2:1),e.map(o=>typeof o=="function"?o(...a):(console.warn(`[@faker-js/faker]: Invoking expressions which are not functions is deprecated since v9.0 and will be removed in v10.0.
Please remove the parentheses or replace the expression with an actual function.
${r}
${" ".repeat(r.length-i.length)}^`),o))]}function Gr(i){let e=i.indexOf(")",1);if(e===-1)throw new m(`Missing closing parenthesis in '${i}'`);for(;e!==-1;){let t=i.substring(1,e);try{return[e,JSON.parse(`[${t}]`)]}catch (e3){if(!t.includes("'")&&!t.includes('"'))try{return[e,JSON.parse(`["${t}"]`)]}catch (e4){}}e=i.indexOf(")",e+1)}e=i.lastIndexOf(")");let r=i.substring(1,e);return[e,[r]]}function Or(i,e){let r=_r.exec(i),t=(_nullishCoalesce(_optionalChain([r, 'optionalAccess', _7 => _7[0]]), () => ("")))===".",a=_nullishCoalesce(_optionalChain([r, 'optionalAccess', _8 => _8.index]), () => (i.length)),n=i.substring(0,a);if(n.length===0)throw new m(`Expression parts cannot be empty in '${i}'`);let o=i[a+1];if(t&&(o==null||o==="."||o==="("))throw new m(`Found dot without property name in '${i}'`);return[a+(t?1:0),e.map(s=>Ur(s,n))]}function Ur(i,e){switch(typeof i){case"function":{try{i=i()}catch (e5){return}return _optionalChain([i, 'optionalAccess', _9 => _9[e]])}case"object":return _optionalChain([i, 'optionalAccess', _10 => _10[e]]);default:return}}function je(i){let e=Kr(i.replace(/L?$/,"0"));return e===0?0:10-e}function Kr(i){i=i.replaceAll(/[\s-]/g,"");let e=0,r=!1;for(let t=i.length-1;t>=0;t--){let a=Number.parseInt(i[t]);r&&(a*=2,a>9&&(a=a%10+1)),e+=a,r=!r}return e%10}function He(i,e,r,t){let a=1;if(e)switch(e){case"?":{a=i.datatype.boolean()?0:1;break}case"*":{let n=1;for(;i.datatype.boolean();)n*=2;a=i.number.int({min:0,max:n});break}case"+":{let n=1;for(;i.datatype.boolean();)n*=2;a=i.number.int({min:1,max:n});break}default:throw new m("Unknown quantifier symbol provided.")}else r!=null&&t!=null?a=i.number.int({min:Number.parseInt(r),max:Number.parseInt(t)}):r!=null&&t==null&&(a=Number.parseInt(r));return a}function jr(i,e=""){let r=/(.)\{(\d+),(\d+)\}/,t=/(.)\{(\d+)\}/,a=/\[(\d+)-(\d+)\]/,n,o,s,l,c=r.exec(e);for(;c!=null;)n=Number.parseInt(c[2]),o=Number.parseInt(c[3]),n>o&&(s=o,o=n,n=s),l=i.number.int({min:n,max:o}),e=e.slice(0,c.index)+c[1].repeat(l)+e.slice(c.index+c[0].length),c=r.exec(e);for(c=t.exec(e);c!=null;)l=Number.parseInt(c[2]),e=e.slice(0,c.index)+c[1].repeat(l)+e.slice(c.index+c[0].length),c=t.exec(e);for(c=a.exec(e);c!=null;)n=Number.parseInt(c[1]),o=Number.parseInt(c[2]),n>o&&(s=o,o=n,n=s),e=e.slice(0,c.index)+i.number.int({min:n,max:o}).toString()+e.slice(c.index+c[0].length),c=a.exec(e);return e}function we(i,e="",r="#"){let t="";for(let a=0;a<e.length;a++)e.charAt(a)===r?t+=i.number.int(9):e.charAt(a)==="!"?t+=i.number.int({min:2,max:9}):t+=e.charAt(a);return t}var v=class extends x{slugify(e=""){return e.normalize("NFKD").replaceAll(/[\u0300-\u036F]/g,"").replaceAll(" ","-").replaceAll(/[^\w.-]+/g,"")}replaceSymbols(e=""){let r=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],t="";for(let a=0;a<e.length;a++)e.charAt(a)==="#"?t+=this.faker.number.int(9):e.charAt(a)==="?"?t+=this.arrayElement(r):e.charAt(a)==="*"?t+=this.faker.datatype.boolean()?this.arrayElement(r):this.faker.number.int(9):t+=e.charAt(a);return t}replaceCreditCardSymbols(e="6453-####-####-####-###L",r="#"){e=jr(this.faker,e),e=we(this.faker,e,r);let t=je(e);return e.replace("L",String(t))}fromRegExp(e){let r=!1;e instanceof RegExp&&(r=e.flags.includes("i"),e=e.toString(),e=_nullishCoalesce(_optionalChain([/\/(.+?)\//, 'access', _11 => _11.exec, 'call', _12 => _12(e), 'optionalAccess', _13 => _13[1]]), () => ("")));let t,a,n,o=/([.A-Za-z0-9])(?:\{(\d+)(?:,(\d+)|)\}|(\?|\*|\+))(?![^[]*]|[^{]*})/,s=o.exec(e);for(;s!=null;){let f=s[2],g=s[3],b=s[4];n=He(this.faker,b,f,g);let y;s[1]==="."?y=this.faker.string.alphanumeric(n):r?y=this.faker.string.fromCharacters([s[1].toLowerCase(),s[1].toUpperCase()],n):y=s[1].repeat(n),e=e.slice(0,s.index)+y+e.slice(s.index+s[0].length),s=o.exec(e)}let l=/(\d-\d|\w-\w|\d|\w|[-!@#$&()`.+,/"])/,c=/\[(\^|)(-|)(.+?)\](?:\{(\d+)(?:,(\d+)|)\}|(\?|\*|\+)|)/;for(s=c.exec(e);s!=null;){let f=s[1]==="^",g=s[2]==="-",b=s[4],y=s[5],N=s[6],k=[],_=s[3],C=l.exec(_);for(g&&k.push(45);C!=null;){if(C[0].includes("-")){let E=C[0].split("-").map(d=>_nullishCoalesce(d.codePointAt(0), () => (Number.NaN)));if(t=E[0],a=E[1],t>a)throw new m("Character range provided is out of order.");for(let d=t;d<=a;d++)if(r&&Number.isNaN(Number(String.fromCodePoint(d)))){let Te=String.fromCodePoint(d);k.push(_nullishCoalesce(Te.toUpperCase().codePointAt(0), () => (Number.NaN)),_nullishCoalesce(Te.toLowerCase().codePointAt(0), () => (Number.NaN)))}else k.push(d)}else r&&Number.isNaN(Number(C[0]))?k.push(_nullishCoalesce(C[0].toUpperCase().codePointAt(0), () => (Number.NaN)),_nullishCoalesce(C[0].toLowerCase().codePointAt(0), () => (Number.NaN))):k.push(_nullishCoalesce(C[0].codePointAt(0), () => (Number.NaN)));_=_.substring(C[0].length),C=l.exec(_)}if(n=He(this.faker,N,b,y),f){let E=-1;for(let d=48;d<=57;d++){if(E=k.indexOf(d),E>-1){k.splice(E,1);continue}k.push(d)}for(let d=65;d<=90;d++){if(E=k.indexOf(d),E>-1){k.splice(E,1);continue}k.push(d)}for(let d=97;d<=122;d++){if(E=k.indexOf(d),E>-1){k.splice(E,1);continue}k.push(d)}}let yr=this.multiple(()=>String.fromCodePoint(this.arrayElement(k)),{count:n}).join("");e=e.slice(0,s.index)+yr+e.slice(s.index+s[0].length),s=c.exec(e)}let u=/(.)\{(\d+),(\d+)\}/;for(s=u.exec(e);s!=null;){if(t=Number.parseInt(s[2]),a=Number.parseInt(s[3]),t>a)throw new m("Numbers out of order in {} quantifier.");n=this.faker.number.int({min:t,max:a}),e=e.slice(0,s.index)+s[1].repeat(n)+e.slice(s.index+s[0].length),s=u.exec(e)}let h=/(.)\{(\d+)\}/;for(s=h.exec(e);s!=null;)n=Number.parseInt(s[2]),e=e.slice(0,s.index)+s[1].repeat(n)+e.slice(s.index+s[0].length),s=h.exec(e);return e}shuffle(e,r={}){let{inplace:t=!1}=r;t||(e=[...e]);for(let a=e.length-1;a>0;--a){let n=this.faker.number.int(a);[e[a],e[n]]=[e[n],e[a]]}return e}uniqueArray(e,r){if(Array.isArray(e)){let n=[...new Set(e)];return this.shuffle(n).splice(0,r)}let t=new Set;try{if(typeof e=="function"){let a=1e3*r,n=0;for(;t.size<r&&n<a;)t.add(e()),n++}}catch (e6){}return[...t]}mustache(e,r){if(e==null)return"";for(let t in r){let a=new RegExp(`{{${t}}}`,"g"),n=r[t];typeof n=="string"&&(n=n.replaceAll("$","$$$$")),e=e.replace(a,n)}return e}maybe(e,r={}){if(this.faker.datatype.boolean(r))return e()}objectKey(e){let r=Object.keys(e);return this.arrayElement(r)}objectValue(e){let r=this.faker.helpers.objectKey(e);return e[r]}objectEntry(e){let r=this.faker.helpers.objectKey(e);return[r,e[r]]}arrayElement(e){if(e.length===0)throw new m("Cannot get value from empty dataset.");let r=e.length>1?this.faker.number.int({max:e.length-1}):0;return e[r]}weightedArrayElement(e){if(e.length===0)throw new m("weightedArrayElement expects an array with at least one element");if(!e.every(n=>n.weight>0))throw new m("weightedArrayElement expects an array of { weight, value } objects where weight is a positive number");let r=e.reduce((n,{weight:o})=>n+o,0),t=this.faker.number.float({min:0,max:r}),a=0;for(let{weight:n,value:o}of e)if(a+=n,t<a)return o;return e.at(-1).value}arrayElements(e,r){if(e.length===0)return[];let t=this.rangeToNumber(_nullishCoalesce(r, () => ({min:1,max:e.length})));if(t>=e.length)return this.shuffle(e);if(t<=0)return[];let a=[...e],n=e.length,o=n-t,s,l;for(;n-- >o;)l=this.faker.number.int(n),s=a[l],a[l]=a[n],a[n]=s;return a.slice(o)}enumValue(e){let r=Object.keys(e).filter(a=>Number.isNaN(Number(a))),t=this.arrayElement(r);return e[t]}rangeToNumber(e){return typeof e=="number"?e:this.faker.number.int(e)}multiple(e,r={}){let t=this.rangeToNumber(_nullishCoalesce(r.count, () => (3)));return t<=0?[]:Array.from({length:t},e)}},z=class extends v{constructor(r){super(r);this.faker=r}fake(r){r=typeof r=="string"?r:this.arrayElement(r);let t=r.search(/{{[a-z]/),a=r.indexOf("}}",t);if(t===-1||a===-1)return r;let o=r.substring(t+2,a+2).replace("}}","").replace("{{",""),s=Ke(o,this.faker),l=String(s),c=r.substring(0,t)+l+r.substring(a+2);return this.fake(c)}};var W=class extends x{int(e={}){typeof e=="number"&&(e={max:e});let{min:r=0,max:t=Number.MAX_SAFE_INTEGER,multipleOf:a=1}=e;if(!Number.isInteger(a))throw new m("multipleOf should be an integer.");if(a<=0)throw new m("multipleOf should be greater than 0.");let n=Math.ceil(r/a),o=Math.floor(t/a);if(n===o)return n*a;if(o<n)throw t>=r?new m(`No suitable integer value between ${r} and ${t} found.`):new m(`Max ${t} should be greater than min ${r}.`);let l=this.faker._randomizer.next(),c=o-n+1;return Math.floor(l*c+n)*a}float(e={}){typeof e=="number"&&(e={max:e});let{min:r=0,max:t=1,fractionDigits:a,multipleOf:n,multipleOf:o=a==null?void 0:10**-a}=e;if(t<r)throw new m(`Max ${t} should be greater than min ${r}.`);if(a!=null){if(n!=null)throw new m("multipleOf and fractionDigits cannot be set at the same time.");if(!Number.isInteger(a))throw new m("fractionDigits should be an integer.");if(a<0)throw new m("fractionDigits should be greater than or equal to 0.")}if(o!=null){if(o<=0)throw new m("multipleOf should be greater than 0.");let c=Math.log10(o),u=o<1&&Number.isInteger(c)?10**-c:1/o;return this.int({min:r*u,max:t*u})/u}return this.faker._randomizer.next()*(t-r)+r}binary(e={}){typeof e=="number"&&(e={max:e});let{min:r=0,max:t=1}=e;return this.int({max:t,min:r}).toString(2)}octal(e={}){typeof e=="number"&&(e={max:e});let{min:r=0,max:t=7}=e;return this.int({max:t,min:r}).toString(8)}hex(e={}){typeof e=="number"&&(e={max:e});let{min:r=0,max:t=15}=e;return this.int({max:t,min:r}).toString(16)}bigInt(e={}){(typeof e=="bigint"||typeof e=="number"||typeof e=="string"||typeof e=="boolean")&&(e={max:e});let r=BigInt(_nullishCoalesce(e.min, () => (0))),t=BigInt(_nullishCoalesce(e.max, () => (r+BigInt(999999999999999)))),a=BigInt(_nullishCoalesce(e.multipleOf, () => (1)));if(t<r)throw new m(`Max ${t} should be larger than min ${r}.`);if(a<=BigInt(0))throw new m("multipleOf should be greater than 0.");let n=r/a+(r%a>0n?1n:0n),o=t/a-(t%a<0n?1n:0n);if(n===o)return n*a;if(o<n)throw new m(`No suitable bigint value between ${r} and ${t} found.`);let s=o-n+1n,l=BigInt(this.faker.string.numeric({length:s.toString(10).length,allowLeadingZeros:!0}))%s;return(n+l)*a}romanNumeral(e={}){typeof e=="number"&&(e={max:e});let{min:a=1,max:n=3999}=e;if(a<1)throw new m(`Min value ${a} should be 1 or greater.`);if(n>3999)throw new m(`Max value ${n} should be 3999 or less.`);let o=this.int({min:a,max:n}),s=[["M",1e3],["CM",900],["D",500],["CD",400],["C",100],["XC",90],["L",50],["XL",40],["X",10],["IX",9],["V",5],["IV",4],["I",1]],l="";for(let[c,u]of s)l+=c.repeat(Math.floor(o/u)),o%=u;return l}};var Se="0123456789ABCDEFGHJKMNPQRSTVWXYZ";function Ve(i){let e=i.valueOf(),r="";for(let t=10;t>0;t--){let a=e%32;r=Se[a]+r,e=(e-a)/32}return r}var Y=[..."ABCDEFGHIJKLMNOPQRSTUVWXYZ"],Z=[..."abcdefghijklmnopqrstuvwxyz"],ze=[..."0123456789"],J=class extends x{fromCharacters(e,r=1){if(r=this.faker.helpers.rangeToNumber(r),r<=0)return"";if(typeof e=="string"&&(e=[...e]),e.length===0)throw new m("Unable to generate string: No characters to select from.");return this.faker.helpers.multiple(()=>this.faker.helpers.arrayElement(e),{count:r}).join("")}alpha(e={}){typeof e=="number"&&(e={length:e});let r=this.faker.helpers.rangeToNumber(_nullishCoalesce(e.length, () => (1)));if(r<=0)return"";let{casing:t="mixed"}=e,{exclude:a=[]}=e;typeof a=="string"&&(a=[...a]);let n;switch(t){case"upper":{n=[...Y];break}case"lower":{n=[...Z];break}case"mixed":{n=[...Z,...Y];break}}return n=n.filter(o=>!a.includes(o)),this.fromCharacters(n,r)}alphanumeric(e={}){typeof e=="number"&&(e={length:e});let r=this.faker.helpers.rangeToNumber(_nullishCoalesce(e.length, () => (1)));if(r<=0)return"";let{casing:t="mixed"}=e,{exclude:a=[]}=e;typeof a=="string"&&(a=[...a]);let n=[...ze];switch(t){case"upper":{n.push(...Y);break}case"lower":{n.push(...Z);break}case"mixed":{n.push(...Z,...Y);break}}return n=n.filter(o=>!a.includes(o)),this.fromCharacters(n,r)}binary(e={}){let{prefix:r="0b"}=e,t=r;return t+=this.fromCharacters(["0","1"],_nullishCoalesce(e.length, () => (1))),t}octal(e={}){let{prefix:r="0o"}=e,t=r;return t+=this.fromCharacters(["0","1","2","3","4","5","6","7"],_nullishCoalesce(e.length, () => (1))),t}hexadecimal(e={}){let{casing:r="mixed",prefix:t="0x"}=e,a=this.faker.helpers.rangeToNumber(_nullishCoalesce(e.length, () => (1)));if(a<=0)return t;let n=this.fromCharacters(["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","A","B","C","D","E","F"],a);return r==="upper"?n=n.toUpperCase():r==="lower"&&(n=n.toLowerCase()),`${t}${n}`}numeric(e={}){typeof e=="number"&&(e={length:e});let r=this.faker.helpers.rangeToNumber(_nullishCoalesce(e.length, () => (1)));if(r<=0)return"";let{allowLeadingZeros:t=!0}=e,{exclude:a=[]}=e;typeof a=="string"&&(a=[...a]);let n=ze.filter(s=>!a.includes(s));if(n.length===0||n.length===1&&!t&&n[0]==="0")throw new m("Unable to generate numeric string, because all possible digits are excluded.");let o="";return!t&&!a.includes("0")&&(o+=this.faker.helpers.arrayElement(n.filter(s=>s!=="0"))),o+=this.fromCharacters(n,r-o.length),o}sample(e=10){e=this.faker.helpers.rangeToNumber(e);let r={min:33,max:125},t="";for(;t.length<e;)t+=String.fromCodePoint(this.faker.number.int(r));return t}uuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replaceAll("x",()=>this.faker.number.hex({min:0,max:15})).replaceAll("y",()=>this.faker.number.hex({min:8,max:11}))}ulid(e={}){let{refDate:r=this.faker.defaultRefDate()}=e,t=S(r);return Ve(t)+this.fromCharacters(Se,16)}nanoid(e=21){if(e=this.faker.helpers.rangeToNumber(e),e<=0)return"";let r=[{value:()=>this.alphanumeric(1),weight:62},{value:()=>this.faker.helpers.arrayElement(["_","-"]),weight:2}],t="";for(;t.length<e;){let a=this.faker.helpers.weightedArrayElement(r);t+=a()}return t}symbol(e=1){return this.fromCharacters(["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_","`","{","|","}","~"],e)}};var $= (_class =class{__init() {this._defaultRefDate=()=>new Date}get defaultRefDate(){return this._defaultRefDate}setDefaultRefDate(e=()=>new Date){typeof e=="function"?this._defaultRefDate=e:this._defaultRefDate=()=>new Date(e)}__init2() {this.datatype=new K(this)}__init3() {this.date=new B(this)}__init4() {this.helpers=new v(this)}__init5() {this.number=new W(this)}__init6() {this.string=new J(this)}constructor(e={}){;_class.prototype.__init.call(this);_class.prototype.__init2.call(this);_class.prototype.__init3.call(this);_class.prototype.__init4.call(this);_class.prototype.__init5.call(this);_class.prototype.__init6.call(this);let{randomizer:r,seed:t}=e;r!=null&&t!=null&&r.seed(t),this._randomizer=_nullishCoalesce(r, () => (Oe(t)))}seed(e=P()){return this._randomizer.seed(e),e}}, _class),fa= exports.l =new $;function We(i){let e={};for(let r of i)for(let t in r){let a=r[t];e[t]===void 0?e[t]={...a}:e[t]={...a,...e[t]}}return e}var X=class extends p{dog(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.dog)}cat(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.cat)}snake(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.snake)}bear(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.bear)}lion(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.lion)}cetacean(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.cetacean)}horse(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.horse)}bird(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.bird)}cow(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.cow)}fish(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.fish)}crocodilia(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.crocodilia)}insect(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.insect)}rabbit(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.rabbit)}rodent(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.rodent)}type(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.type)}petName(){return this.faker.helpers.arrayElement(this.faker.definitions.animal.pet_name)}};var Q=class extends p{author(){return this.faker.helpers.arrayElement(this.faker.definitions.book.author)}format(){return this.faker.helpers.arrayElement(this.faker.definitions.book.format)}genre(){return this.faker.helpers.arrayElement(this.faker.definitions.book.genre)}publisher(){return this.faker.helpers.arrayElement(this.faker.definitions.book.publisher)}series(){return this.faker.helpers.arrayElement(this.faker.definitions.book.series)}title(){return this.faker.helpers.arrayElement(this.faker.definitions.book.title)}};var Hr={0:[[1999999,2],[2279999,3],[2289999,4],[3689999,3],[3699999,4],[6389999,3],[6397999,4],[6399999,7],[6449999,3],[6459999,7],[6479999,3],[6489999,7],[6549999,3],[6559999,4],[6999999,3],[8499999,4],[8999999,5],[9499999,6],[9999999,7]],1:[[99999,3],[299999,2],[349999,3],[399999,4],[499999,3],[699999,2],[999999,4],[3979999,3],[5499999,4],[6499999,5],[6799999,4],[6859999,5],[7139999,4],[7169999,3],[7319999,4],[7399999,7],[7749999,5],[7753999,7],[7763999,5],[7764999,7],[7769999,5],[7782999,7],[7899999,5],[7999999,4],[8004999,5],[8049999,5],[8379999,5],[8384999,7],[8671999,5],[8675999,4],[8697999,5],[9159999,6],[9165059,7],[9168699,6],[9169079,7],[9195999,6],[9196549,7],[9729999,6],[9877999,4],[9911499,6],[9911999,7],[9989899,6],[9999999,7]]},q=class extends p{department(){return this.faker.helpers.arrayElement(this.faker.definitions.commerce.department)}productName(){return`${this.productAdjective()} ${this.productMaterial()} ${this.product()}`}price(e={}){let{dec:r=2,max:t=1e3,min:a=1,symbol:n=""}=e;if(a<0||t<0)return`${n}0`;if(a===t)return`${n}${a.toFixed(r)}`;let o=this.faker.number.float({min:a,max:t,fractionDigits:r});if(r===0)return`${n}${o.toFixed(r)}`;let s=o*10**r%10,l=this.faker.helpers.weightedArrayElement([{weight:5,value:9},{weight:3,value:5},{weight:1,value:0},{weight:1,value:this.faker.number.int({min:0,max:9})}]),c=(1/10)**r,u=s*c,h=l*c,f=o-u+h;return a<=f&&f<=t?`${n}${f.toFixed(r)}`:`${n}${o.toFixed(r)}`}productAdjective(){return this.faker.helpers.arrayElement(this.faker.definitions.commerce.product_name.adjective)}productMaterial(){return this.faker.helpers.arrayElement(this.faker.definitions.commerce.product_name.material)}product(){return this.faker.helpers.arrayElement(this.faker.definitions.commerce.product_name.product)}productDescription(){return this.faker.helpers.fake(this.faker.definitions.commerce.product_description)}isbn(e={}){typeof e=="number"&&(e={variant:e});let{variant:r=13,separator:t="-"}=e,a="978",[n,o]=this.faker.helpers.objectEntry(Hr),s=this.faker.string.numeric(8),l=Number.parseInt(s.slice(0,-1)),c=_optionalChain([o, 'access', _14 => _14.find, 'call', _15 => _15(([y])=>l<=y), 'optionalAccess', _16 => _16[1]]);if(!c)throw new m(`Unable to find a registrant length for the group ${n}`);let u=s.slice(0,c),h=s.slice(c),f=[a,n,u,h];r===10&&f.shift();let g=f.join(""),b=0;for(let y=0;y<r-1;y++){let N=r===10?y+1:y%2?3:1;b+=N*Number.parseInt(g[y])}return b=r===10?b%11:(10-b%10)%10,f.push(b===10?"X":b.toString()),f.join(t)}};var ee=class extends p{name(){return this.faker.helpers.fake(this.faker.definitions.company.name_pattern)}catchPhrase(){return[this.catchPhraseAdjective(),this.catchPhraseDescriptor(),this.catchPhraseNoun()].join(" ")}buzzPhrase(){return[this.buzzVerb(),this.buzzAdjective(),this.buzzNoun()].join(" ")}catchPhraseAdjective(){return this.faker.helpers.arrayElement(this.faker.definitions.company.adjective)}catchPhraseDescriptor(){return this.faker.helpers.arrayElement(this.faker.definitions.company.descriptor)}catchPhraseNoun(){return this.faker.helpers.arrayElement(this.faker.definitions.company.noun)}buzzAdjective(){return this.faker.helpers.arrayElement(this.faker.definitions.company.buzz_adjective)}buzzVerb(){return this.faker.helpers.arrayElement(this.faker.definitions.company.buzz_verb)}buzzNoun(){return this.faker.helpers.arrayElement(this.faker.definitions.company.buzz_noun)}};var re=class extends p{column(){return this.faker.helpers.arrayElement(this.faker.definitions.database.column)}type(){return this.faker.helpers.arrayElement(this.faker.definitions.database.type)}collation(){return this.faker.helpers.arrayElement(this.faker.definitions.database.collation)}engine(){return this.faker.helpers.arrayElement(this.faker.definitions.database.engine)}mongodbObjectId(){return this.faker.string.hexadecimal({length:24,casing:"lower",prefix:""})}};var Vr={alpha:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],formats:[{country:"AL",total:28,bban:[{type:"n",count:8},{type:"c",count:16}],format:"ALkk bbbs sssx cccc cccc cccc cccc"},{country:"AD",total:24,bban:[{type:"n",count:8},{type:"c",count:12}],format:"ADkk bbbb ssss cccc cccc cccc"},{country:"AT",total:20,bban:[{type:"n",count:5},{type:"n",count:11}],format:"ATkk bbbb bccc cccc cccc"},{country:"AZ",total:28,bban:[{type:"a",count:4},{type:"n",count:20}],format:"AZkk bbbb cccc cccc cccc cccc cccc"},{country:"BH",total:22,bban:[{type:"a",count:4},{type:"c",count:14}],format:"BHkk bbbb cccc cccc cccc cc"},{country:"BE",total:16,bban:[{type:"n",count:3},{type:"n",count:9}],format:"BEkk bbbc cccc ccxx"},{country:"BA",total:20,bban:[{type:"n",count:6},{type:"n",count:10}],format:"BAkk bbbs sscc cccc ccxx"},{country:"BR",total:29,bban:[{type:"n",count:13},{type:"n",count:10},{type:"a",count:1},{type:"c",count:1}],format:"BRkk bbbb bbbb ssss sccc cccc ccct n"},{country:"BG",total:22,bban:[{type:"a",count:4},{type:"n",count:6},{type:"c",count:8}],format:"BGkk bbbb ssss ddcc cccc cc"},{country:"CR",total:22,bban:[{type:"n",count:1},{type:"n",count:3},{type:"n",count:14}],format:"CRkk xbbb cccc cccc cccc cc"},{country:"HR",total:21,bban:[{type:"n",count:7},{type:"n",count:10}],format:"HRkk bbbb bbbc cccc cccc c"},{country:"CY",total:28,bban:[{type:"n",count:8},{type:"c",count:16}],format:"CYkk bbbs ssss cccc cccc cccc cccc"},{country:"CZ",total:24,bban:[{type:"n",count:10},{type:"n",count:10}],format:"CZkk bbbb ssss sscc cccc cccc"},{country:"DK",total:18,bban:[{type:"n",count:4},{type:"n",count:10}],format:"DKkk bbbb cccc cccc cc"},{country:"DO",total:28,bban:[{type:"a",count:4},{type:"n",count:20}],format:"DOkk bbbb cccc cccc cccc cccc cccc"},{country:"TL",total:23,bban:[{type:"n",count:3},{type:"n",count:16}],format:"TLkk bbbc cccc cccc cccc cxx"},{country:"EE",total:20,bban:[{type:"n",count:4},{type:"n",count:12}],format:"EEkk bbss cccc cccc cccx"},{country:"FO",total:18,bban:[{type:"n",count:4},{type:"n",count:10}],format:"FOkk bbbb cccc cccc cx"},{country:"FI",total:18,bban:[{type:"n",count:6},{type:"n",count:8}],format:"FIkk bbbb bbcc cccc cx"},{country:"FR",total:27,bban:[{type:"n",count:10},{type:"c",count:11},{type:"n",count:2}],format:"FRkk bbbb bggg ggcc cccc cccc cxx"},{country:"GE",total:22,bban:[{type:"a",count:2},{type:"n",count:16}],format:"GEkk bbcc cccc cccc cccc cc"},{country:"DE",total:22,bban:[{type:"n",count:8},{type:"n",count:10}],format:"DEkk bbbb bbbb cccc cccc cc"},{country:"GI",total:23,bban:[{type:"a",count:4},{type:"c",count:15}],format:"GIkk bbbb cccc cccc cccc ccc"},{country:"GR",total:27,bban:[{type:"n",count:7},{type:"c",count:16}],format:"GRkk bbbs sssc cccc cccc cccc ccc"},{country:"GL",total:18,bban:[{type:"n",count:4},{type:"n",count:10}],format:"GLkk bbbb cccc cccc cc"},{country:"GT",total:28,bban:[{type:"c",count:4},{type:"c",count:4},{type:"c",count:16}],format:"GTkk bbbb mmtt cccc cccc cccc cccc"},{country:"HU",total:28,bban:[{type:"n",count:8},{type:"n",count:16}],format:"HUkk bbbs sssk cccc cccc cccc cccx"},{country:"IS",total:26,bban:[{type:"n",count:6},{type:"n",count:16}],format:"ISkk bbbb sscc cccc iiii iiii ii"},{country:"IE",total:22,bban:[{type:"a",count:4},{type:"n",count:6},{type:"n",count:8}],format:"IEkk aaaa bbbb bbcc cccc cc"},{country:"IL",total:23,bban:[{type:"n",count:6},{type:"n",count:13}],format:"ILkk bbbn nncc cccc cccc ccc"},{country:"IT",total:27,bban:[{type:"a",count:1},{type:"n",count:10},{type:"c",count:12}],format:"ITkk xaaa aabb bbbc cccc cccc ccc"},{country:"JO",total:30,bban:[{type:"a",count:4},{type:"n",count:4},{type:"n",count:18}],format:"JOkk bbbb nnnn cccc cccc cccc cccc cc"},{country:"KZ",total:20,bban:[{type:"n",count:3},{type:"c",count:13}],format:"KZkk bbbc cccc cccc cccc"},{country:"XK",total:20,bban:[{type:"n",count:4},{type:"n",count:12}],format:"XKkk bbbb cccc cccc cccc"},{country:"KW",total:30,bban:[{type:"a",count:4},{type:"c",count:22}],format:"KWkk bbbb cccc cccc cccc cccc cccc cc"},{country:"LV",total:21,bban:[{type:"a",count:4},{type:"c",count:13}],format:"LVkk bbbb cccc cccc cccc c"},{country:"LB",total:28,bban:[{type:"n",count:4},{type:"c",count:20}],format:"LBkk bbbb cccc cccc cccc cccc cccc"},{country:"LI",total:21,bban:[{type:"n",count:5},{type:"c",count:12}],format:"LIkk bbbb bccc cccc cccc c"},{country:"LT",total:20,bban:[{type:"n",count:5},{type:"n",count:11}],format:"LTkk bbbb bccc cccc cccc"},{country:"LU",total:20,bban:[{type:"n",count:3},{type:"c",count:13}],format:"LUkk bbbc cccc cccc cccc"},{country:"MK",total:19,bban:[{type:"n",count:3},{type:"c",count:10},{type:"n",count:2}],format:"MKkk bbbc cccc cccc cxx"},{country:"MT",total:31,bban:[{type:"a",count:4},{type:"n",count:5},{type:"c",count:18}],format:"MTkk bbbb ssss sccc cccc cccc cccc ccc"},{country:"MR",total:27,bban:[{type:"n",count:10},{type:"n",count:13}],format:"MRkk bbbb bsss sscc cccc cccc cxx"},{country:"MU",total:30,bban:[{type:"a",count:4},{type:"n",count:4},{type:"n",count:15},{type:"a",count:3}],format:"MUkk bbbb bbss cccc cccc cccc 000d dd"},{country:"MC",total:27,bban:[{type:"n",count:10},{type:"c",count:11},{type:"n",count:2}],format:"MCkk bbbb bsss sscc cccc cccc cxx"},{country:"MD",total:24,bban:[{type:"c",count:2},{type:"c",count:18}],format:"MDkk bbcc cccc cccc cccc cccc"},{country:"ME",total:22,bban:[{type:"n",count:3},{type:"n",count:15}],format:"MEkk bbbc cccc cccc cccc xx"},{country:"NL",total:18,bban:[{type:"a",count:4},{type:"n",count:10}],format:"NLkk bbbb cccc cccc cc"},{country:"NO",total:15,bban:[{type:"n",count:4},{type:"n",count:7}],format:"NOkk bbbb cccc ccx"},{country:"PK",total:24,bban:[{type:"a",count:4},{type:"n",count:16}],format:"PKkk bbbb cccc cccc cccc cccc"},{country:"PS",total:29,bban:[{type:"a",count:4},{type:"n",count:9},{type:"n",count:12}],format:"PSkk bbbb xxxx xxxx xccc cccc cccc c"},{country:"PL",total:28,bban:[{type:"n",count:8},{type:"n",count:16}],format:"PLkk bbbs sssx cccc cccc cccc cccc"},{country:"PT",total:25,bban:[{type:"n",count:8},{type:"n",count:13}],format:"PTkk bbbb ssss cccc cccc cccx x"},{country:"QA",total:29,bban:[{type:"a",count:4},{type:"c",count:21}],format:"QAkk bbbb cccc cccc cccc cccc cccc c"},{country:"RO",total:24,bban:[{type:"a",count:4},{type:"c",count:16}],format:"ROkk bbbb cccc cccc cccc cccc"},{country:"SM",total:27,bban:[{type:"a",count:1},{type:"n",count:10},{type:"c",count:12}],format:"SMkk xaaa aabb bbbc cccc cccc ccc"},{country:"SA",total:24,bban:[{type:"n",count:2},{type:"c",count:18}],format:"SAkk bbcc cccc cccc cccc cccc"},{country:"RS",total:22,bban:[{type:"n",count:3},{type:"n",count:15}],format:"RSkk bbbc cccc cccc cccc xx"},{country:"SK",total:24,bban:[{type:"n",count:10},{type:"n",count:10}],format:"SKkk bbbb ssss sscc cccc cccc"},{country:"SI",total:19,bban:[{type:"n",count:5},{type:"n",count:10}],format:"SIkk bbss sccc cccc cxx"},{country:"ES",total:24,bban:[{type:"n",count:10},{type:"n",count:10}],format:"ESkk bbbb gggg xxcc cccc cccc"},{country:"SE",total:24,bban:[{type:"n",count:3},{type:"n",count:17}],format:"SEkk bbbc cccc cccc cccc cccc"},{country:"CH",total:21,bban:[{type:"n",count:5},{type:"c",count:12}],format:"CHkk bbbb bccc cccc cccc c"},{country:"TN",total:24,bban:[{type:"n",count:5},{type:"n",count:15}],format:"TNkk bbss sccc cccc cccc cccc"},{country:"TR",total:26,bban:[{type:"n",count:5},{type:"n",count:1},{type:"n",count:16}],format:"TRkk bbbb bxcc cccc cccc cccc cc"},{country:"AE",total:23,bban:[{type:"n",count:3},{type:"n",count:16}],format:"AEkk bbbc cccc cccc cccc ccc"},{country:"GB",total:22,bban:[{type:"a",count:4},{type:"n",count:6},{type:"n",count:8}],format:"GBkk bbbb ssss sscc cccc cc"},{country:"VG",total:24,bban:[{type:"a",count:4},{type:"n",count:16}],format:"VGkk bbbb cccc cccc cccc cccc"}],iso3166:["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","XK","YE","YT","ZA","ZM","ZW"],mod97:i=>{let e=0;for(let r of i)e=(e*10+ +r)%97;return e},pattern10:["01","02","03","04","05","06","07","08","09"],pattern100:["001","002","003","004","005","006","007","008","009"],toDigitString:i=>i.replaceAll(/[A-Z]/gi,e=>String((_nullishCoalesce(e.toUpperCase().codePointAt(0), () => (Number.NaN)))-55))},M=Vr;function zr(i){let e="";for(let r=0;r<i.length;r+=4)e+=`${i.substring(r,r+4)} `;return e.trimEnd()}var te=class extends p{accountNumber(e={}){typeof e=="number"&&(e={length:e});let{length:r=8}=e;return this.faker.string.numeric({length:r,allowLeadingZeros:!0})}accountName(){return[this.faker.helpers.arrayElement(this.faker.definitions.finance.account_type),"Account"].join(" ")}routingNumber(){let e=this.faker.string.numeric({length:8,allowLeadingZeros:!0}),r=0;for(let t=0;t<e.length;t+=3)r+=Number(e[t])*3,r+=Number(e[t+1])*7,r+=Number(e[t+2])||0;return`${e}${Math.ceil(r/10)*10-r}`}maskedNumber(e={}){w({deprecated:"faker.finance.maskedNumber()",proposed:"faker.finance.iban().replace(/(?<=.{4})\\w(?=.{2})/g, '*') or a similar approach",since:"9.3.0",until:"10.0.0"}),typeof e=="number"&&(e={length:e});let{ellipsis:r=!0,length:t=4,parens:a=!0}=e,n=this.faker.string.numeric({length:t});return r&&(n=`...${n}`),a&&(n=`(${n})`),n}amount(e={}){let{autoFormat:r=!1,dec:t=2,max:a=1e3,min:n=0,symbol:o=""}=e,s=this.faker.number.float({max:a,min:n,fractionDigits:t}),l=r?s.toLocaleString(void 0,{minimumFractionDigits:t}):s.toFixed(t);return o+l}transactionType(){return this.faker.helpers.arrayElement(this.faker.definitions.finance.transaction_type)}currency(){return this.faker.helpers.arrayElement(this.faker.definitions.finance.currency)}currencyCode(){return this.currency().code}currencyName(){return this.currency().name}currencySymbol(){let e;do e=this.currency().symbol;while(e.length===0);return e}currencyNumericCode(){return this.currency().numericCode}bitcoinAddress(e={}){let{type:r=this.faker.helpers.enumValue(be),network:t="mainnet"}=e,a=Be[r],n=a.prefix[t],o=this.faker.number.int(a.length),s=this.faker.string.alphanumeric({length:o-n.length,casing:a.casing,exclude:a.exclude});return n+s}litecoinAddress(){let e=this.faker.number.int({min:26,max:33});return this.faker.string.fromCharacters("LM3")+this.faker.string.fromCharacters("**********************************************************",e-1)}creditCardNumber(e={}){typeof e=="string"&&(e={issuer:e});let{issuer:r=""}=e,t,a=this.faker.definitions.finance.credit_card,n=r.toLowerCase();if(n in a)t=this.faker.helpers.arrayElement(a[n]);else if(r.includes("#"))t=r;else{let o=this.faker.helpers.objectValue(a);t=this.faker.helpers.arrayElement(o)}return t=t.replaceAll("/",""),this.faker.helpers.replaceCreditCardSymbols(t)}creditCardCVV(){return this.faker.string.numeric({length:3,allowLeadingZeros:!0})}creditCardIssuer(){return this.faker.helpers.objectKey(this.faker.definitions.finance.credit_card)}pin(e={}){typeof e=="number"&&(e={length:e});let{length:r=4}=e;if(r<1)throw new m("minimum length is 1");return this.faker.string.numeric({length:r,allowLeadingZeros:!0})}ethereumAddress(){return this.faker.string.hexadecimal({length:40,casing:"lower"})}iban(e={}){let{countryCode:r,formatted:t=!1}=e,a=r?M.formats.find(c=>c.country===r):this.faker.helpers.arrayElement(M.formats);if(!a)throw new m(`Country code ${r} not supported.`);let n="",o=0;for(let c of a.bban){let u=c.count;for(o+=c.count;u>0;)c.type==="a"?n+=this.faker.helpers.arrayElement(M.alpha):c.type==="c"?this.faker.datatype.boolean(.8)?n+=this.faker.number.int(9):n+=this.faker.helpers.arrayElement(M.alpha):u>=3&&this.faker.datatype.boolean(.3)?this.faker.datatype.boolean()?(n+=this.faker.helpers.arrayElement(M.pattern100),u-=2):(n+=this.faker.helpers.arrayElement(M.pattern10),u--):n+=this.faker.number.int(9),u--;n=n.substring(0,o)}let s=98-M.mod97(M.toDigitString(`${n}${a.country}00`));s<10&&(s=`0${s}`);let l=`${a.country}${s}${n}`;return t?zr(l):l}bic(e={}){let{includeBranchCode:r=this.faker.datatype.boolean()}=e,t=this.faker.string.alpha({length:4,casing:"upper"}),a=this.faker.helpers.arrayElement(M.iso3166),n=this.faker.string.alphanumeric({length:2,casing:"upper"}),o=r?this.faker.datatype.boolean()?this.faker.string.alphanumeric({length:3,casing:"upper"}):"XXX":"";return`${t}${a}${n}${o}`}transactionDescription(){return this.faker.helpers.fake(this.faker.definitions.finance.transaction_description_pattern)}};function Ye(i){return i.split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}var ae=class extends p{adjective(){return this.faker.helpers.arrayElement(this.faker.definitions.food.adjective)}description(){return this.faker.helpers.fake(this.faker.definitions.food.description_pattern)}dish(){return this.faker.datatype.boolean()?Ye(this.faker.helpers.fake(this.faker.definitions.food.dish_pattern)):Ye(this.faker.helpers.arrayElement(this.faker.definitions.food.dish))}ethnicCategory(){return this.faker.helpers.arrayElement(this.faker.definitions.food.ethnic_category)}fruit(){return this.faker.helpers.arrayElement(this.faker.definitions.food.fruit)}ingredient(){return this.faker.helpers.arrayElement(this.faker.definitions.food.ingredient)}meat(){return this.faker.helpers.arrayElement(this.faker.definitions.food.meat)}spice(){return this.faker.helpers.arrayElement(this.faker.definitions.food.spice)}vegetable(){return this.faker.helpers.arrayElement(this.faker.definitions.food.vegetable)}};var Wr="\xA0",ne=class extends p{branch(){let e=this.faker.hacker.noun().replace(" ","-"),r=this.faker.hacker.verb().replace(" ","-");return`${e}-${r}`}commitEntry(e={}){let{merge:r=this.faker.datatype.boolean({probability:.2}),eol:t="CRLF",refDate:a}=e,n=[`commit ${this.faker.git.commitSha()}`];r&&n.push(`Merge: ${this.commitSha({length:7})} ${this.commitSha({length:7})}`);let o=this.faker.person.firstName(),s=this.faker.person.lastName(),l=this.faker.person.fullName({firstName:o,lastName:s}),c=this.faker.internet.username({firstName:o,lastName:s}),u=this.faker.helpers.arrayElement([l,c]),h=this.faker.internet.email({firstName:o,lastName:s});u=u.replaceAll(/^[.,:;"\\']|[<>\n]|[.,:;"\\']$/g,""),n.push(`Author: ${u} <${h}>`,`Date: ${this.commitDate({refDate:a})}`,"",`${Wr.repeat(4)}${this.commitMessage()}`,"");let f=t==="CRLF"?`\r
`:`
`;return n.join(f)}commitMessage(){return`${this.faker.hacker.verb()} ${this.faker.hacker.adjective()} ${this.faker.hacker.noun()}`}commitDate(e={}){let{refDate:r=this.faker.defaultRefDate()}=e,t=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],a=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],n=this.faker.date.recent({days:1,refDate:r}),o=t[n.getUTCDay()],s=a[n.getUTCMonth()],l=n.getUTCDate(),c=n.getUTCHours().toString().padStart(2,"0"),u=n.getUTCMinutes().toString().padStart(2,"0"),h=n.getUTCSeconds().toString().padStart(2,"0"),f=n.getUTCFullYear(),g=this.faker.number.int({min:-11,max:12}),b=Math.abs(g).toString().padStart(2,"0"),y="00",N=g>=0?"+":"-";return`${o} ${s} ${l} ${c}:${u}:${h} ${f} ${N}${b}${y}`}commitSha(e={}){let{length:r=40}=e;return this.faker.string.hexadecimal({length:r,casing:"lower",prefix:""})}};var ie=class extends p{abbreviation(){return this.faker.helpers.arrayElement(this.faker.definitions.hacker.abbreviation)}adjective(){return this.faker.helpers.arrayElement(this.faker.definitions.hacker.adjective)}noun(){return this.faker.helpers.arrayElement(this.faker.definitions.hacker.noun)}verb(){return this.faker.helpers.arrayElement(this.faker.definitions.hacker.verb)}ingverb(){return this.faker.helpers.arrayElement(this.faker.definitions.hacker.ingverb)}phrase(){let e={abbreviation:this.abbreviation,adjective:this.adjective,ingverb:this.ingverb,noun:this.noun,verb:this.verb},r=this.faker.helpers.arrayElement(this.faker.definitions.hacker.phrase);return this.faker.helpers.mustache(r,e)}};var oe=class extends p{avatar(){return this.faker.helpers.arrayElement([this.personPortrait,this.avatarGitHub])()}avatarGitHub(){return`https://avatars.githubusercontent.com/u/${this.faker.number.int(1e8)}`}personPortrait(e={}){let{sex:r=this.faker.person.sexType(),size:t=512}=e;return`https://cdn.jsdelivr.net/gh/faker-js/assets-person-portrait/${r}/${t}/${this.faker.number.int({min:0,max:99})}.jpg`}avatarLegacy(){return w({deprecated:"faker.image.avatarLegacy()",proposed:"faker.image.avatar() or faker.image.personPortrait()",since:"9.0.2",until:"10.0.0"}),`https://cloudflare-ipfs.com/ipfs/Qmd3W5DuhgHirLHGVixi6V76LhCkZUz6pnFt5AJBiyvHye/avatar/${this.faker.number.int(1249)}.jpg`}url(e={}){let{width:r=this.faker.number.int({min:1,max:3999}),height:t=this.faker.number.int({min:1,max:3999})}=e;return this.faker.helpers.arrayElement([this.urlLoremFlickr,({width:n,height:o})=>this.urlPicsumPhotos({width:n,height:o,grayscale:!1,blur:0})])({width:r,height:t})}urlLoremFlickr(e={}){let{width:r=this.faker.number.int({min:1,max:3999}),height:t=this.faker.number.int({min:1,max:3999}),category:a}=e;return`https://loremflickr.com/${r}/${t}${a==null?"":`/${a}`}?lock=${this.faker.number.int()}`}urlPicsumPhotos(e={}){let{width:r=this.faker.number.int({min:1,max:3999}),height:t=this.faker.number.int({min:1,max:3999}),grayscale:a=this.faker.datatype.boolean(),blur:n=this.faker.number.int({max:10})}=e,o=`https://picsum.photos/seed/${this.faker.string.alphanumeric({length:{min:5,max:10}})}/${r}/${t}`,s=typeof n=="number"&&n>=1&&n<=10;return(a||s)&&(o+="?",a&&(o+="grayscale"),a&&s&&(o+="&"),s&&(o+=`blur=${n}`)),o}urlPlaceholder(e={}){w({deprecated:"faker.image.urlPlaceholder()",proposed:"faker.image.url() or faker.image.dataUri()",since:"9.4.0",until:"10.0.0"});let{width:r=this.faker.number.int({min:1,max:3500}),height:t=this.faker.number.int({min:1,max:3500}),backgroundColor:a=this.faker.color.rgb({format:"hex",prefix:""}),textColor:n=this.faker.color.rgb({format:"hex",prefix:""}),format:o=this.faker.helpers.arrayElement(["gif","jpeg","jpg","png","webp"]),text:s=this.faker.lorem.words()}=e,l="https://via.placeholder.com";return l+=`/${r}`,l+=`x${t}`,l+=`/${a}`,l+=`/${n}`,l+=`.${o}`,l+=`?text=${encodeURIComponent(s)}`,l}dataUri(e={}){let{width:r=this.faker.number.int({min:1,max:3999}),height:t=this.faker.number.int({min:1,max:3999}),color:a=this.faker.color.rgb(),type:n=this.faker.helpers.arrayElement(["svg-uri","svg-base64"])}=e,o=`<svg xmlns="http://www.w3.org/2000/svg" version="1.1" baseProfile="full" width="${r}" height="${t}"><rect width="100%" height="100%" fill="${a}"/><text x="${r/2}" y="${t/2}" font-size="20" alignment-baseline="middle" text-anchor="middle" fill="white">${r}x${t}</text></svg>`;return n==="svg-uri"?`data:image/svg+xml;charset=UTF-8,${encodeURIComponent(o)}`:`data:image/svg+xml;base64,${de(o)}`}};var se=class extends p{zipCode(e={}){typeof e=="string"&&(e={format:e});let{state:r}=e;if(r!=null){let a=this.faker.definitions.location.postcode_by_state[r];if(a==null)throw new m(`No zip code definition found for state "${r}"`);return this.faker.helpers.fake(a)}let{format:t=this.faker.definitions.location.postcode}=e;return typeof t=="string"&&(t=[t]),t=this.faker.helpers.arrayElement(t),this.faker.helpers.replaceSymbols(t)}city(){return this.faker.helpers.fake(this.faker.definitions.location.city_pattern)}buildingNumber(){return this.faker.helpers.arrayElement(this.faker.definitions.location.building_number).replaceAll(/#+/g,e=>this.faker.string.numeric({length:e.length,allowLeadingZeros:!1}))}street(){return this.faker.helpers.fake(this.faker.definitions.location.street_pattern)}streetAddress(e={}){typeof e=="boolean"&&(e={useFullAddress:e});let{useFullAddress:r}=e,a=this.faker.definitions.location.street_address[r?"full":"normal"];return this.faker.helpers.fake(a)}secondaryAddress(){return this.faker.helpers.fake(this.faker.definitions.location.secondary_address).replaceAll(/#+/g,e=>this.faker.string.numeric({length:e.length,allowLeadingZeros:!1}))}county(){return this.faker.helpers.arrayElement(this.faker.definitions.location.county)}country(){return this.faker.helpers.arrayElement(this.faker.definitions.location.country)}continent(){return this.faker.helpers.arrayElement(this.faker.definitions.location.continent)}countryCode(e={}){typeof e=="string"&&(e={variant:e});let{variant:r="alpha-2"}=e,t=(()=>{switch(r){case"numeric":return"numeric";case"alpha-3":return"alpha3";case"alpha-2":return"alpha2"}})();return this.faker.helpers.arrayElement(this.faker.definitions.location.country_code)[t]}state(e={}){let{abbreviated:r=!1}=e,t=r?this.faker.definitions.location.state_abbr:this.faker.definitions.location.state;return this.faker.helpers.arrayElement(t)}latitude(e={}){let{max:r=90,min:t=-90,precision:a=4}=e;return this.faker.number.float({min:t,max:r,fractionDigits:a})}longitude(e={}){let{max:r=180,min:t=-180,precision:a=4}=e;return this.faker.number.float({max:r,min:t,fractionDigits:a})}direction(e={}){let{abbreviated:r=!1}=e;return r?this.faker.helpers.arrayElement([...this.faker.definitions.location.direction.cardinal_abbr,...this.faker.definitions.location.direction.ordinal_abbr]):this.faker.helpers.arrayElement([...this.faker.definitions.location.direction.cardinal,...this.faker.definitions.location.direction.ordinal])}cardinalDirection(e={}){let{abbreviated:r=!1}=e;return r?this.faker.helpers.arrayElement(this.faker.definitions.location.direction.cardinal_abbr):this.faker.helpers.arrayElement(this.faker.definitions.location.direction.cardinal)}ordinalDirection(e={}){let{abbreviated:r=!1}=e;return r?this.faker.helpers.arrayElement(this.faker.definitions.location.direction.ordinal_abbr):this.faker.helpers.arrayElement(this.faker.definitions.location.direction.ordinal)}nearbyGPSCoordinate(e={}){let{origin:r,radius:t=10,isMetric:a=!1}=e;if(r==null)return[this.latitude(),this.longitude()];let n=this.faker.number.float({max:2*Math.PI,fractionDigits:5}),o=a?t:t*1.60934,l=this.faker.number.float({max:o,fractionDigits:3})*.995,c=4e4/360,u=l/c,h=[r[0]+Math.sin(n)*u,r[1]+Math.cos(n)*u];return h[0]=h[0]%180,(h[0]<-90||h[0]>90)&&(h[0]=Math.sign(h[0])*180-h[0],h[1]+=180),h[1]=(h[1]%360+540)%360-180,[h[0],h[1]]}timeZone(){return this.faker.helpers.arrayElement(this.faker.definitions.location.time_zone)}language(){return this.faker.helpers.arrayElement(this.faker.definitions.location.language)}};function Ze(i,e,r=t=>t){let t={};for(let a of i){let n=e(a);t[n]===void 0&&(t[n]=[]),t[n].push(r(a))}return t}var Me={fail:()=>{throw new m("No words found that match the given length.")},closest:(i,e)=>{let r=Ze(i,s=>s.length),t=Object.keys(r).map(Number),a=Math.min(...t),n=Math.max(...t),o=Math.min(e.min-a,n-e.max);return i.filter(s=>s.length===e.min-o||s.length===e.max+o)},shortest:i=>{let e=Math.min(...i.map(r=>r.length));return i.filter(r=>r.length===e)},longest:i=>{let e=Math.max(...i.map(r=>r.length));return i.filter(r=>r.length===e)},"any-length":i=>[...i]};function T(i){let{wordList:e,length:r,strategy:t="any-length"}=i;if(r!=null){let a=typeof r=="number"?o=>o.length===r:o=>o.length>=r.min&&o.length<=r.max,n=e.filter(a);return n.length>0?n:typeof r=="number"?Me[t](e,{min:r,max:r}):Me[t](e,r)}else if(t==="shortest"||t==="longest")return Me[t](e);return[...e]}var ce=class extends p{word(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.lorem.word}))}words(e=3){return this.faker.helpers.multiple(()=>this.word(),{count:e}).join(" ")}sentence(e={min:3,max:10}){let r=this.words(e);return`${r.charAt(0).toUpperCase()+r.substring(1)}.`}slug(e=3){let r=this.words(e);return this.faker.helpers.slugify(r)}sentences(e={min:2,max:6},r=" "){return this.faker.helpers.multiple(()=>this.sentence(),{count:e}).join(r)}paragraph(e=3){return this.sentences(e)}paragraphs(e=3,r=`
`){return this.faker.helpers.multiple(()=>this.paragraph(),{count:e}).join(r)}text(){let e=["sentence","sentences","paragraph","paragraphs","lines"],r=this.faker.helpers.arrayElement(e);return this[r]()}lines(e={min:1,max:5}){return this.sentences(e,`
`)}};var le=class extends p{album(){return this.faker.helpers.arrayElement(this.faker.definitions.music.album)}artist(){return this.faker.helpers.arrayElement(this.faker.definitions.music.artist)}genre(){return this.faker.helpers.arrayElement(this.faker.definitions.music.genre)}songName(){return this.faker.helpers.arrayElement(this.faker.definitions.music.song_name)}};var me=class extends p{number(e={}){let{style:r="human"}=e,a=this.faker.definitions.phone_number.format[r];if(!a)throw new Error(`No definitions for ${r} in this locale`);let n=this.faker.helpers.arrayElement(a);return we(this.faker,n)}imei(){return this.faker.helpers.replaceCreditCardSymbols("##-######-######-L","#")}};var ue=class extends p{chemicalElement(){return this.faker.helpers.arrayElement(this.faker.definitions.science.chemical_element)}unit(){return this.faker.helpers.arrayElement(this.faker.definitions.science.unit)}};var Yr=["video","audio","image","text","application"],Zr=["application/pdf","audio/mpeg","audio/wav","image/png","image/jpeg","image/gif","video/mp4","video/mpeg","text/html"],Jr=["en","wl","ww"],Je={index:"o",slot:"s",mac:"x",pci:"p"},Xr=["SUN","MON","TUE","WED","THU","FRI","SAT"],pe=class extends p{fileName(e={}){let{extensionCount:r=1}=e,t=this.faker.word.words().toLowerCase().replaceAll(/\W/g,"_"),a=this.faker.helpers.multiple(()=>this.fileExt(),{count:r}).join(".");return a.length===0?t:`${t}.${a}`}commonFileName(e){return`${this.fileName({extensionCount:0})}.${e||this.commonFileExt()}`}mimeType(){let e=Object.keys(this.faker.definitions.system.mime_type);return this.faker.helpers.arrayElement(e)}commonFileType(){return this.faker.helpers.arrayElement(Yr)}commonFileExt(){return this.fileExt(this.faker.helpers.arrayElement(Zr))}fileType(){let e=this.faker.definitions.system.mime_type,r=new Set(Object.keys(e).map(t=>t.split("/")[0]));return this.faker.helpers.arrayElement([...r])}fileExt(e){let r=this.faker.definitions.system.mime_type;if(typeof e=="string")return this.faker.helpers.arrayElement(r[e].extensions);let t=new Set(Object.values(r).flatMap(({extensions:a})=>a));return this.faker.helpers.arrayElement([...t])}directoryPath(){let e=this.faker.definitions.system.directory_path;return this.faker.helpers.arrayElement(e)}filePath(){return`${this.directoryPath()}/${this.fileName()}`}semver(){return[this.faker.number.int(9),this.faker.number.int(20),this.faker.number.int(20)].join(".")}networkInterface(e={}){let{interfaceType:r=this.faker.helpers.arrayElement(Jr),interfaceSchema:t=this.faker.helpers.objectKey(Je)}=e,a,n="";switch(t){case"index":{a=this.faker.string.numeric();break}case"slot":{a=`${this.faker.string.numeric()}${_nullishCoalesce(this.faker.helpers.maybe(()=>`f${this.faker.string.numeric()}`), () => (""))}${_nullishCoalesce(this.faker.helpers.maybe(()=>`d${this.faker.string.numeric()}`), () => (""))}`;break}case"mac":{a=this.faker.internet.mac("");break}case"pci":{n=_nullishCoalesce(this.faker.helpers.maybe(()=>`P${this.faker.string.numeric()}`), () => ("")),a=`${this.faker.string.numeric()}s${this.faker.string.numeric()}${_nullishCoalesce(this.faker.helpers.maybe(()=>`f${this.faker.string.numeric()}`), () => (""))}${_nullishCoalesce(this.faker.helpers.maybe(()=>`d${this.faker.string.numeric()}`), () => (""))}`;break}}return`${n}${r}${Je[t]}${a}`}cron(e={}){let{includeYear:r=!1,includeNonStandard:t=!1}=e,a=[this.faker.number.int(59),"*"],n=[this.faker.number.int(23),"*"],o=[this.faker.number.int({min:1,max:31}),"*","?"],s=[this.faker.number.int({min:1,max:12}),"*"],l=[this.faker.number.int(6),this.faker.helpers.arrayElement(Xr),"*","?"],c=[this.faker.number.int({min:1970,max:2099}),"*"],u=this.faker.helpers.arrayElement(a),h=this.faker.helpers.arrayElement(n),f=this.faker.helpers.arrayElement(o),g=this.faker.helpers.arrayElement(s),b=this.faker.helpers.arrayElement(l),y=this.faker.helpers.arrayElement(c),N=`${u} ${h} ${f} ${g} ${b}`;r&&(N+=` ${y}`);let k=["@annually","@daily","@hourly","@monthly","@reboot","@weekly","@yearly"];return!t||this.faker.datatype.boolean()?N:this.faker.helpers.arrayElement(k)}};var he=class extends p{vehicle(){return`${this.manufacturer()} ${this.model()}`}manufacturer(){return this.faker.helpers.arrayElement(this.faker.definitions.vehicle.manufacturer)}model(){return this.faker.helpers.arrayElement(this.faker.definitions.vehicle.model)}type(){return this.faker.helpers.arrayElement(this.faker.definitions.vehicle.type)}fuel(){return this.faker.helpers.arrayElement(this.faker.definitions.vehicle.fuel)}vin(){let e=["o","i","q","O","I","Q"];return`${this.faker.string.alphanumeric({length:10,casing:"upper",exclude:e})}${this.faker.string.alpha({length:1,casing:"upper",exclude:e})}${this.faker.string.alphanumeric({length:1,casing:"upper",exclude:e})}${this.faker.string.numeric({length:5,allowLeadingZeros:!0})}`}color(){return this.faker.color.human()}vrm(){return`${this.faker.string.alpha({length:2,casing:"upper"})}${this.faker.string.numeric({length:2,allowLeadingZeros:!0})}${this.faker.string.alpha({length:3,casing:"upper"})}`}bicycle(){return this.faker.helpers.arrayElement(this.faker.definitions.vehicle.bicycle_type)}};var fe=class extends p{adjective(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.adjective}))}adverb(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.adverb}))}conjunction(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.conjunction}))}interjection(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.interjection}))}noun(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.noun}))}preposition(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.preposition}))}verb(e={}){return typeof e=="number"&&(e={length:e}),this.faker.helpers.arrayElement(T({...e,wordList:this.faker.definitions.word.verb}))}sample(e={}){let r=this.faker.helpers.shuffle([this.adjective,this.adverb,this.conjunction,this.interjection,this.noun,this.preposition,this.verb]);for(let t of r)try{return t(e)}catch (e7){continue}throw new m("No matching word data available for the current locale")}words(e={}){typeof e=="number"&&(e={count:e});let{count:r={min:1,max:3}}=e;return this.faker.helpers.multiple(()=>this.sample(),{count:r}).join(" ")}};var Xe= (_class2 =class extends ${__init7() {this.airline=new F(this)}__init8() {this.animal=new X(this)}__init9() {this.book=new Q(this)}__init10() {this.color=new G(this)}__init11() {this.commerce=new q(this)}__init12() {this.company=new ee(this)}__init13() {this.database=new re(this)}__init14() {this.date=new V(this)}__init15() {this.finance=new te(this)}__init16() {this.food=new ae(this)}__init17() {this.git=new ne(this)}__init18() {this.hacker=new ie(this)}__init19() {this.helpers=new z(this)}__init20() {this.image=new oe(this)}__init21() {this.internet=new O(this)}__init22() {this.location=new se(this)}__init23() {this.lorem=new ce(this)}__init24() {this.music=new le(this)}__init25() {this.person=new U(this)}__init26() {this.phone=new me(this)}__init27() {this.science=new ue(this)}__init28() {this.system=new pe(this)}__init29() {this.vehicle=new he(this)}__init30() {this.word=new fe(this)}get address(){return w({deprecated:"faker.address",proposed:"faker.location",since:"8.0",until:"10.0"}),this.location}get name(){return w({deprecated:"faker.name",proposed:"faker.person",since:"8.0",until:"10.0"}),this.person}constructor(e){super({randomizer:e.randomizer,seed:e.seed});_class2.prototype.__init7.call(this);_class2.prototype.__init8.call(this);_class2.prototype.__init9.call(this);_class2.prototype.__init10.call(this);_class2.prototype.__init11.call(this);_class2.prototype.__init12.call(this);_class2.prototype.__init13.call(this);_class2.prototype.__init14.call(this);_class2.prototype.__init15.call(this);_class2.prototype.__init16.call(this);_class2.prototype.__init17.call(this);_class2.prototype.__init18.call(this);_class2.prototype.__init19.call(this);_class2.prototype.__init20.call(this);_class2.prototype.__init21.call(this);_class2.prototype.__init22.call(this);_class2.prototype.__init23.call(this);_class2.prototype.__init24.call(this);_class2.prototype.__init25.call(this);_class2.prototype.__init26.call(this);_class2.prototype.__init27.call(this);_class2.prototype.__init28.call(this);_class2.prototype.__init29.call(this);_class2.prototype.__init30.call(this);;let{locale:r}=e;if(Array.isArray(r)){if(r.length===0)throw new m("The locale option must contain at least one locale definition.");r=We(r)}this.rawDefinitions=r,this.definitions=Ue(this.rawDefinitions)}getMetadata(){return _nullishCoalesce(this.rawDefinitions.metadata, () => ({}))}}, _class2);var Qe=["Academy Color Encoding System (ACES)","Adobe RGB","Adobe Wide Gamut RGB","British Standard Colour (BS)","CIE 1931 XYZ","CIELAB","CIELUV","CIEUVW","CMY","CMYK","DCI-P3","Display-P3","Federal Standard 595C","HKS","HSL","HSLA","HSLuv","HSV","HWB","LCh","LMS","Munsell Color System","Natural Color System (NSC)","Pantone Matching System (PMS)","ProPhoto RGB Color Space","RAL","RG","RGBA","RGK","Rec. 2020","Rec. 2100","Rec. 601","Rec. 709","Uniform Color Spaces (UCSs)","YDbDr","YIQ","YPbPr","sRGB","sYCC","scRGB","xvYCC"];var Qr={space:Qe},qe=Qr;var er=["ascii_bin","ascii_general_ci","cp1250_bin","cp1250_general_ci","utf8_bin","utf8_general_ci","utf8_unicode_ci"];var rr=["ARCHIVE","BLACKHOLE","CSV","InnoDB","MEMORY","MyISAM"];var tr=["bigint","binary","bit","blob","boolean","date","datetime","decimal","double","enum","float","geometry","int","mediumint","point","real","serial","set","smallint","text","time","timestamp","tinyint","varchar"];var qr={collation:er,engine:rr,type:tr},ar=qr;var I=["Africa/Abidjan","Africa/Accra","Africa/Addis_Ababa","Africa/Algiers","Africa/Asmara","Africa/Bamako","Africa/Bangui","Africa/Banjul","Africa/Bissau","Africa/Blantyre","Africa/Brazzaville","Africa/Bujumbura","Africa/Cairo","Africa/Casablanca","Africa/Ceuta","Africa/Conakry","Africa/Dakar","Africa/Dar_es_Salaam","Africa/Djibouti","Africa/Douala","Africa/El_Aaiun","Africa/Freetown","Africa/Gaborone","Africa/Harare","Africa/Johannesburg","Africa/Juba","Africa/Kampala","Africa/Khartoum","Africa/Kigali","Africa/Kinshasa","Africa/Lagos","Africa/Libreville","Africa/Lome","Africa/Luanda","Africa/Lubumbashi","Africa/Lusaka","Africa/Malabo","Africa/Maputo","Africa/Maseru","Africa/Mbabane","Africa/Mogadishu","Africa/Monrovia","Africa/Nairobi","Africa/Ndjamena","Africa/Niamey","Africa/Nouakchott","Africa/Ouagadougou","Africa/Porto-Novo","Africa/Sao_Tome","Africa/Tripoli","Africa/Tunis","Africa/Windhoek","America/Adak","America/Anchorage","America/Anguilla","America/Antigua","America/Araguaina","America/Argentina/Buenos_Aires","America/Argentina/Catamarca","America/Argentina/Cordoba","America/Argentina/Jujuy","America/Argentina/La_Rioja","America/Argentina/Mendoza","America/Argentina/Rio_Gallegos","America/Argentina/Salta","America/Argentina/San_Juan","America/Argentina/San_Luis","America/Argentina/Tucuman","America/Argentina/Ushuaia","America/Aruba","America/Asuncion","America/Atikokan","America/Bahia","America/Bahia_Banderas","America/Barbados","America/Belem","America/Belize","America/Blanc-Sablon","America/Boa_Vista","America/Bogota","America/Boise","America/Cambridge_Bay","America/Campo_Grande","America/Cancun","America/Caracas","America/Cayenne","America/Cayman","America/Chicago","America/Chihuahua","America/Ciudad_Juarez","America/Costa_Rica","America/Creston","America/Cuiaba","America/Curacao","America/Danmarkshavn","America/Dawson","America/Dawson_Creek","America/Denver","America/Detroit","America/Dominica","America/Edmonton","America/Eirunepe","America/El_Salvador","America/Fort_Nelson","America/Fortaleza","America/Glace_Bay","America/Goose_Bay","America/Grand_Turk","America/Grenada","America/Guadeloupe","America/Guatemala","America/Guayaquil","America/Guyana","America/Halifax","America/Havana","America/Hermosillo","America/Indiana/Indianapolis","America/Indiana/Knox","America/Indiana/Marengo","America/Indiana/Petersburg","America/Indiana/Tell_City","America/Indiana/Vevay","America/Indiana/Vincennes","America/Indiana/Winamac","America/Inuvik","America/Iqaluit","America/Jamaica","America/Juneau","America/Kentucky/Louisville","America/Kentucky/Monticello","America/Kralendijk","America/La_Paz","America/Lima","America/Los_Angeles","America/Lower_Princes","America/Maceio","America/Managua","America/Manaus","America/Marigot","America/Martinique","America/Matamoros","America/Mazatlan","America/Menominee","America/Merida","America/Metlakatla","America/Mexico_City","America/Miquelon","America/Moncton","America/Monterrey","America/Montevideo","America/Montserrat","America/Nassau","America/New_York","America/Nome","America/Noronha","America/North_Dakota/Beulah","America/North_Dakota/Center","America/North_Dakota/New_Salem","America/Nuuk","America/Ojinaga","America/Panama","America/Paramaribo","America/Phoenix","America/Port-au-Prince","America/Port_of_Spain","America/Porto_Velho","America/Puerto_Rico","America/Punta_Arenas","America/Rankin_Inlet","America/Recife","America/Regina","America/Resolute","America/Rio_Branco","America/Santarem","America/Santiago","America/Santo_Domingo","America/Sao_Paulo","America/Scoresbysund","America/Sitka","America/St_Barthelemy","America/St_Johns","America/St_Kitts","America/St_Lucia","America/St_Thomas","America/St_Vincent","America/Swift_Current","America/Tegucigalpa","America/Thule","America/Tijuana","America/Toronto","America/Tortola","America/Vancouver","America/Whitehorse","America/Winnipeg","America/Yakutat","America/Yellowknife","Antarctica/Casey","Antarctica/Davis","Antarctica/DumontDUrville","Antarctica/Macquarie","Antarctica/Mawson","Antarctica/McMurdo","Antarctica/Palmer","Antarctica/Rothera","Antarctica/Syowa","Antarctica/Troll","Antarctica/Vostok","Arctic/Longyearbyen","Asia/Aden","Asia/Almaty","Asia/Amman","Asia/Anadyr","Asia/Aqtau","Asia/Aqtobe","Asia/Ashgabat","Asia/Atyrau","Asia/Baghdad","Asia/Bahrain","Asia/Baku","Asia/Bangkok","Asia/Barnaul","Asia/Beirut","Asia/Bishkek","Asia/Brunei","Asia/Chita","Asia/Choibalsan","Asia/Colombo","Asia/Damascus","Asia/Dhaka","Asia/Dili","Asia/Dubai","Asia/Dushanbe","Asia/Famagusta","Asia/Gaza","Asia/Hebron","Asia/Ho_Chi_Minh","Asia/Hong_Kong","Asia/Hovd","Asia/Irkutsk","Asia/Jakarta","Asia/Jayapura","Asia/Jerusalem","Asia/Kabul","Asia/Kamchatka","Asia/Karachi","Asia/Kathmandu","Asia/Khandyga","Asia/Kolkata","Asia/Krasnoyarsk","Asia/Kuala_Lumpur","Asia/Kuching","Asia/Kuwait","Asia/Macau","Asia/Magadan","Asia/Makassar","Asia/Manila","Asia/Muscat","Asia/Nicosia","Asia/Novokuznetsk","Asia/Novosibirsk","Asia/Omsk","Asia/Oral","Asia/Phnom_Penh","Asia/Pontianak","Asia/Pyongyang","Asia/Qatar","Asia/Qostanay","Asia/Qyzylorda","Asia/Riyadh","Asia/Sakhalin","Asia/Samarkand","Asia/Seoul","Asia/Shanghai","Asia/Singapore","Asia/Srednekolymsk","Asia/Taipei","Asia/Tashkent","Asia/Tbilisi","Asia/Tehran","Asia/Thimphu","Asia/Tokyo","Asia/Tomsk","Asia/Ulaanbaatar","Asia/Urumqi","Asia/Ust-Nera","Asia/Vientiane","Asia/Vladivostok","Asia/Yakutsk","Asia/Yangon","Asia/Yekaterinburg","Asia/Yerevan","Atlantic/Azores","Atlantic/Bermuda","Atlantic/Canary","Atlantic/Cape_Verde","Atlantic/Faroe","Atlantic/Madeira","Atlantic/Reykjavik","Atlantic/South_Georgia","Atlantic/St_Helena","Atlantic/Stanley","Australia/Adelaide","Australia/Brisbane","Australia/Broken_Hill","Australia/Darwin","Australia/Eucla","Australia/Hobart","Australia/Lindeman","Australia/Lord_Howe","Australia/Melbourne","Australia/Perth","Australia/Sydney","Europe/Amsterdam","Europe/Andorra","Europe/Astrakhan","Europe/Athens","Europe/Belgrade","Europe/Berlin","Europe/Bratislava","Europe/Brussels","Europe/Bucharest","Europe/Budapest","Europe/Busingen","Europe/Chisinau","Europe/Copenhagen","Europe/Dublin","Europe/Gibraltar","Europe/Guernsey","Europe/Helsinki","Europe/Isle_of_Man","Europe/Istanbul","Europe/Jersey","Europe/Kaliningrad","Europe/Kirov","Europe/Kyiv","Europe/Lisbon","Europe/Ljubljana","Europe/London","Europe/Luxembourg","Europe/Madrid","Europe/Malta","Europe/Mariehamn","Europe/Minsk","Europe/Monaco","Europe/Moscow","Europe/Oslo","Europe/Paris","Europe/Podgorica","Europe/Prague","Europe/Riga","Europe/Rome","Europe/Samara","Europe/San_Marino","Europe/Sarajevo","Europe/Saratov","Europe/Simferopol","Europe/Skopje","Europe/Sofia","Europe/Stockholm","Europe/Tallinn","Europe/Tirane","Europe/Ulyanovsk","Europe/Vaduz","Europe/Vatican","Europe/Vienna","Europe/Vilnius","Europe/Volgograd","Europe/Warsaw","Europe/Zagreb","Europe/Zurich","Indian/Antananarivo","Indian/Chagos","Indian/Christmas","Indian/Cocos","Indian/Comoro","Indian/Kerguelen","Indian/Mahe","Indian/Maldives","Indian/Mauritius","Indian/Mayotte","Indian/Reunion","Pacific/Apia","Pacific/Auckland","Pacific/Bougainville","Pacific/Chatham","Pacific/Chuuk","Pacific/Easter","Pacific/Efate","Pacific/Fakaofo","Pacific/Fiji","Pacific/Funafuti","Pacific/Galapagos","Pacific/Gambier","Pacific/Guadalcanal","Pacific/Guam","Pacific/Honolulu","Pacific/Kanton","Pacific/Kiritimati","Pacific/Kosrae","Pacific/Kwajalein","Pacific/Majuro","Pacific/Marquesas","Pacific/Midway","Pacific/Nauru","Pacific/Niue","Pacific/Norfolk","Pacific/Noumea","Pacific/Pago_Pago","Pacific/Palau","Pacific/Pitcairn","Pacific/Pohnpei","Pacific/Port_Moresby","Pacific/Rarotonga","Pacific/Saipan","Pacific/Tahiti","Pacific/Tarawa","Pacific/Tongatapu","Pacific/Wake","Pacific/Wallis"];var et={time_zone:I},nr=et;var ir=["ADP","AGP","AI","API","ASCII","CLI","COM","CSS","DNS","DRAM","EXE","FTP","GB","HDD","HEX","HTTP","IB","IP","JBOD","JSON","OCR","PCI","PNG","RAM","RSS","SAS","SCSI","SDD","SMS","SMTP","SQL","SSD","SSL","TCP","THX","TLS","UDP","USB","UTF8","VGA","XML","XSS"];var rt={abbreviation:ir},or=rt;var sr={smiley:["\u2620\uFE0F","\u2639\uFE0F","\u263A\uFE0F","\u2763\uFE0F","\u2764\uFE0F","\u2764\uFE0F\u200D\u{1F525}","\u2764\uFE0F\u200D\u{1FA79}","\u{1F441}\uFE0F\u200D\u{1F5E8}\uFE0F","\u{1F479}","\u{1F47A}","\u{1F47B}","\u{1F47D}","\u{1F47E}","\u{1F47F}","\u{1F480}","\u{1F48B}","\u{1F48C}","\u{1F493}","\u{1F494}","\u{1F495}","\u{1F496}","\u{1F497}","\u{1F498}","\u{1F499}","\u{1F49A}","\u{1F49B}","\u{1F49C}","\u{1F49D}","\u{1F49E}","\u{1F49F}","\u{1F4A2}","\u{1F4A3}","\u{1F4A4}","\u{1F4A5}","\u{1F4A6}","\u{1F4A8}","\u{1F4A9}","\u{1F4AB}","\u{1F4AC}","\u{1F4AD}","\u{1F4AF}","\u{1F573}\uFE0F","\u{1F5A4}","\u{1F5E8}\uFE0F","\u{1F5EF}\uFE0F","\u{1F600}","\u{1F601}","\u{1F602}","\u{1F603}","\u{1F604}","\u{1F605}","\u{1F606}","\u{1F607}","\u{1F608}","\u{1F609}","\u{1F60A}","\u{1F60B}","\u{1F60C}","\u{1F60D}","\u{1F60E}","\u{1F60F}","\u{1F610}","\u{1F611}","\u{1F612}","\u{1F613}","\u{1F614}","\u{1F615}","\u{1F616}","\u{1F617}","\u{1F618}","\u{1F619}","\u{1F61A}","\u{1F61B}","\u{1F61C}","\u{1F61D}","\u{1F61E}","\u{1F61F}","\u{1F620}","\u{1F621}","\u{1F622}","\u{1F623}","\u{1F624}","\u{1F625}","\u{1F626}","\u{1F627}","\u{1F628}","\u{1F629}","\u{1F62A}","\u{1F62B}","\u{1F62C}","\u{1F62D}","\u{1F62E}","\u{1F62E}\u200D\u{1F4A8}","\u{1F62F}","\u{1F630}","\u{1F631}","\u{1F632}","\u{1F633}","\u{1F634}","\u{1F635}","\u{1F635}\u200D\u{1F4AB}","\u{1F636}","\u{1F636}\u200D\u{1F32B}\uFE0F","\u{1F637}","\u{1F638}","\u{1F639}","\u{1F63A}","\u{1F63B}","\u{1F63C}","\u{1F63D}","\u{1F63E}","\u{1F63F}","\u{1F640}","\u{1F641}","\u{1F642}","\u{1F643}","\u{1F644}","\u{1F648}","\u{1F649}","\u{1F64A}","\u{1F90D}","\u{1F90E}","\u{1F910}","\u{1F911}","\u{1F912}","\u{1F913}","\u{1F914}","\u{1F915}","\u{1F916}","\u{1F917}","\u{1F920}","\u{1F921}","\u{1F922}","\u{1F923}","\u{1F924}","\u{1F925}","\u{1F927}","\u{1F928}","\u{1F929}","\u{1F92A}","\u{1F92B}","\u{1F92C}","\u{1F92D}","\u{1F92E}","\u{1F92F}","\u{1F970}","\u{1F971}","\u{1F972}","\u{1F973}","\u{1F974}","\u{1F975}","\u{1F976}","\u{1F978}","\u{1F97A}","\u{1F9D0}","\u{1F9E1}"],body:["\u261D\u{1F3FB}","\u261D\u{1F3FC}","\u261D\u{1F3FD}","\u261D\u{1F3FE}","\u261D\u{1F3FF}","\u261D\uFE0F","\u270A","\u270A\u{1F3FB}","\u270A\u{1F3FC}","\u270A\u{1F3FD}","\u270A\u{1F3FE}","\u270A\u{1F3FF}","\u270B","\u270B\u{1F3FB}","\u270B\u{1F3FC}","\u270B\u{1F3FD}","\u270B\u{1F3FE}","\u270B\u{1F3FF}","\u270C\u{1F3FB}","\u270C\u{1F3FC}","\u270C\u{1F3FD}","\u270C\u{1F3FE}","\u270C\u{1F3FF}","\u270C\uFE0F","\u270D\u{1F3FB}","\u270D\u{1F3FC}","\u270D\u{1F3FD}","\u270D\u{1F3FE}","\u270D\u{1F3FF}","\u270D\uFE0F","\u{1F440}","\u{1F441}\uFE0F","\u{1F442}","\u{1F442}\u{1F3FB}","\u{1F442}\u{1F3FC}","\u{1F442}\u{1F3FD}","\u{1F442}\u{1F3FE}","\u{1F442}\u{1F3FF}","\u{1F443}","\u{1F443}\u{1F3FB}","\u{1F443}\u{1F3FC}","\u{1F443}\u{1F3FD}","\u{1F443}\u{1F3FE}","\u{1F443}\u{1F3FF}","\u{1F444}","\u{1F445}","\u{1F446}","\u{1F446}\u{1F3FB}","\u{1F446}\u{1F3FC}","\u{1F446}\u{1F3FD}","\u{1F446}\u{1F3FE}","\u{1F446}\u{1F3FF}","\u{1F447}","\u{1F447}\u{1F3FB}","\u{1F447}\u{1F3FC}","\u{1F447}\u{1F3FD}","\u{1F447}\u{1F3FE}","\u{1F447}\u{1F3FF}","\u{1F448}","\u{1F448}\u{1F3FB}","\u{1F448}\u{1F3FC}","\u{1F448}\u{1F3FD}","\u{1F448}\u{1F3FE}","\u{1F448}\u{1F3FF}","\u{1F449}","\u{1F449}\u{1F3FB}","\u{1F449}\u{1F3FC}","\u{1F449}\u{1F3FD}","\u{1F449}\u{1F3FE}","\u{1F449}\u{1F3FF}","\u{1F44A}","\u{1F44A}\u{1F3FB}","\u{1F44A}\u{1F3FC}","\u{1F44A}\u{1F3FD}","\u{1F44A}\u{1F3FE}","\u{1F44A}\u{1F3FF}","\u{1F44B}","\u{1F44B}\u{1F3FB}","\u{1F44B}\u{1F3FC}","\u{1F44B}\u{1F3FD}","\u{1F44B}\u{1F3FE}","\u{1F44B}\u{1F3FF}","\u{1F44C}","\u{1F44C}\u{1F3FB}","\u{1F44C}\u{1F3FC}","\u{1F44C}\u{1F3FD}","\u{1F44C}\u{1F3FE}","\u{1F44C}\u{1F3FF}","\u{1F44D}","\u{1F44D}\u{1F3FB}","\u{1F44D}\u{1F3FC}","\u{1F44D}\u{1F3FD}","\u{1F44D}\u{1F3FE}","\u{1F44D}\u{1F3FF}","\u{1F44E}","\u{1F44E}\u{1F3FB}","\u{1F44E}\u{1F3FC}","\u{1F44E}\u{1F3FD}","\u{1F44E}\u{1F3FE}","\u{1F44E}\u{1F3FF}","\u{1F44F}","\u{1F44F}\u{1F3FB}","\u{1F44F}\u{1F3FC}","\u{1F44F}\u{1F3FD}","\u{1F44F}\u{1F3FE}","\u{1F44F}\u{1F3FF}","\u{1F450}","\u{1F450}\u{1F3FB}","\u{1F450}\u{1F3FC}","\u{1F450}\u{1F3FD}","\u{1F450}\u{1F3FE}","\u{1F450}\u{1F3FF}","\u{1F485}","\u{1F485}\u{1F3FB}","\u{1F485}\u{1F3FC}","\u{1F485}\u{1F3FD}","\u{1F485}\u{1F3FE}","\u{1F485}\u{1F3FF}","\u{1F4AA}","\u{1F4AA}\u{1F3FB}","\u{1F4AA}\u{1F3FC}","\u{1F4AA}\u{1F3FD}","\u{1F4AA}\u{1F3FE}","\u{1F4AA}\u{1F3FF}","\u{1F590}\u{1F3FB}","\u{1F590}\u{1F3FC}","\u{1F590}\u{1F3FD}","\u{1F590}\u{1F3FE}","\u{1F590}\u{1F3FF}","\u{1F590}\uFE0F","\u{1F595}","\u{1F595}\u{1F3FB}","\u{1F595}\u{1F3FC}","\u{1F595}\u{1F3FD}","\u{1F595}\u{1F3FE}","\u{1F595}\u{1F3FF}","\u{1F596}","\u{1F596}\u{1F3FB}","\u{1F596}\u{1F3FC}","\u{1F596}\u{1F3FD}","\u{1F596}\u{1F3FE}","\u{1F596}\u{1F3FF}","\u{1F64C}","\u{1F64C}\u{1F3FB}","\u{1F64C}\u{1F3FC}","\u{1F64C}\u{1F3FD}","\u{1F64C}\u{1F3FE}","\u{1F64C}\u{1F3FF}","\u{1F64F}","\u{1F64F}\u{1F3FB}","\u{1F64F}\u{1F3FC}","\u{1F64F}\u{1F3FD}","\u{1F64F}\u{1F3FE}","\u{1F64F}\u{1F3FF}","\u{1F90C}","\u{1F90C}\u{1F3FB}","\u{1F90C}\u{1F3FC}","\u{1F90C}\u{1F3FD}","\u{1F90C}\u{1F3FE}","\u{1F90C}\u{1F3FF}","\u{1F90F}","\u{1F90F}\u{1F3FB}","\u{1F90F}\u{1F3FC}","\u{1F90F}\u{1F3FD}","\u{1F90F}\u{1F3FE}","\u{1F90F}\u{1F3FF}","\u{1F918}","\u{1F918}\u{1F3FB}","\u{1F918}\u{1F3FC}","\u{1F918}\u{1F3FD}","\u{1F918}\u{1F3FE}","\u{1F918}\u{1F3FF}","\u{1F919}","\u{1F919}\u{1F3FB}","\u{1F919}\u{1F3FC}","\u{1F919}\u{1F3FD}","\u{1F919}\u{1F3FE}","\u{1F919}\u{1F3FF}","\u{1F91A}","\u{1F91A}\u{1F3FB}","\u{1F91A}\u{1F3FC}","\u{1F91A}\u{1F3FD}","\u{1F91A}\u{1F3FE}","\u{1F91A}\u{1F3FF}","\u{1F91B}","\u{1F91B}\u{1F3FB}","\u{1F91B}\u{1F3FC}","\u{1F91B}\u{1F3FD}","\u{1F91B}\u{1F3FE}","\u{1F91B}\u{1F3FF}","\u{1F91C}","\u{1F91C}\u{1F3FB}","\u{1F91C}\u{1F3FC}","\u{1F91C}\u{1F3FD}","\u{1F91C}\u{1F3FE}","\u{1F91C}\u{1F3FF}","\u{1F91D}","\u{1F91E}","\u{1F91E}\u{1F3FB}","\u{1F91E}\u{1F3FC}","\u{1F91E}\u{1F3FD}","\u{1F91E}\u{1F3FE}","\u{1F91E}\u{1F3FF}","\u{1F91F}","\u{1F91F}\u{1F3FB}","\u{1F91F}\u{1F3FC}","\u{1F91F}\u{1F3FD}","\u{1F91F}\u{1F3FE}","\u{1F91F}\u{1F3FF}","\u{1F932}","\u{1F932}\u{1F3FB}","\u{1F932}\u{1F3FC}","\u{1F932}\u{1F3FD}","\u{1F932}\u{1F3FE}","\u{1F932}\u{1F3FF}","\u{1F933}","\u{1F933}\u{1F3FB}","\u{1F933}\u{1F3FC}","\u{1F933}\u{1F3FD}","\u{1F933}\u{1F3FE}","\u{1F933}\u{1F3FF}","\u{1F9B4}","\u{1F9B5}","\u{1F9B5}\u{1F3FB}","\u{1F9B5}\u{1F3FC}","\u{1F9B5}\u{1F3FD}","\u{1F9B5}\u{1F3FE}","\u{1F9B5}\u{1F3FF}","\u{1F9B6}","\u{1F9B6}\u{1F3FB}","\u{1F9B6}\u{1F3FC}","\u{1F9B6}\u{1F3FD}","\u{1F9B6}\u{1F3FE}","\u{1F9B6}\u{1F3FF}","\u{1F9B7}","\u{1F9BB}","\u{1F9BB}\u{1F3FB}","\u{1F9BB}\u{1F3FC}","\u{1F9BB}\u{1F3FD}","\u{1F9BB}\u{1F3FE}","\u{1F9BB}\u{1F3FF}","\u{1F9BE}","\u{1F9BF}","\u{1F9E0}","\u{1FAC0}","\u{1FAC1}"],person:["\u{1F385}","\u{1F385}\u{1F3FB}","\u{1F385}\u{1F3FC}","\u{1F385}\u{1F3FD}","\u{1F385}\u{1F3FE}","\u{1F385}\u{1F3FF}","\u{1F466}","\u{1F466}\u{1F3FB}","\u{1F466}\u{1F3FC}","\u{1F466}\u{1F3FD}","\u{1F466}\u{1F3FE}","\u{1F466}\u{1F3FF}","\u{1F467}","\u{1F467}\u{1F3FB}","\u{1F467}\u{1F3FC}","\u{1F467}\u{1F3FD}","\u{1F467}\u{1F3FE}","\u{1F467}\u{1F3FF}","\u{1F468}","\u{1F468}\u200D\u2695\uFE0F","\u{1F468}\u200D\u2696\uFE0F","\u{1F468}\u200D\u2708\uFE0F","\u{1F468}\u200D\u{1F33E}","\u{1F468}\u200D\u{1F373}","\u{1F468}\u200D\u{1F37C}","\u{1F468}\u200D\u{1F393}","\u{1F468}\u200D\u{1F3A4}","\u{1F468}\u200D\u{1F3A8}","\u{1F468}\u200D\u{1F3EB}","\u{1F468}\u200D\u{1F3ED}","\u{1F468}\u200D\u{1F4BB}","\u{1F468}\u200D\u{1F4BC}","\u{1F468}\u200D\u{1F527}","\u{1F468}\u200D\u{1F52C}","\u{1F468}\u200D\u{1F680}","\u{1F468}\u200D\u{1F692}","\u{1F468}\u200D\u{1F9B0}","\u{1F468}\u200D\u{1F9B1}","\u{1F468}\u200D\u{1F9B2}","\u{1F468}\u200D\u{1F9B3}","\u{1F468}\u{1F3FB}","\u{1F468}\u{1F3FB}\u200D\u2695\uFE0F","\u{1F468}\u{1F3FB}\u200D\u2696\uFE0F","\u{1F468}\u{1F3FB}\u200D\u2708\uFE0F","\u{1F468}\u{1F3FB}\u200D\u{1F33E}","\u{1F468}\u{1F3FB}\u200D\u{1F373}","\u{1F468}\u{1F3FB}\u200D\u{1F37C}","\u{1F468}\u{1F3FB}\u200D\u{1F393}","\u{1F468}\u{1F3FB}\u200D\u{1F3A4}","\u{1F468}\u{1F3FB}\u200D\u{1F3A8}","\u{1F468}\u{1F3FB}\u200D\u{1F3EB}","\u{1F468}\u{1F3FB}\u200D\u{1F3ED}","\u{1F468}\u{1F3FB}\u200D\u{1F4BB}","\u{1F468}\u{1F3FB}\u200D\u{1F4BC}","\u{1F468}\u{1F3FB}\u200D\u{1F527}","\u{1F468}\u{1F3FB}\u200D\u{1F52C}","\u{1F468}\u{1F3FB}\u200D\u{1F680}","\u{1F468}\u{1F3FB}\u200D\u{1F692}","\u{1F468}\u{1F3FB}\u200D\u{1F9B0}","\u{1F468}\u{1F3FB}\u200D\u{1F9B1}","\u{1F468}\u{1F3FB}\u200D\u{1F9B2}","\u{1F468}\u{1F3FB}\u200D\u{1F9B3}","\u{1F468}\u{1F3FC}","\u{1F468}\u{1F3FC}\u200D\u2695\uFE0F","\u{1F468}\u{1F3FC}\u200D\u2696\uFE0F","\u{1F468}\u{1F3FC}\u200D\u2708\uFE0F","\u{1F468}\u{1F3FC}\u200D\u{1F33E}","\u{1F468}\u{1F3FC}\u200D\u{1F373}","\u{1F468}\u{1F3FC}\u200D\u{1F37C}","\u{1F468}\u{1F3FC}\u200D\u{1F393}","\u{1F468}\u{1F3FC}\u200D\u{1F3A4}","\u{1F468}\u{1F3FC}\u200D\u{1F3A8}","\u{1F468}\u{1F3FC}\u200D\u{1F3EB}","\u{1F468}\u{1F3FC}\u200D\u{1F3ED}","\u{1F468}\u{1F3FC}\u200D\u{1F4BB}","\u{1F468}\u{1F3FC}\u200D\u{1F4BC}","\u{1F468}\u{1F3FC}\u200D\u{1F527}","\u{1F468}\u{1F3FC}\u200D\u{1F52C}","\u{1F468}\u{1F3FC}\u200D\u{1F680}","\u{1F468}\u{1F3FC}\u200D\u{1F692}","\u{1F468}\u{1F3FC}\u200D\u{1F9B0}","\u{1F468}\u{1F3FC}\u200D\u{1F9B1}","\u{1F468}\u{1F3FC}\u200D\u{1F9B2}","\u{1F468}\u{1F3FC}\u200D\u{1F9B3}","\u{1F468}\u{1F3FD}","\u{1F468}\u{1F3FD}\u200D\u2695\uFE0F","\u{1F468}\u{1F3FD}\u200D\u2696\uFE0F","\u{1F468}\u{1F3FD}\u200D\u2708\uFE0F","\u{1F468}\u{1F3FD}\u200D\u{1F33E}","\u{1F468}\u{1F3FD}\u200D\u{1F373}","\u{1F468}\u{1F3FD}\u200D\u{1F37C}","\u{1F468}\u{1F3FD}\u200D\u{1F393}","\u{1F468}\u{1F3FD}\u200D\u{1F3A4}","\u{1F468}\u{1F3FD}\u200D\u{1F3A8}","\u{1F468}\u{1F3FD}\u200D\u{1F3EB}","\u{1F468}\u{1F3FD}\u200D\u{1F3ED}","\u{1F468}\u{1F3FD}\u200D\u{1F4BB}","\u{1F468}\u{1F3FD}\u200D\u{1F4BC}","\u{1F468}\u{1F3FD}\u200D\u{1F527}","\u{1F468}\u{1F3FD}\u200D\u{1F52C}","\u{1F468}\u{1F3FD}\u200D\u{1F680}","\u{1F468}\u{1F3FD}\u200D\u{1F692}","\u{1F468}\u{1F3FD}\u200D\u{1F9B0}","\u{1F468}\u{1F3FD}\u200D\u{1F9B1}","\u{1F468}\u{1F3FD}\u200D\u{1F9B2}","\u{1F468}\u{1F3FD}\u200D\u{1F9B3}","\u{1F468}\u{1F3FE}","\u{1F468}\u{1F3FE}\u200D\u2695\uFE0F","\u{1F468}\u{1F3FE}\u200D\u2696\uFE0F","\u{1F468}\u{1F3FE}\u200D\u2708\uFE0F","\u{1F468}\u{1F3FE}\u200D\u{1F33E}","\u{1F468}\u{1F3FE}\u200D\u{1F373}","\u{1F468}\u{1F3FE}\u200D\u{1F37C}","\u{1F468}\u{1F3FE}\u200D\u{1F393}","\u{1F468}\u{1F3FE}\u200D\u{1F3A4}","\u{1F468}\u{1F3FE}\u200D\u{1F3A8}","\u{1F468}\u{1F3FE}\u200D\u{1F3EB}","\u{1F468}\u{1F3FE}\u200D\u{1F3ED}","\u{1F468}\u{1F3FE}\u200D\u{1F4BB}","\u{1F468}\u{1F3FE}\u200D\u{1F4BC}","\u{1F468}\u{1F3FE}\u200D\u{1F527}","\u{1F468}\u{1F3FE}\u200D\u{1F52C}","\u{1F468}\u{1F3FE}\u200D\u{1F680}","\u{1F468}\u{1F3FE}\u200D\u{1F692}","\u{1F468}\u{1F3FE}\u200D\u{1F9B0}","\u{1F468}\u{1F3FE}\u200D\u{1F9B1}","\u{1F468}\u{1F3FE}\u200D\u{1F9B2}","\u{1F468}\u{1F3FE}\u200D\u{1F9B3}","\u{1F468}\u{1F3FF}","\u{1F468}\u{1F3FF}\u200D\u2695\uFE0F","\u{1F468}\u{1F3FF}\u200D\u2696\uFE0F","\u{1F468}\u{1F3FF}\u200D\u2708\uFE0F","\u{1F468}\u{1F3FF}\u200D\u{1F33E}","\u{1F468}\u{1F3FF}\u200D\u{1F373}","\u{1F468}\u{1F3FF}\u200D\u{1F37C}","\u{1F468}\u{1F3FF}\u200D\u{1F393}","\u{1F468}\u{1F3FF}\u200D\u{1F3A4}","\u{1F468}\u{1F3FF}\u200D\u{1F3A8}","\u{1F468}\u{1F3FF}\u200D\u{1F3EB}","\u{1F468}\u{1F3FF}\u200D\u{1F3ED}","\u{1F468}\u{1F3FF}\u200D\u{1F4BB}","\u{1F468}\u{1F3FF}\u200D\u{1F4BC}","\u{1F468}\u{1F3FF}\u200D\u{1F527}","\u{1F468}\u{1F3FF}\u200D\u{1F52C}","\u{1F468}\u{1F3FF}\u200D\u{1F680}","\u{1F468}\u{1F3FF}\u200D\u{1F692}","\u{1F468}\u{1F3FF}\u200D\u{1F9B0}","\u{1F468}\u{1F3FF}\u200D\u{1F9B1}","\u{1F468}\u{1F3FF}\u200D\u{1F9B2}","\u{1F468}\u{1F3FF}\u200D\u{1F9B3}","\u{1F469}","\u{1F469}\u200D\u2695\uFE0F","\u{1F469}\u200D\u2696\uFE0F","\u{1F469}\u200D\u2708\uFE0F","\u{1F469}\u200D\u{1F33E}","\u{1F469}\u200D\u{1F373}","\u{1F469}\u200D\u{1F37C}","\u{1F469}\u200D\u{1F393}","\u{1F469}\u200D\u{1F3A4}","\u{1F469}\u200D\u{1F3A8}","\u{1F469}\u200D\u{1F3EB}","\u{1F469}\u200D\u{1F3ED}","\u{1F469}\u200D\u{1F4BB}","\u{1F469}\u200D\u{1F4BC}","\u{1F469}\u200D\u{1F527}","\u{1F469}\u200D\u{1F52C}","\u{1F469}\u200D\u{1F680}","\u{1F469}\u200D\u{1F692}","\u{1F469}\u200D\u{1F9B0}","\u{1F469}\u200D\u{1F9B1}","\u{1F469}\u200D\u{1F9B2}","\u{1F469}\u200D\u{1F9B3}","\u{1F469}\u{1F3FB}","\u{1F469}\u{1F3FB}\u200D\u2695\uFE0F","\u{1F469}\u{1F3FB}\u200D\u2696\uFE0F","\u{1F469}\u{1F3FB}\u200D\u2708\uFE0F","\u{1F469}\u{1F3FB}\u200D\u{1F33E}","\u{1F469}\u{1F3FB}\u200D\u{1F373}","\u{1F469}\u{1F3FB}\u200D\u{1F37C}","\u{1F469}\u{1F3FB}\u200D\u{1F393}","\u{1F469}\u{1F3FB}\u200D\u{1F3A4}","\u{1F469}\u{1F3FB}\u200D\u{1F3A8}","\u{1F469}\u{1F3FB}\u200D\u{1F3EB}","\u{1F469}\u{1F3FB}\u200D\u{1F3ED}","\u{1F469}\u{1F3FB}\u200D\u{1F4BB}","\u{1F469}\u{1F3FB}\u200D\u{1F4BC}","\u{1F469}\u{1F3FB}\u200D\u{1F527}","\u{1F469}\u{1F3FB}\u200D\u{1F52C}","\u{1F469}\u{1F3FB}\u200D\u{1F680}","\u{1F469}\u{1F3FB}\u200D\u{1F692}","\u{1F469}\u{1F3FB}\u200D\u{1F9B0}","\u{1F469}\u{1F3FB}\u200D\u{1F9B1}","\u{1F469}\u{1F3FB}\u200D\u{1F9B2}","\u{1F469}\u{1F3FB}\u200D\u{1F9B3}","\u{1F469}\u{1F3FC}","\u{1F469}\u{1F3FC}\u200D\u2695\uFE0F","\u{1F469}\u{1F3FC}\u200D\u2696\uFE0F","\u{1F469}\u{1F3FC}\u200D\u2708\uFE0F","\u{1F469}\u{1F3FC}\u200D\u{1F33E}","\u{1F469}\u{1F3FC}\u200D\u{1F373}","\u{1F469}\u{1F3FC}\u200D\u{1F37C}","\u{1F469}\u{1F3FC}\u200D\u{1F393}","\u{1F469}\u{1F3FC}\u200D\u{1F3A4}","\u{1F469}\u{1F3FC}\u200D\u{1F3A8}","\u{1F469}\u{1F3FC}\u200D\u{1F3EB}","\u{1F469}\u{1F3FC}\u200D\u{1F3ED}","\u{1F469}\u{1F3FC}\u200D\u{1F4BB}","\u{1F469}\u{1F3FC}\u200D\u{1F4BC}","\u{1F469}\u{1F3FC}\u200D\u{1F527}","\u{1F469}\u{1F3FC}\u200D\u{1F52C}","\u{1F469}\u{1F3FC}\u200D\u{1F680}","\u{1F469}\u{1F3FC}\u200D\u{1F692}","\u{1F469}\u{1F3FC}\u200D\u{1F9B0}","\u{1F469}\u{1F3FC}\u200D\u{1F9B1}","\u{1F469}\u{1F3FC}\u200D\u{1F9B2}","\u{1F469}\u{1F3FC}\u200D\u{1F9B3}","\u{1F469}\u{1F3FD}","\u{1F469}\u{1F3FD}\u200D\u2695\uFE0F","\u{1F469}\u{1F3FD}\u200D\u2696\uFE0F","\u{1F469}\u{1F3FD}\u200D\u2708\uFE0F","\u{1F469}\u{1F3FD}\u200D\u{1F33E}","\u{1F469}\u{1F3FD}\u200D\u{1F373}","\u{1F469}\u{1F3FD}\u200D\u{1F37C}","\u{1F469}\u{1F3FD}\u200D\u{1F393}","\u{1F469}\u{1F3FD}\u200D\u{1F3A4}","\u{1F469}\u{1F3FD}\u200D\u{1F3A8}","\u{1F469}\u{1F3FD}\u200D\u{1F3EB}","\u{1F469}\u{1F3FD}\u200D\u{1F3ED}","\u{1F469}\u{1F3FD}\u200D\u{1F4BB}","\u{1F469}\u{1F3FD}\u200D\u{1F4BC}","\u{1F469}\u{1F3FD}\u200D\u{1F527}","\u{1F469}\u{1F3FD}\u200D\u{1F52C}","\u{1F469}\u{1F3FD}\u200D\u{1F680}","\u{1F469}\u{1F3FD}\u200D\u{1F692}","\u{1F469}\u{1F3FD}\u200D\u{1F9B0}","\u{1F469}\u{1F3FD}\u200D\u{1F9B1}","\u{1F469}\u{1F3FD}\u200D\u{1F9B2}","\u{1F469}\u{1F3FD}\u200D\u{1F9B3}","\u{1F469}\u{1F3FE}","\u{1F469}\u{1F3FE}\u200D\u2695\uFE0F","\u{1F469}\u{1F3FE}\u200D\u2696\uFE0F","\u{1F469}\u{1F3FE}\u200D\u2708\uFE0F","\u{1F469}\u{1F3FE}\u200D\u{1F33E}","\u{1F469}\u{1F3FE}\u200D\u{1F373}","\u{1F469}\u{1F3FE}\u200D\u{1F37C}","\u{1F469}\u{1F3FE}\u200D\u{1F393}","\u{1F469}\u{1F3FE}\u200D\u{1F3A4}","\u{1F469}\u{1F3FE}\u200D\u{1F3A8}","\u{1F469}\u{1F3FE}\u200D\u{1F3EB}","\u{1F469}\u{1F3FE}\u200D\u{1F3ED}","\u{1F469}\u{1F3FE}\u200D\u{1F4BB}","\u{1F469}\u{1F3FE}\u200D\u{1F4BC}","\u{1F469}\u{1F3FE}\u200D\u{1F527}","\u{1F469}\u{1F3FE}\u200D\u{1F52C}","\u{1F469}\u{1F3FE}\u200D\u{1F680}","\u{1F469}\u{1F3FE}\u200D\u{1F692}","\u{1F469}\u{1F3FE}\u200D\u{1F9B0}","\u{1F469}\u{1F3FE}\u200D\u{1F9B1}","\u{1F469}\u{1F3FE}\u200D\u{1F9B2}","\u{1F469}\u{1F3FE}\u200D\u{1F9B3}","\u{1F469}\u{1F3FF}","\u{1F469}\u{1F3FF}\u200D\u2695\uFE0F","\u{1F469}\u{1F3FF}\u200D\u2696\uFE0F","\u{1F469}\u{1F3FF}\u200D\u2708\uFE0F","\u{1F469}\u{1F3FF}\u200D\u{1F33E}","\u{1F469}\u{1F3FF}\u200D\u{1F373}","\u{1F469}\u{1F3FF}\u200D\u{1F37C}","\u{1F469}\u{1F3FF}\u200D\u{1F393}","\u{1F469}\u{1F3FF}\u200D\u{1F3A4}","\u{1F469}\u{1F3FF}\u200D\u{1F3A8}","\u{1F469}\u{1F3FF}\u200D\u{1F3EB}","\u{1F469}\u{1F3FF}\u200D\u{1F3ED}","\u{1F469}\u{1F3FF}\u200D\u{1F4BB}","\u{1F469}\u{1F3FF}\u200D\u{1F4BC}","\u{1F469}\u{1F3FF}\u200D\u{1F527}","\u{1F469}\u{1F3FF}\u200D\u{1F52C}","\u{1F469}\u{1F3FF}\u200D\u{1F680}","\u{1F469}\u{1F3FF}\u200D\u{1F692}","\u{1F469}\u{1F3FF}\u200D\u{1F9B0}","\u{1F469}\u{1F3FF}\u200D\u{1F9B1}","\u{1F469}\u{1F3FF}\u200D\u{1F9B2}","\u{1F469}\u{1F3FF}\u200D\u{1F9B3}","\u{1F46E}","\u{1F46E}\u200D\u2640\uFE0F","\u{1F46E}\u200D\u2642\uFE0F","\u{1F46E}\u{1F3FB}","\u{1F46E}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F46E}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F46E}\u{1F3FC}","\u{1F46E}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F46E}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F46E}\u{1F3FD}","\u{1F46E}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F46E}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F46E}\u{1F3FE}","\u{1F46E}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F46E}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F46E}\u{1F3FF}","\u{1F46E}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F46E}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F470}","\u{1F470}\u200D\u2640\uFE0F","\u{1F470}\u200D\u2642\uFE0F","\u{1F470}\u{1F3FB}","\u{1F470}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F470}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F470}\u{1F3FC}","\u{1F470}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F470}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F470}\u{1F3FD}","\u{1F470}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F470}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F470}\u{1F3FE}","\u{1F470}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F470}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F470}\u{1F3FF}","\u{1F470}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F470}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F471}","\u{1F471}\u200D\u2640\uFE0F","\u{1F471}\u200D\u2642\uFE0F","\u{1F471}\u{1F3FB}","\u{1F471}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F471}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F471}\u{1F3FC}","\u{1F471}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F471}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F471}\u{1F3FD}","\u{1F471}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F471}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F471}\u{1F3FE}","\u{1F471}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F471}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F471}\u{1F3FF}","\u{1F471}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F471}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F472}","\u{1F472}\u{1F3FB}","\u{1F472}\u{1F3FC}","\u{1F472}\u{1F3FD}","\u{1F472}\u{1F3FE}","\u{1F472}\u{1F3FF}","\u{1F473}","\u{1F473}\u200D\u2640\uFE0F","\u{1F473}\u200D\u2642\uFE0F","\u{1F473}\u{1F3FB}","\u{1F473}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F473}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F473}\u{1F3FC}","\u{1F473}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F473}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F473}\u{1F3FD}","\u{1F473}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F473}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F473}\u{1F3FE}","\u{1F473}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F473}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F473}\u{1F3FF}","\u{1F473}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F473}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F474}","\u{1F474}\u{1F3FB}","\u{1F474}\u{1F3FC}","\u{1F474}\u{1F3FD}","\u{1F474}\u{1F3FE}","\u{1F474}\u{1F3FF}","\u{1F475}","\u{1F475}\u{1F3FB}","\u{1F475}\u{1F3FC}","\u{1F475}\u{1F3FD}","\u{1F475}\u{1F3FE}","\u{1F475}\u{1F3FF}","\u{1F476}","\u{1F476}\u{1F3FB}","\u{1F476}\u{1F3FC}","\u{1F476}\u{1F3FD}","\u{1F476}\u{1F3FE}","\u{1F476}\u{1F3FF}","\u{1F477}","\u{1F477}\u200D\u2640\uFE0F","\u{1F477}\u200D\u2642\uFE0F","\u{1F477}\u{1F3FB}","\u{1F477}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F477}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F477}\u{1F3FC}","\u{1F477}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F477}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F477}\u{1F3FD}","\u{1F477}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F477}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F477}\u{1F3FE}","\u{1F477}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F477}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F477}\u{1F3FF}","\u{1F477}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F477}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F478}","\u{1F478}\u{1F3FB}","\u{1F478}\u{1F3FC}","\u{1F478}\u{1F3FD}","\u{1F478}\u{1F3FE}","\u{1F478}\u{1F3FF}","\u{1F47C}","\u{1F47C}\u{1F3FB}","\u{1F47C}\u{1F3FC}","\u{1F47C}\u{1F3FD}","\u{1F47C}\u{1F3FE}","\u{1F47C}\u{1F3FF}","\u{1F481}","\u{1F481}\u200D\u2640\uFE0F","\u{1F481}\u200D\u2642\uFE0F","\u{1F481}\u{1F3FB}","\u{1F481}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F481}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F481}\u{1F3FC}","\u{1F481}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F481}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F481}\u{1F3FD}","\u{1F481}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F481}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F481}\u{1F3FE}","\u{1F481}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F481}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F481}\u{1F3FF}","\u{1F481}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F481}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F482}","\u{1F482}\u200D\u2640\uFE0F","\u{1F482}\u200D\u2642\uFE0F","\u{1F482}\u{1F3FB}","\u{1F482}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F482}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F482}\u{1F3FC}","\u{1F482}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F482}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F482}\u{1F3FD}","\u{1F482}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F482}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F482}\u{1F3FE}","\u{1F482}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F482}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F482}\u{1F3FF}","\u{1F482}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F482}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F486}","\u{1F486}\u200D\u2640\uFE0F","\u{1F486}\u200D\u2642\uFE0F","\u{1F486}\u{1F3FB}","\u{1F486}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F486}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F486}\u{1F3FC}","\u{1F486}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F486}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F486}\u{1F3FD}","\u{1F486}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F486}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F486}\u{1F3FE}","\u{1F486}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F486}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F486}\u{1F3FF}","\u{1F486}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F486}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F487}","\u{1F487}\u{1F3FB}","\u{1F487}\u{1F3FC}","\u{1F487}\u{1F3FD}","\u{1F575}\u{1F3FB}","\u{1F575}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F575}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F575}\u{1F3FC}","\u{1F575}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F575}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F575}\u{1F3FD}","\u{1F575}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F575}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F575}\u{1F3FE}","\u{1F575}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F575}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F575}\u{1F3FF}","\u{1F575}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F575}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F575}\uFE0F","\u{1F575}\uFE0F\u200D\u2640\uFE0F","\u{1F575}\uFE0F\u200D\u2642\uFE0F","\u{1F645}","\u{1F645}\u200D\u2640\uFE0F","\u{1F645}\u200D\u2642\uFE0F","\u{1F645}\u{1F3FB}","\u{1F645}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F645}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F645}\u{1F3FC}","\u{1F645}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F645}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F645}\u{1F3FD}","\u{1F645}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F645}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F645}\u{1F3FE}","\u{1F645}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F645}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F645}\u{1F3FF}","\u{1F645}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F645}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F646}","\u{1F646}\u200D\u2640\uFE0F","\u{1F646}\u200D\u2642\uFE0F","\u{1F646}\u{1F3FB}","\u{1F646}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F646}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F646}\u{1F3FC}","\u{1F646}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F646}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F646}\u{1F3FD}","\u{1F646}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F646}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F646}\u{1F3FE}","\u{1F646}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F646}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F646}\u{1F3FF}","\u{1F646}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F646}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F647}","\u{1F647}\u200D\u2640\uFE0F","\u{1F647}\u200D\u2642\uFE0F","\u{1F647}\u{1F3FB}","\u{1F647}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F647}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F647}\u{1F3FC}","\u{1F647}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F647}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F647}\u{1F3FD}","\u{1F647}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F647}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F647}\u{1F3FE}","\u{1F647}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F647}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F647}\u{1F3FF}","\u{1F647}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F647}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F64B}","\u{1F64B}\u200D\u2640\uFE0F","\u{1F64B}\u200D\u2642\uFE0F","\u{1F64B}\u{1F3FB}","\u{1F64B}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F64B}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F64B}\u{1F3FC}","\u{1F64B}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F64B}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F64B}\u{1F3FD}","\u{1F64B}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F64B}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F64B}\u{1F3FE}","\u{1F64B}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F64B}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F64B}\u{1F3FF}","\u{1F64B}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F64B}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F64D}","\u{1F64D}\u200D\u2640\uFE0F","\u{1F64D}\u200D\u2642\uFE0F","\u{1F64D}\u{1F3FB}","\u{1F64D}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F64D}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F64D}\u{1F3FC}","\u{1F64D}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F64D}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F64D}\u{1F3FD}","\u{1F64D}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F64D}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F64D}\u{1F3FE}","\u{1F64D}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F64D}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F64D}\u{1F3FF}","\u{1F64D}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F64D}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F64E}","\u{1F64E}\u200D\u2640\uFE0F","\u{1F64E}\u200D\u2642\uFE0F","\u{1F64E}\u{1F3FB}","\u{1F64E}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F64E}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F64E}\u{1F3FC}","\u{1F64E}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F64E}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F64E}\u{1F3FD}","\u{1F64E}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F64E}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F64E}\u{1F3FE}","\u{1F64E}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F64E}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F64E}\u{1F3FF}","\u{1F64E}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F64E}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F926}","\u{1F926}\u200D\u2640\uFE0F","\u{1F926}\u200D\u2642\uFE0F","\u{1F926}\u{1F3FB}","\u{1F926}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F926}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F926}\u{1F3FC}","\u{1F926}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F926}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F926}\u{1F3FD}","\u{1F926}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F926}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F926}\u{1F3FE}","\u{1F926}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F926}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F926}\u{1F3FF}","\u{1F926}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F926}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F930}","\u{1F930}\u{1F3FB}","\u{1F930}\u{1F3FC}","\u{1F930}\u{1F3FD}","\u{1F930}\u{1F3FE}","\u{1F930}\u{1F3FF}","\u{1F931}","\u{1F931}\u{1F3FB}","\u{1F931}\u{1F3FC}","\u{1F931}\u{1F3FD}","\u{1F931}\u{1F3FE}","\u{1F931}\u{1F3FF}","\u{1F934}","\u{1F934}\u{1F3FB}","\u{1F934}\u{1F3FC}","\u{1F934}\u{1F3FD}","\u{1F934}\u{1F3FE}","\u{1F934}\u{1F3FF}","\u{1F935}","\u{1F935}\u200D\u2640\uFE0F","\u{1F935}\u200D\u2642\uFE0F","\u{1F935}\u{1F3FB}","\u{1F935}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F935}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F935}\u{1F3FC}","\u{1F935}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F935}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F935}\u{1F3FD}","\u{1F935}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F935}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F935}\u{1F3FE}","\u{1F935}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F935}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F935}\u{1F3FF}","\u{1F935}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F935}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F936}","\u{1F936}\u{1F3FB}","\u{1F936}\u{1F3FC}","\u{1F936}\u{1F3FD}","\u{1F936}\u{1F3FE}","\u{1F936}\u{1F3FF}","\u{1F937}","\u{1F937}\u200D\u2640\uFE0F","\u{1F937}\u200D\u2642\uFE0F","\u{1F937}\u{1F3FB}","\u{1F937}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F937}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F937}\u{1F3FC}","\u{1F937}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F937}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F937}\u{1F3FD}","\u{1F937}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F937}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F937}\u{1F3FE}","\u{1F937}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F937}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F937}\u{1F3FF}","\u{1F937}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F937}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F977}","\u{1F977}\u{1F3FB}","\u{1F977}\u{1F3FC}","\u{1F977}\u{1F3FD}","\u{1F977}\u{1F3FE}","\u{1F977}\u{1F3FF}","\u{1F9B8}","\u{1F9B8}\u200D\u2640\uFE0F","\u{1F9B8}\u200D\u2642\uFE0F","\u{1F9B8}\u{1F3FB}","\u{1F9B8}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9B8}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9B8}\u{1F3FC}","\u{1F9B8}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9B8}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9B8}\u{1F3FD}","\u{1F9B8}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9B8}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9B8}\u{1F3FE}","\u{1F9B8}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9B8}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9B8}\u{1F3FF}","\u{1F9B8}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9B8}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9B9}","\u{1F9B9}\u200D\u2640\uFE0F","\u{1F9B9}\u200D\u2642\uFE0F","\u{1F9B9}\u{1F3FB}","\u{1F9B9}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9B9}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9B9}\u{1F3FC}","\u{1F9B9}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9B9}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9B9}\u{1F3FD}","\u{1F9B9}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9B9}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9B9}\u{1F3FE}","\u{1F9B9}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9B9}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9B9}\u{1F3FF}","\u{1F9B9}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9B9}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9CF}","\u{1F9CF}\u200D\u2640\uFE0F","\u{1F9CF}\u200D\u2642\uFE0F","\u{1F9CF}\u{1F3FB}","\u{1F9CF}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9CF}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9CF}\u{1F3FC}","\u{1F9CF}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9CF}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9CF}\u{1F3FD}","\u{1F9CF}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9CF}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9CF}\u{1F3FE}","\u{1F9CF}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9CF}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9CF}\u{1F3FF}","\u{1F9CF}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9CF}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9D1}","\u{1F9D1}\u200D\u2695\uFE0F","\u{1F9D1}\u200D\u2696\uFE0F","\u{1F9D1}\u200D\u2708\uFE0F","\u{1F9D1}\u200D\u{1F33E}","\u{1F9D1}\u200D\u{1F373}","\u{1F9D1}\u200D\u{1F37C}","\u{1F9D1}\u200D\u{1F384}","\u{1F9D1}\u200D\u{1F393}","\u{1F9D1}\u200D\u{1F3A4}","\u{1F9D1}\u200D\u{1F3A8}","\u{1F9D1}\u200D\u{1F3EB}","\u{1F9D1}\u200D\u{1F3ED}","\u{1F9D1}\u200D\u{1F4BB}","\u{1F9D1}\u200D\u{1F4BC}","\u{1F9D1}\u200D\u{1F527}","\u{1F9D1}\u200D\u{1F52C}","\u{1F9D1}\u200D\u{1F680}","\u{1F9D1}\u200D\u{1F692}","\u{1F9D1}\u200D\u{1F9B0}","\u{1F9D1}\u200D\u{1F9B1}","\u{1F9D1}\u200D\u{1F9B2}","\u{1F9D1}\u200D\u{1F9B3}","\u{1F9D1}\u{1F3FB}","\u{1F9D1}\u{1F3FB}\u200D\u2695\uFE0F","\u{1F9D1}\u{1F3FB}\u200D\u2696\uFE0F","\u{1F9D1}\u{1F3FB}\u200D\u2708\uFE0F","\u{1F9D1}\u{1F3FB}\u200D\u{1F33E}","\u{1F9D1}\u{1F3FB}\u200D\u{1F373}","\u{1F9D1}\u{1F3FB}\u200D\u{1F37C}","\u{1F9D1}\u{1F3FB}\u200D\u{1F384}","\u{1F9D1}\u{1F3FB}\u200D\u{1F393}","\u{1F9D1}\u{1F3FB}\u200D\u{1F3A4}","\u{1F9D1}\u{1F3FB}\u200D\u{1F3A8}","\u{1F9D1}\u{1F3FB}\u200D\u{1F3EB}","\u{1F9D1}\u{1F3FB}\u200D\u{1F3ED}","\u{1F9D1}\u{1F3FB}\u200D\u{1F4BB}","\u{1F9D1}\u{1F3FB}\u200D\u{1F4BC}","\u{1F9D1}\u{1F3FB}\u200D\u{1F527}","\u{1F9D1}\u{1F3FB}\u200D\u{1F52C}","\u{1F9D1}\u{1F3FB}\u200D\u{1F680}","\u{1F9D1}\u{1F3FB}\u200D\u{1F692}","\u{1F9D1}\u{1F3FB}\u200D\u{1F9B0}","\u{1F9D1}\u{1F3FB}\u200D\u{1F9B1}","\u{1F9D1}\u{1F3FB}\u200D\u{1F9B2}","\u{1F9D1}\u{1F3FB}\u200D\u{1F9B3}","\u{1F9D1}\u{1F3FC}","\u{1F9D1}\u{1F3FC}\u200D\u2695\uFE0F","\u{1F9D1}\u{1F3FC}\u200D\u2696\uFE0F","\u{1F9D1}\u{1F3FC}\u200D\u2708\uFE0F","\u{1F9D1}\u{1F3FC}\u200D\u{1F33E}","\u{1F9D1}\u{1F3FC}\u200D\u{1F373}","\u{1F9D1}\u{1F3FC}\u200D\u{1F37C}","\u{1F9D1}\u{1F3FC}\u200D\u{1F384}","\u{1F9D1}\u{1F3FC}\u200D\u{1F393}","\u{1F9D1}\u{1F3FC}\u200D\u{1F3A4}","\u{1F9D1}\u{1F3FC}\u200D\u{1F3A8}","\u{1F9D1}\u{1F3FC}\u200D\u{1F3EB}","\u{1F9D1}\u{1F3FC}\u200D\u{1F3ED}","\u{1F9D1}\u{1F3FC}\u200D\u{1F4BB}","\u{1F9D1}\u{1F3FC}\u200D\u{1F4BC}","\u{1F9D1}\u{1F3FC}\u200D\u{1F527}","\u{1F9D1}\u{1F3FC}\u200D\u{1F52C}","\u{1F9D1}\u{1F3FC}\u200D\u{1F680}","\u{1F9D1}\u{1F3FC}\u200D\u{1F692}","\u{1F9D1}\u{1F3FC}\u200D\u{1F9B0}","\u{1F9D1}\u{1F3FC}\u200D\u{1F9B1}","\u{1F9D1}\u{1F3FC}\u200D\u{1F9B2}","\u{1F9D1}\u{1F3FC}\u200D\u{1F9B3}","\u{1F9D1}\u{1F3FD}","\u{1F9D1}\u{1F3FD}\u200D\u2695\uFE0F","\u{1F9D1}\u{1F3FD}\u200D\u2696\uFE0F","\u{1F9D1}\u{1F3FD}\u200D\u2708\uFE0F","\u{1F9D1}\u{1F3FD}\u200D\u{1F33E}","\u{1F9D1}\u{1F3FD}\u200D\u{1F373}","\u{1F9D1}\u{1F3FD}\u200D\u{1F37C}","\u{1F9D1}\u{1F3FD}\u200D\u{1F384}","\u{1F9D1}\u{1F3FD}\u200D\u{1F393}","\u{1F9D1}\u{1F3FD}\u200D\u{1F3A4}","\u{1F9D1}\u{1F3FD}\u200D\u{1F3A8}","\u{1F9D1}\u{1F3FD}\u200D\u{1F3EB}","\u{1F9D1}\u{1F3FD}\u200D\u{1F3ED}","\u{1F9D1}\u{1F3FD}\u200D\u{1F4BB}","\u{1F9D1}\u{1F3FD}\u200D\u{1F4BC}","\u{1F9D1}\u{1F3FD}\u200D\u{1F527}","\u{1F9D1}\u{1F3FD}\u200D\u{1F52C}","\u{1F9D1}\u{1F3FD}\u200D\u{1F680}","\u{1F9D1}\u{1F3FD}\u200D\u{1F692}","\u{1F9D1}\u{1F3FD}\u200D\u{1F9B0}","\u{1F9D1}\u{1F3FD}\u200D\u{1F9B1}","\u{1F9D1}\u{1F3FD}\u200D\u{1F9B2}","\u{1F9D1}\u{1F3FD}\u200D\u{1F9B3}","\u{1F9D1}\u{1F3FE}","\u{1F9D1}\u{1F3FE}\u200D\u2695\uFE0F","\u{1F9D1}\u{1F3FE}\u200D\u2696\uFE0F","\u{1F9D1}\u{1F3FE}\u200D\u2708\uFE0F","\u{1F9D1}\u{1F3FE}\u200D\u{1F33E}","\u{1F9D1}\u{1F3FE}\u200D\u{1F373}","\u{1F9D1}\u{1F3FE}\u200D\u{1F37C}","\u{1F9D1}\u{1F3FE}\u200D\u{1F384}","\u{1F9D1}\u{1F3FE}\u200D\u{1F393}","\u{1F9D1}\u{1F3FE}\u200D\u{1F3A4}","\u{1F9D1}\u{1F3FE}\u200D\u{1F3A8}","\u{1F9D1}\u{1F3FE}\u200D\u{1F3EB}","\u{1F9D1}\u{1F3FE}\u200D\u{1F3ED}","\u{1F9D1}\u{1F3FE}\u200D\u{1F4BB}","\u{1F9D1}\u{1F3FE}\u200D\u{1F4BC}","\u{1F9D1}\u{1F3FE}\u200D\u{1F527}","\u{1F9D1}\u{1F3FE}\u200D\u{1F52C}","\u{1F9D1}\u{1F3FE}\u200D\u{1F680}","\u{1F9D1}\u{1F3FE}\u200D\u{1F692}","\u{1F9D1}\u{1F3FE}\u200D\u{1F9B0}","\u{1F9D1}\u{1F3FE}\u200D\u{1F9B1}","\u{1F9D1}\u{1F3FE}\u200D\u{1F9B2}","\u{1F9D1}\u{1F3FE}\u200D\u{1F9B3}","\u{1F9D1}\u{1F3FF}","\u{1F9D1}\u{1F3FF}\u200D\u2695\uFE0F","\u{1F9D1}\u{1F3FF}\u200D\u2696\uFE0F","\u{1F9D1}\u{1F3FF}\u200D\u2708\uFE0F","\u{1F9D1}\u{1F3FF}\u200D\u{1F33E}","\u{1F9D1}\u{1F3FF}\u200D\u{1F373}","\u{1F9D1}\u{1F3FF}\u200D\u{1F37C}","\u{1F9D1}\u{1F3FF}\u200D\u{1F384}","\u{1F9D1}\u{1F3FF}\u200D\u{1F393}","\u{1F9D1}\u{1F3FF}\u200D\u{1F3A4}","\u{1F9D1}\u{1F3FF}\u200D\u{1F3A8}","\u{1F9D1}\u{1F3FF}\u200D\u{1F3EB}","\u{1F9D1}\u{1F3FF}\u200D\u{1F3ED}","\u{1F9D1}\u{1F3FF}\u200D\u{1F4BB}","\u{1F9D1}\u{1F3FF}\u200D\u{1F4BC}","\u{1F9D1}\u{1F3FF}\u200D\u{1F527}","\u{1F9D1}\u{1F3FF}\u200D\u{1F52C}","\u{1F9D1}\u{1F3FF}\u200D\u{1F680}","\u{1F9D1}\u{1F3FF}\u200D\u{1F692}","\u{1F9D1}\u{1F3FF}\u200D\u{1F9B0}","\u{1F9D1}\u{1F3FF}\u200D\u{1F9B1}","\u{1F9D1}\u{1F3FF}\u200D\u{1F9B2}","\u{1F9D1}\u{1F3FF}\u200D\u{1F9B3}","\u{1F9D2}","\u{1F9D2}\u{1F3FB}","\u{1F9D2}\u{1F3FC}","\u{1F9D2}\u{1F3FD}","\u{1F9D2}\u{1F3FE}","\u{1F9D2}\u{1F3FF}","\u{1F9D3}","\u{1F9D3}\u{1F3FB}","\u{1F9D3}\u{1F3FC}","\u{1F9D3}\u{1F3FD}","\u{1F9D3}\u{1F3FE}","\u{1F9D3}\u{1F3FF}","\u{1F9D4}","\u{1F9D4}\u200D\u2640\uFE0F","\u{1F9D4}\u200D\u2642\uFE0F","\u{1F9D4}\u{1F3FB}","\u{1F9D4}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9D4}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9D4}\u{1F3FC}","\u{1F9D4}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9D4}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9D4}\u{1F3FD}","\u{1F9D4}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9D4}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9D4}\u{1F3FE}","\u{1F9D4}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9D4}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9D4}\u{1F3FF}","\u{1F9D4}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9D4}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9D5}","\u{1F9D5}\u{1F3FB}","\u{1F9D5}\u{1F3FC}","\u{1F9D5}\u{1F3FD}","\u{1F9D5}\u{1F3FE}","\u{1F9D5}\u{1F3FF}","\u{1F9D9}","\u{1F9D9}\u200D\u2640\uFE0F","\u{1F9D9}\u200D\u2642\uFE0F","\u{1F9D9}\u{1F3FB}","\u{1F9D9}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9D9}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9D9}\u{1F3FC}","\u{1F9D9}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9D9}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9D9}\u{1F3FD}","\u{1F9D9}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9D9}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9D9}\u{1F3FE}","\u{1F9D9}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9D9}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9D9}\u{1F3FF}","\u{1F9D9}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9D9}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9DA}","\u{1F9DA}\u200D\u2640\uFE0F","\u{1F9DA}\u200D\u2642\uFE0F","\u{1F9DA}\u{1F3FB}","\u{1F9DA}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9DA}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9DA}\u{1F3FC}","\u{1F9DA}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9DA}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9DA}\u{1F3FD}","\u{1F9DA}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9DA}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9DA}\u{1F3FE}","\u{1F9DA}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9DA}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9DA}\u{1F3FF}","\u{1F9DA}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9DA}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9DB}","\u{1F9DB}\u200D\u2640\uFE0F","\u{1F9DB}\u200D\u2642\uFE0F","\u{1F9DB}\u{1F3FB}","\u{1F9DB}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9DB}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9DB}\u{1F3FC}","\u{1F9DB}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9DB}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9DB}\u{1F3FD}","\u{1F9DB}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9DB}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9DB}\u{1F3FE}","\u{1F9DB}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9DB}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9DB}\u{1F3FF}","\u{1F9DB}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9DB}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9DC}","\u{1F9DC}\u200D\u2640\uFE0F","\u{1F9DC}\u200D\u2642\uFE0F","\u{1F9DC}\u{1F3FB}","\u{1F9DC}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9DC}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9DC}\u{1F3FC}","\u{1F9DC}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9DC}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9DC}\u{1F3FD}","\u{1F9DC}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9DC}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9DC}\u{1F3FE}","\u{1F9DC}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9DC}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9DC}\u{1F3FF}","\u{1F9DC}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9DC}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9DD}","\u{1F9DD}\u200D\u2640\uFE0F","\u{1F9DD}\u200D\u2642\uFE0F","\u{1F9DD}\u{1F3FB}","\u{1F9DD}\u{1F3FB}\u200D\u2640\uFE0F","\u{1F9DD}\u{1F3FB}\u200D\u2642\uFE0F","\u{1F9DD}\u{1F3FC}","\u{1F9DD}\u{1F3FC}\u200D\u2640\uFE0F","\u{1F9DD}\u{1F3FC}\u200D\u2642\uFE0F","\u{1F9DD}\u{1F3FD}","\u{1F9DD}\u{1F3FD}\u200D\u2640\uFE0F","\u{1F9DD}\u{1F3FD}\u200D\u2642\uFE0F","\u{1F9DD}\u{1F3FE}","\u{1F9DD}\u{1F3FE}\u200D\u2640\uFE0F","\u{1F9DD}\u{1F3FE}\u200D\u2642\uFE0F","\u{1F9DD}\u{1F3FF}","\u{1F9DD}\u{1F3FF}\u200D\u2640\uFE0F","\u{1F9DD}\u{1F3FF}\u200D\u2642\uFE0F","\u{1F9DE}","\u{1F9DE}\u200D\u2640\uFE0F","\u{1F9DE}\u200D\u2642\uFE0F","\u{1F9DF}","\u{1F9DF}\u200D\u2640\uFE0F","\u{1F9DF}\u200D\u2642\uFE0F"],nature:["\u2618\uFE0F","\u{1F331}","\u{1F332}","\u{1F333}","\u{1F334}","\u{1F335}","\u{1F337}","\u{1F338}","\u{1F339}","\u{1F33A}","\u{1F33B}","\u{1F33C}","\u{1F33E}","\u{1F33F}","\u{1F340}","\u{1F341}","\u{1F342}","\u{1F343}","\u{1F3F5}\uFE0F","\u{1F400}","\u{1F401}","\u{1F402}","\u{1F403}","\u{1F404}","\u{1F405}","\u{1F406}","\u{1F407}","\u{1F408}","\u{1F408}\u200D\u2B1B","\u{1F409}","\u{1F40A}","\u{1F40B}","\u{1F40C}","\u{1F40D}","\u{1F40E}","\u{1F40F}","\u{1F410}","\u{1F411}","\u{1F412}","\u{1F413}","\u{1F414}","\u{1F415}","\u{1F415}\u200D\u{1F9BA}","\u{1F416}","\u{1F417}","\u{1F418}","\u{1F419}","\u{1F41A}","\u{1F41B}","\u{1F41C}","\u{1F41D}","\u{1F41E}","\u{1F41F}","\u{1F420}","\u{1F421}","\u{1F422}","\u{1F423}","\u{1F424}","\u{1F425}","\u{1F426}","\u{1F427}","\u{1F428}","\u{1F429}","\u{1F42A}","\u{1F42B}","\u{1F42C}","\u{1F42D}","\u{1F42E}","\u{1F42F}","\u{1F430}","\u{1F431}","\u{1F432}","\u{1F433}","\u{1F434}","\u{1F435}","\u{1F436}","\u{1F437}","\u{1F438}","\u{1F439}","\u{1F43A}","\u{1F43B}","\u{1F43B}\u200D\u2744\uFE0F","\u{1F43C}","\u{1F43D}","\u{1F43E}","\u{1F43F}\uFE0F","\u{1F490}","\u{1F4AE}","\u{1F54A}\uFE0F","\u{1F577}\uFE0F","\u{1F578}\uFE0F","\u{1F940}","\u{1F981}","\u{1F982}","\u{1F983}","\u{1F984}","\u{1F985}","\u{1F986}","\u{1F987}","\u{1F988}","\u{1F989}","\u{1F98A}","\u{1F98B}","\u{1F98C}","\u{1F98D}","\u{1F98E}","\u{1F98F}","\u{1F992}","\u{1F993}","\u{1F994}","\u{1F995}","\u{1F996}","\u{1F997}","\u{1F998}","\u{1F999}","\u{1F99A}","\u{1F99B}","\u{1F99C}","\u{1F99D}","\u{1F99F}","\u{1F9A0}","\u{1F9A1}","\u{1F9A2}","\u{1F9A3}","\u{1F9A4}","\u{1F9A5}","\u{1F9A6}","\u{1F9A7}","\u{1F9A8}","\u{1F9A9}","\u{1F9AB}","\u{1F9AC}","\u{1F9AD}","\u{1F9AE}","\u{1FAB0}","\u{1FAB1}","\u{1FAB2}","\u{1FAB3}","\u{1FAB4}","\u{1FAB6}"],food:["\u2615","\u{1F32D}","\u{1F32E}","\u{1F32F}","\u{1F330}","\u{1F336}\uFE0F","\u{1F33D}","\u{1F344}","\u{1F345}","\u{1F346}","\u{1F347}","\u{1F348}","\u{1F349}","\u{1F34A}","\u{1F34B}","\u{1F34C}","\u{1F34D}","\u{1F34E}","\u{1F34F}","\u{1F350}","\u{1F351}","\u{1F352}","\u{1F353}","\u{1F354}","\u{1F355}","\u{1F356}","\u{1F357}","\u{1F358}","\u{1F359}","\u{1F35A}","\u{1F35B}","\u{1F35C}","\u{1F35D}","\u{1F35E}","\u{1F35F}","\u{1F360}","\u{1F361}","\u{1F362}","\u{1F363}","\u{1F364}","\u{1F365}","\u{1F366}","\u{1F367}","\u{1F368}","\u{1F369}","\u{1F36A}","\u{1F36B}","\u{1F36C}","\u{1F36D}","\u{1F36E}","\u{1F36F}","\u{1F370}","\u{1F371}","\u{1F372}","\u{1F373}","\u{1F374}","\u{1F375}","\u{1F376}","\u{1F377}","\u{1F378}","\u{1F379}","\u{1F37A}","\u{1F37B}","\u{1F37C}","\u{1F37D}\uFE0F","\u{1F37E}","\u{1F37F}","\u{1F382}","\u{1F3FA}","\u{1F52A}","\u{1F942}","\u{1F943}","\u{1F944}","\u{1F950}","\u{1F951}","\u{1F952}","\u{1F953}","\u{1F954}","\u{1F955}","\u{1F956}","\u{1F957}","\u{1F958}","\u{1F959}","\u{1F95A}","\u{1F95B}","\u{1F95C}","\u{1F95D}","\u{1F95E}","\u{1F95F}","\u{1F960}","\u{1F961}","\u{1F962}","\u{1F963}","\u{1F964}","\u{1F965}","\u{1F966}","\u{1F967}","\u{1F968}","\u{1F969}","\u{1F96A}","\u{1F96B}","\u{1F96C}","\u{1F96D}","\u{1F96E}","\u{1F96F}","\u{1F980}","\u{1F990}","\u{1F991}","\u{1F99E}","\u{1F9AA}","\u{1F9C0}","\u{1F9C1}","\u{1F9C2}","\u{1F9C3}","\u{1F9C4}","\u{1F9C5}","\u{1F9C6}","\u{1F9C7}","\u{1F9C8}","\u{1F9C9}","\u{1F9CA}","\u{1F9CB}","\u{1FAD0}","\u{1FAD1}","\u{1FAD2}","\u{1FAD3}","\u{1FAD4}","\u{1FAD5}","\u{1FAD6}"],travel:["\u231A","\u231B","\u23F0","\u23F1\uFE0F","\u23F2\uFE0F","\u23F3","\u2600\uFE0F","\u2601\uFE0F","\u2602\uFE0F","\u2603\uFE0F","\u2604\uFE0F","\u2614","\u2668\uFE0F","\u2693","\u26A1","\u26C4","\u26C5","\u26C8\uFE0F","\u26E9\uFE0F","\u26EA","\u26F0\uFE0F","\u26F1\uFE0F","\u26F2","\u26F4\uFE0F","\u26F5","\u26FA","\u26FD","\u2708\uFE0F","\u2744\uFE0F","\u2B50","\u{1F300}","\u{1F301}","\u{1F302}","\u{1F303}","\u{1F304}","\u{1F305}","\u{1F306}","\u{1F307}","\u{1F308}","\u{1F309}","\u{1F30A}","\u{1F30B}","\u{1F30C}","\u{1F30D}","\u{1F30E}","\u{1F30F}","\u{1F310}","\u{1F311}","\u{1F312}","\u{1F313}","\u{1F314}","\u{1F315}","\u{1F316}","\u{1F317}","\u{1F318}","\u{1F319}","\u{1F31A}","\u{1F31B}","\u{1F31C}","\u{1F31D}","\u{1F31E}","\u{1F31F}","\u{1F320}","\u{1F321}\uFE0F","\u{1F324}\uFE0F","\u{1F325}\uFE0F","\u{1F326}\uFE0F","\u{1F327}\uFE0F","\u{1F328}\uFE0F","\u{1F329}\uFE0F","\u{1F32A}\uFE0F","\u{1F32B}\uFE0F","\u{1F32C}\uFE0F","\u{1F3A0}","\u{1F3A1}","\u{1F3A2}","\u{1F3AA}","\u{1F3CD}\uFE0F","\u{1F3CE}\uFE0F","\u{1F3D4}\uFE0F","\u{1F3D5}\uFE0F","\u{1F3D6}\uFE0F","\u{1F3D7}\uFE0F","\u{1F3D8}\uFE0F","\u{1F3D9}\uFE0F","\u{1F3DA}\uFE0F","\u{1F3DB}\uFE0F","\u{1F3DC}\uFE0F","\u{1F3DD}\uFE0F","\u{1F3DE}\uFE0F","\u{1F3DF}\uFE0F","\u{1F3E0}","\u{1F3E1}","\u{1F3E2}","\u{1F3E3}","\u{1F3E4}","\u{1F3E5}","\u{1F3E6}","\u{1F3E8}","\u{1F3E9}","\u{1F3EA}","\u{1F3EB}","\u{1F3EC}","\u{1F3ED}","\u{1F3EF}","\u{1F3F0}","\u{1F488}","\u{1F492}","\u{1F4A7}","\u{1F4BA}","\u{1F525}","\u{1F54B}","\u{1F54C}","\u{1F54D}","\u{1F550}","\u{1F551}","\u{1F552}","\u{1F553}","\u{1F554}","\u{1F555}","\u{1F556}","\u{1F557}","\u{1F558}","\u{1F559}","\u{1F55A}","\u{1F55B}","\u{1F55C}","\u{1F55D}","\u{1F55E}","\u{1F55F}","\u{1F560}","\u{1F561}","\u{1F562}","\u{1F563}","\u{1F564}","\u{1F565}","\u{1F566}","\u{1F567}","\u{1F570}\uFE0F","\u{1F5FA}\uFE0F","\u{1F5FB}","\u{1F5FC}","\u{1F5FD}","\u{1F5FE}","\u{1F680}","\u{1F681}","\u{1F682}","\u{1F683}","\u{1F684}","\u{1F685}","\u{1F686}","\u{1F687}","\u{1F688}","\u{1F689}","\u{1F68A}","\u{1F68B}","\u{1F68C}","\u{1F68D}","\u{1F68E}","\u{1F68F}","\u{1F690}","\u{1F691}","\u{1F692}","\u{1F693}","\u{1F694}","\u{1F695}","\u{1F696}","\u{1F697}","\u{1F698}","\u{1F699}","\u{1F69A}","\u{1F69B}","\u{1F69C}","\u{1F69D}","\u{1F69E}","\u{1F69F}","\u{1F6A0}","\u{1F6A1}","\u{1F6A2}","\u{1F6A4}","\u{1F6A5}","\u{1F6A6}","\u{1F6A7}","\u{1F6A8}","\u{1F6B2}","\u{1F6CE}\uFE0F","\u{1F6D1}","\u{1F6D5}","\u{1F6D6}","\u{1F6E2}\uFE0F","\u{1F6E3}\uFE0F","\u{1F6E4}\uFE0F","\u{1F6E5}\uFE0F","\u{1F6E9}\uFE0F","\u{1F6EB}","\u{1F6EC}","\u{1F6F0}\uFE0F","\u{1F6F3}\uFE0F","\u{1F6F4}","\u{1F6F5}","\u{1F6F6}","\u{1F6F8}","\u{1F6F9}","\u{1F6FA}","\u{1F6FB}","\u{1F6FC}","\u{1F9BC}","\u{1F9BD}","\u{1F9ED}","\u{1F9F1}","\u{1F9F3}","\u{1FA82}","\u{1FA90}","\u{1FAA8}","\u{1FAB5}"],activity:["\u265F\uFE0F","\u2660\uFE0F","\u2663\uFE0F","\u2665\uFE0F","\u2666\uFE0F","\u26BD","\u26BE","\u26F3","\u26F8\uFE0F","\u2728","\u{1F004}","\u{1F0CF}","\u{1F380}","\u{1F381}","\u{1F383}","\u{1F384}","\u{1F386}","\u{1F387}","\u{1F388}","\u{1F389}","\u{1F38A}","\u{1F38B}","\u{1F38D}","\u{1F38E}","\u{1F38F}","\u{1F390}","\u{1F391}","\u{1F396}\uFE0F","\u{1F397}\uFE0F","\u{1F39F}\uFE0F","\u{1F3A3}","\u{1F3A8}","\u{1F3AB}","\u{1F3AD}","\u{1F3AE}","\u{1F3AF}","\u{1F3B0}","\u{1F3B1}","\u{1F3B2}","\u{1F3B3}","\u{1F3B4}","\u{1F3BD}","\u{1F3BE}","\u{1F3BF}","\u{1F3C0}","\u{1F3C5}","\u{1F3C6}","\u{1F3C8}","\u{1F3C9}","\u{1F3CF}","\u{1F3D0}","\u{1F3D1}","\u{1F3D2}","\u{1F3D3}","\u{1F3F8}","\u{1F52E}","\u{1F579}\uFE0F","\u{1F5BC}\uFE0F","\u{1F6F7}","\u{1F93F}","\u{1F945}","\u{1F947}","\u{1F948}","\u{1F949}","\u{1F94A}","\u{1F94B}","\u{1F94C}","\u{1F94D}","\u{1F94E}","\u{1F94F}","\u{1F9E7}","\u{1F9E8}","\u{1F9E9}","\u{1F9F5}","\u{1F9F6}","\u{1F9F8}","\u{1F9FF}","\u{1FA80}","\u{1FA81}","\u{1FA84}","\u{1FA85}","\u{1FA86}","\u{1FAA1}","\u{1FAA2}"],object:["\u2328\uFE0F","\u260E\uFE0F","\u2692\uFE0F","\u2694\uFE0F","\u2696\uFE0F","\u2697\uFE0F","\u2699\uFE0F","\u26B0\uFE0F","\u26B1\uFE0F","\u26CF\uFE0F","\u26D1\uFE0F","\u26D3\uFE0F","\u2702\uFE0F","\u2709\uFE0F","\u270F\uFE0F","\u2712\uFE0F","\u{1F392}","\u{1F393}","\u{1F399}\uFE0F","\u{1F39A}\uFE0F","\u{1F39B}\uFE0F","\u{1F39E}\uFE0F","\u{1F3A4}","\u{1F3A5}","\u{1F3A7}","\u{1F3A9}","\u{1F3AC}","\u{1F3B5}","\u{1F3B6}","\u{1F3B7}","\u{1F3B8}","\u{1F3B9}","\u{1F3BA}","\u{1F3BB}","\u{1F3BC}","\u{1F3EE}","\u{1F3F7}\uFE0F","\u{1F3F9}","\u{1F451}","\u{1F452}","\u{1F453}","\u{1F454}","\u{1F455}","\u{1F456}","\u{1F457}","\u{1F458}","\u{1F459}","\u{1F45A}","\u{1F45B}","\u{1F45C}","\u{1F45D}","\u{1F45E}","\u{1F45F}","\u{1F460}","\u{1F461}","\u{1F462}","\u{1F484}","\u{1F489}","\u{1F48A}","\u{1F48D}","\u{1F48E}","\u{1F4A1}","\u{1F4B0}","\u{1F4B3}","\u{1F4B4}","\u{1F4B5}","\u{1F4B6}","\u{1F4B7}","\u{1F4B8}","\u{1F4B9}","\u{1F4BB}","\u{1F4BC}","\u{1F4BD}","\u{1F4BE}","\u{1F4BF}","\u{1F4C0}","\u{1F4C1}","\u{1F4C2}","\u{1F4C3}","\u{1F4C4}","\u{1F4C5}","\u{1F4C6}","\u{1F4C7}","\u{1F4C8}","\u{1F4C9}","\u{1F4CA}","\u{1F4CB}","\u{1F4CC}","\u{1F4CD}","\u{1F4CE}","\u{1F4CF}","\u{1F4D0}","\u{1F4D1}","\u{1F4D2}","\u{1F4D3}","\u{1F4D4}","\u{1F4D5}","\u{1F4D6}","\u{1F4D7}","\u{1F4D8}","\u{1F4D9}","\u{1F4DA}","\u{1F4DC}","\u{1F4DD}","\u{1F4DE}","\u{1F4DF}","\u{1F4E0}","\u{1F4E1}","\u{1F4E2}","\u{1F4E3}","\u{1F4E4}","\u{1F4E5}","\u{1F4E6}","\u{1F4E7}","\u{1F4E8}","\u{1F4E9}","\u{1F4EA}","\u{1F4EB}","\u{1F4EC}","\u{1F4ED}","\u{1F4EE}","\u{1F4EF}","\u{1F4F0}","\u{1F4F1}","\u{1F4F2}","\u{1F4F7}","\u{1F4F8}","\u{1F4F9}","\u{1F4FA}","\u{1F4FB}","\u{1F4FC}","\u{1F4FD}\uFE0F","\u{1F4FF}","\u{1F507}","\u{1F508}","\u{1F509}","\u{1F50A}","\u{1F50B}","\u{1F50C}","\u{1F50D}","\u{1F50E}","\u{1F50F}","\u{1F510}","\u{1F511}","\u{1F512}","\u{1F513}","\u{1F514}","\u{1F515}","\u{1F516}","\u{1F517}","\u{1F526}","\u{1F527}","\u{1F528}","\u{1F529}","\u{1F52B}","\u{1F52C}","\u{1F52D}","\u{1F56F}\uFE0F","\u{1F576}\uFE0F","\u{1F587}\uFE0F","\u{1F58A}\uFE0F","\u{1F58B}\uFE0F","\u{1F58C}\uFE0F","\u{1F58D}\uFE0F","\u{1F5A5}\uFE0F","\u{1F5A8}\uFE0F","\u{1F5B1}\uFE0F","\u{1F5B2}\uFE0F","\u{1F5C2}\uFE0F","\u{1F5C3}\uFE0F","\u{1F5C4}\uFE0F","\u{1F5D1}\uFE0F","\u{1F5D2}\uFE0F","\u{1F5D3}\uFE0F","\u{1F5DC}\uFE0F","\u{1F5DD}\uFE0F","\u{1F5DE}\uFE0F","\u{1F5E1}\uFE0F","\u{1F5F3}\uFE0F","\u{1F5FF}","\u{1F6AA}","\u{1F6AC}","\u{1F6BD}","\u{1F6BF}","\u{1F6C1}","\u{1F6CB}\uFE0F","\u{1F6CD}\uFE0F","\u{1F6CF}\uFE0F","\u{1F6D2}","\u{1F6D7}","\u{1F6E0}\uFE0F","\u{1F6E1}\uFE0F","\u{1F941}","\u{1F97B}","\u{1F97C}","\u{1F97D}","\u{1F97E}","\u{1F97F}","\u{1F9AF}","\u{1F9BA}","\u{1F9E2}","\u{1F9E3}","\u{1F9E4}","\u{1F9E5}","\u{1F9E6}","\u{1F9EA}","\u{1F9EB}","\u{1F9EC}","\u{1F9EE}","\u{1F9EF}","\u{1F9F0}","\u{1F9F2}","\u{1F9F4}","\u{1F9F7}","\u{1F9F9}","\u{1F9FA}","\u{1F9FB}","\u{1F9FC}","\u{1F9FD}","\u{1F9FE}","\u{1FA70}","\u{1FA71}","\u{1FA72}","\u{1FA73}","\u{1FA74}","\u{1FA78}","\u{1FA79}","\u{1FA7A}","\u{1FA83}","\u{1FA91}","\u{1FA92}","\u{1FA93}","\u{1FA94}","\u{1FA95}","\u{1FA96}","\u{1FA97}","\u{1FA98}","\u{1FA99}","\u{1FA9A}","\u{1FA9B}","\u{1FA9C}","\u{1FA9D}","\u{1FA9E}","\u{1FA9F}","\u{1FAA0}","\u{1FAA3}","\u{1FAA4}","\u{1FAA5}","\u{1FAA6}","\u{1FAA7}"],symbol:["#\uFE0F\u20E3","*\uFE0F\u20E3","0\uFE0F\u20E3","1\uFE0F\u20E3","2\uFE0F\u20E3","3\uFE0F\u20E3","4\uFE0F\u20E3","5\uFE0F\u20E3","6\uFE0F\u20E3","7\uFE0F\u20E3","8\uFE0F\u20E3","9\uFE0F\u20E3","\xA9\uFE0F","\xAE\uFE0F","\u203C\uFE0F","\u2049\uFE0F","\u2122\uFE0F","\u2139\uFE0F","\u2194\uFE0F","\u2195\uFE0F","\u2196\uFE0F","\u2197\uFE0F","\u2198\uFE0F","\u2199\uFE0F","\u21A9\uFE0F","\u21AA\uFE0F","\u23CF\uFE0F","\u23E9","\u23EA","\u23EB","\u23EC","\u23ED\uFE0F","\u23EE\uFE0F","\u23EF\uFE0F","\u23F8\uFE0F","\u23F9\uFE0F","\u23FA\uFE0F","\u24C2\uFE0F","\u25AA\uFE0F","\u25AB\uFE0F","\u25B6\uFE0F","\u25C0\uFE0F","\u25FB\uFE0F","\u25FC\uFE0F","\u25FD","\u25FE","\u2611\uFE0F","\u2622\uFE0F","\u2623\uFE0F","\u2626\uFE0F","\u262A\uFE0F","\u262E\uFE0F","\u262F\uFE0F","\u2638\uFE0F","\u2640\uFE0F","\u2642\uFE0F","\u2648","\u2649","\u264A","\u264B","\u264C","\u264D","\u264E","\u264F","\u2650","\u2651","\u2652","\u2653","\u267B\uFE0F","\u267E\uFE0F","\u267F","\u2695\uFE0F","\u269B\uFE0F","\u269C\uFE0F","\u26A0\uFE0F","\u26A7\uFE0F","\u26AA","\u26AB","\u26CE","\u26D4","\u2705","\u2714\uFE0F","\u2716\uFE0F","\u271D\uFE0F","\u2721\uFE0F","\u2733\uFE0F","\u2734\uFE0F","\u2747\uFE0F","\u274C","\u274E","\u2753","\u2754","\u2755","\u2757","\u2795","\u2796","\u2797","\u27A1\uFE0F","\u27B0","\u27BF","\u2934\uFE0F","\u2935\uFE0F","\u2B05\uFE0F","\u2B06\uFE0F","\u2B07\uFE0F","\u2B1B","\u2B1C","\u2B55","\u3030\uFE0F","\u303D\uFE0F","\u3297\uFE0F","\u3299\uFE0F","\u{1F170}\uFE0F","\u{1F171}\uFE0F","\u{1F17E}\uFE0F","\u{1F17F}\uFE0F","\u{1F18E}","\u{1F191}","\u{1F192}","\u{1F193}","\u{1F194}","\u{1F195}","\u{1F196}","\u{1F197}","\u{1F198}","\u{1F199}","\u{1F19A}","\u{1F201}","\u{1F202}\uFE0F","\u{1F21A}","\u{1F22F}","\u{1F232}","\u{1F233}","\u{1F234}","\u{1F235}","\u{1F236}","\u{1F237}\uFE0F","\u{1F238}","\u{1F239}","\u{1F23A}","\u{1F250}","\u{1F251}","\u{1F3A6}","\u{1F3E7}","\u{1F4A0}","\u{1F4B1}","\u{1F4B2}","\u{1F4DB}","\u{1F4F3}","\u{1F4F4}","\u{1F4F5}","\u{1F4F6}","\u{1F500}","\u{1F501}","\u{1F502}","\u{1F503}","\u{1F504}","\u{1F505}","\u{1F506}","\u{1F518}","\u{1F519}","\u{1F51A}","\u{1F51B}","\u{1F51C}","\u{1F51D}","\u{1F51E}","\u{1F51F}","\u{1F520}","\u{1F521}","\u{1F522}","\u{1F523}","\u{1F524}","\u{1F52F}","\u{1F530}","\u{1F531}","\u{1F532}","\u{1F533}","\u{1F534}","\u{1F535}","\u{1F536}","\u{1F537}","\u{1F538}","\u{1F539}","\u{1F53A}","\u{1F53B}","\u{1F53C}","\u{1F53D}","\u{1F549}\uFE0F","\u{1F54E}","\u{1F6AB}","\u{1F6AD}","\u{1F6AE}","\u{1F6AF}","\u{1F6B0}","\u{1F6B1}","\u{1F6B3}","\u{1F6B7}","\u{1F6B8}","\u{1F6B9}","\u{1F6BA}","\u{1F6BB}","\u{1F6BC}","\u{1F6BE}","\u{1F6C2}","\u{1F6C3}","\u{1F6C4}","\u{1F6C5}","\u{1F6D0}","\u{1F7E0}","\u{1F7E1}","\u{1F7E2}","\u{1F7E3}","\u{1F7E4}","\u{1F7E5}","\u{1F7E6}","\u{1F7E7}","\u{1F7E8}","\u{1F7E9}","\u{1F7EA}","\u{1F7EB}"],flag:["\u{1F1E6}\u{1F1E8}","\u{1F1E6}\u{1F1E9}","\u{1F1E6}\u{1F1EA}","\u{1F1E6}\u{1F1EB}","\u{1F1E6}\u{1F1EC}","\u{1F1E6}\u{1F1EE}","\u{1F1E6}\u{1F1F1}","\u{1F1E6}\u{1F1F2}","\u{1F1E6}\u{1F1F4}","\u{1F1E6}\u{1F1F6}","\u{1F1E6}\u{1F1F7}","\u{1F1E6}\u{1F1F8}","\u{1F1E6}\u{1F1F9}","\u{1F1E6}\u{1F1FA}","\u{1F1E6}\u{1F1FC}","\u{1F1E6}\u{1F1FD}","\u{1F1E6}\u{1F1FF}","\u{1F1E7}\u{1F1E6}","\u{1F1E7}\u{1F1E7}","\u{1F1E7}\u{1F1E9}","\u{1F1E7}\u{1F1EA}","\u{1F1E7}\u{1F1EB}","\u{1F1E7}\u{1F1EC}","\u{1F1E7}\u{1F1ED}","\u{1F1E7}\u{1F1EE}","\u{1F1E7}\u{1F1EF}","\u{1F1E7}\u{1F1F1}","\u{1F1E7}\u{1F1F2}","\u{1F1E7}\u{1F1F3}","\u{1F1E7}\u{1F1F4}","\u{1F1E7}\u{1F1F6}","\u{1F1E7}\u{1F1F7}","\u{1F1E7}\u{1F1F8}","\u{1F1E7}\u{1F1F9}","\u{1F1E7}\u{1F1FB}","\u{1F1E7}\u{1F1FC}","\u{1F1E7}\u{1F1FE}","\u{1F1E7}\u{1F1FF}","\u{1F1E8}\u{1F1E6}","\u{1F1E8}\u{1F1E8}","\u{1F1E8}\u{1F1E9}","\u{1F1E8}\u{1F1EB}","\u{1F1E8}\u{1F1EC}","\u{1F1E8}\u{1F1ED}","\u{1F1E8}\u{1F1EE}","\u{1F1E8}\u{1F1F0}","\u{1F1E8}\u{1F1F1}","\u{1F1E8}\u{1F1F2}","\u{1F1E8}\u{1F1F3}","\u{1F1E8}\u{1F1F4}","\u{1F1E8}\u{1F1F5}","\u{1F1E8}\u{1F1F7}","\u{1F1E8}\u{1F1FA}","\u{1F1E8}\u{1F1FB}","\u{1F1E8}\u{1F1FC}","\u{1F1E8}\u{1F1FD}","\u{1F1E8}\u{1F1FE}","\u{1F1E8}\u{1F1FF}","\u{1F1E9}\u{1F1EA}","\u{1F1E9}\u{1F1EC}","\u{1F1E9}\u{1F1EF}","\u{1F1E9}\u{1F1F0}","\u{1F1E9}\u{1F1F2}","\u{1F1E9}\u{1F1F4}","\u{1F1E9}\u{1F1FF}","\u{1F1EA}\u{1F1E6}","\u{1F1EA}\u{1F1E8}","\u{1F1EA}\u{1F1EA}","\u{1F1EA}\u{1F1EC}","\u{1F1EA}\u{1F1ED}","\u{1F1EA}\u{1F1F7}","\u{1F1EA}\u{1F1F8}","\u{1F1EA}\u{1F1F9}","\u{1F1EA}\u{1F1FA}","\u{1F1EB}\u{1F1EE}","\u{1F1EB}\u{1F1EF}","\u{1F1EB}\u{1F1F0}","\u{1F1EB}\u{1F1F2}","\u{1F1EB}\u{1F1F4}","\u{1F1EB}\u{1F1F7}","\u{1F1EC}\u{1F1E6}","\u{1F1EC}\u{1F1E7}","\u{1F1EC}\u{1F1E9}","\u{1F1EC}\u{1F1EA}","\u{1F1EC}\u{1F1EB}","\u{1F1EC}\u{1F1EC}","\u{1F1EC}\u{1F1ED}","\u{1F1EC}\u{1F1EE}","\u{1F1EC}\u{1F1F1}","\u{1F1EC}\u{1F1F2}","\u{1F1EC}\u{1F1F3}","\u{1F1EC}\u{1F1F5}","\u{1F1EC}\u{1F1F6}","\u{1F1EC}\u{1F1F7}","\u{1F1EC}\u{1F1F8}","\u{1F1EC}\u{1F1F9}","\u{1F1EC}\u{1F1FA}","\u{1F1EC}\u{1F1FC}","\u{1F1EC}\u{1F1FE}","\u{1F1ED}\u{1F1F0}","\u{1F1ED}\u{1F1F2}","\u{1F1ED}\u{1F1F3}","\u{1F1ED}\u{1F1F7}","\u{1F1ED}\u{1F1F9}","\u{1F1ED}\u{1F1FA}","\u{1F1EE}\u{1F1E8}","\u{1F1EE}\u{1F1E9}","\u{1F1EE}\u{1F1EA}","\u{1F1EE}\u{1F1F1}","\u{1F1EE}\u{1F1F2}","\u{1F1EE}\u{1F1F3}","\u{1F1EE}\u{1F1F4}","\u{1F1EE}\u{1F1F6}","\u{1F1EE}\u{1F1F7}","\u{1F1EE}\u{1F1F8}","\u{1F1EE}\u{1F1F9}","\u{1F1EF}\u{1F1EA}","\u{1F1EF}\u{1F1F2}","\u{1F1EF}\u{1F1F4}","\u{1F1EF}\u{1F1F5}","\u{1F1F0}\u{1F1EA}","\u{1F1F0}\u{1F1EC}","\u{1F1F0}\u{1F1ED}","\u{1F1F0}\u{1F1EE}","\u{1F1F0}\u{1F1F2}","\u{1F1F0}\u{1F1F3}","\u{1F1F0}\u{1F1F5}","\u{1F1F0}\u{1F1F7}","\u{1F1F0}\u{1F1FC}","\u{1F1F0}\u{1F1FE}","\u{1F1F0}\u{1F1FF}","\u{1F1F1}\u{1F1E6}","\u{1F1F1}\u{1F1E7}","\u{1F1F1}\u{1F1E8}","\u{1F1F1}\u{1F1EE}","\u{1F1F1}\u{1F1F0}","\u{1F1F1}\u{1F1F7}","\u{1F1F1}\u{1F1F8}","\u{1F1F1}\u{1F1F9}","\u{1F1F1}\u{1F1FA}","\u{1F1F1}\u{1F1FB}","\u{1F1F1}\u{1F1FE}","\u{1F1F2}\u{1F1E6}","\u{1F1F2}\u{1F1E8}","\u{1F1F2}\u{1F1E9}","\u{1F1F2}\u{1F1EA}","\u{1F1F2}\u{1F1EB}","\u{1F1F2}\u{1F1EC}","\u{1F1F2}\u{1F1ED}","\u{1F1F2}\u{1F1F0}","\u{1F1F2}\u{1F1F1}","\u{1F1F2}\u{1F1F2}","\u{1F1F2}\u{1F1F3}","\u{1F1F2}\u{1F1F4}","\u{1F1F2}\u{1F1F5}","\u{1F1F2}\u{1F1F6}","\u{1F1F2}\u{1F1F7}","\u{1F1F2}\u{1F1F8}","\u{1F1F2}\u{1F1F9}","\u{1F1F2}\u{1F1FA}","\u{1F1F2}\u{1F1FB}","\u{1F1F2}\u{1F1FC}","\u{1F1F2}\u{1F1FD}","\u{1F1F2}\u{1F1FE}","\u{1F1F2}\u{1F1FF}","\u{1F1F3}\u{1F1E6}","\u{1F1F3}\u{1F1E8}","\u{1F1F3}\u{1F1EA}","\u{1F1F3}\u{1F1EB}","\u{1F1F3}\u{1F1EC}","\u{1F1F3}\u{1F1EE}","\u{1F1F3}\u{1F1F1}","\u{1F1F3}\u{1F1F4}","\u{1F1F3}\u{1F1F5}","\u{1F1F3}\u{1F1F7}","\u{1F1F3}\u{1F1FA}","\u{1F1F3}\u{1F1FF}","\u{1F1F4}\u{1F1F2}","\u{1F1F5}\u{1F1E6}","\u{1F1F5}\u{1F1EA}","\u{1F1F5}\u{1F1EB}","\u{1F1F5}\u{1F1EC}","\u{1F1F5}\u{1F1ED}","\u{1F1F5}\u{1F1F0}","\u{1F1F5}\u{1F1F1}","\u{1F1F5}\u{1F1F2}","\u{1F1F5}\u{1F1F3}","\u{1F1F5}\u{1F1F7}","\u{1F1F5}\u{1F1F8}","\u{1F1F5}\u{1F1F9}","\u{1F1F5}\u{1F1FC}","\u{1F1F5}\u{1F1FE}","\u{1F1F6}\u{1F1E6}","\u{1F1F7}\u{1F1EA}","\u{1F1F7}\u{1F1F4}","\u{1F1F7}\u{1F1F8}","\u{1F1F7}\u{1F1FA}","\u{1F1F7}\u{1F1FC}","\u{1F1F8}\u{1F1E6}","\u{1F1F8}\u{1F1E7}","\u{1F1F8}\u{1F1E8}","\u{1F1F8}\u{1F1E9}","\u{1F1F8}\u{1F1EA}","\u{1F1F8}\u{1F1EC}","\u{1F1F8}\u{1F1ED}","\u{1F1F8}\u{1F1EE}","\u{1F1F8}\u{1F1EF}","\u{1F1F8}\u{1F1F0}","\u{1F1F8}\u{1F1F1}","\u{1F1F8}\u{1F1F2}","\u{1F1F8}\u{1F1F3}","\u{1F1F8}\u{1F1F4}","\u{1F1F8}\u{1F1F7}","\u{1F1F8}\u{1F1F8}","\u{1F1F8}\u{1F1F9}","\u{1F1F8}\u{1F1FB}","\u{1F1F8}\u{1F1FD}","\u{1F1F8}\u{1F1FE}","\u{1F1F8}\u{1F1FF}","\u{1F1F9}\u{1F1E6}","\u{1F1F9}\u{1F1E8}","\u{1F1F9}\u{1F1E9}","\u{1F1F9}\u{1F1EB}","\u{1F1F9}\u{1F1EC}","\u{1F1F9}\u{1F1ED}","\u{1F1F9}\u{1F1EF}","\u{1F1F9}\u{1F1F0}","\u{1F1F9}\u{1F1F1}","\u{1F1F9}\u{1F1F2}","\u{1F1F9}\u{1F1F3}","\u{1F1F9}\u{1F1F4}","\u{1F1F9}\u{1F1F7}","\u{1F1F9}\u{1F1F9}","\u{1F1F9}\u{1F1FB}","\u{1F1F9}\u{1F1FC}","\u{1F1F9}\u{1F1FF}","\u{1F1FA}\u{1F1E6}","\u{1F1FA}\u{1F1EC}","\u{1F1FA}\u{1F1F2}","\u{1F1FA}\u{1F1F3}","\u{1F1FA}\u{1F1F8}","\u{1F1FA}\u{1F1FE}","\u{1F1FA}\u{1F1FF}","\u{1F1FB}\u{1F1E6}","\u{1F1FB}\u{1F1E8}","\u{1F1FB}\u{1F1EA}","\u{1F1FB}\u{1F1EC}","\u{1F1FB}\u{1F1EE}","\u{1F1FB}\u{1F1F3}","\u{1F1FB}\u{1F1FA}","\u{1F1FC}\u{1F1EB}","\u{1F1FC}\u{1F1F8}","\u{1F1FD}\u{1F1F0}","\u{1F1FE}\u{1F1EA}","\u{1F1FE}\u{1F1F9}","\u{1F1FF}\u{1F1E6}","\u{1F1FF}\u{1F1F2}","\u{1F1FF}\u{1F1FC}","\u{1F38C}","\u{1F3C1}","\u{1F3F3}\uFE0F","\u{1F3F3}\uFE0F\u200D\u26A7\uFE0F","\u{1F3F3}\uFE0F\u200D\u{1F308}","\u{1F3F4}","\u{1F3F4}\u200D\u2620\uFE0F","\u{1F6A9}"]};var cr={informational:[100,101,102,103],success:[200,201,202,203,204,205,206,207,208,226],redirection:[300,301,302,303,304,305,306,307,308],clientError:[400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,428,429,431,451],serverError:[500,501,502,503,504,505,506,507,508,510,511]};var lr=["ES256","ES384","ES512","HS256","HS384","HS512","PS256","PS384","PS512","RS256","RS384","RS512","none"];var mr=["FakerBot/{{system.semver}}","Googlebot/2.1 (+http://www.google.com/bot.html)",'Mozilla/5.0 (Linux; Android {{number.int({"min":5,"max":13})}}; {{helpers.arrayElement(["SM-G998U","SM-G998B","SM-G998N","SM-G998P","SM-T800"])}}) AppleWebKit/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}} (KHTML, like Gecko) Chrome/{{number.int({"min":55,"max":131})}}.{{system.semver}} Mobile Safari/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}','Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:{{number.int({"min":75, "max":133})}}.0) Gecko/20100101 Firefox/{{number.int({"min":75, "max":133})}}.0','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}.{{number.int({"min":0,"max":99})}} (KHTML, like Gecko) Version/16.1 Safari/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}.{{number.int({"min":0,"max":99})}}','Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_15_7) AppleWebKit/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}.{{number.int({"min":0,"max":99})}} (KHTML, like Gecko) Chrome/{{number.int({"min":55,"max":131})}}.{{system.semver}} Safari/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}.{{number.int({"min":0,"max":99})}}','Mozilla/5.0 (Windows NT {{helpers.arrayElement(["5.1","5.2","6.0","6.1","6.2","6.3","10.0"])}}; Win64; x64) AppleWebKit/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}} (KHTML, like Gecko) Chrome/{{number.int({"min":55,"max":131})}}.{{system.semver}} Safari/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}} Edg/{{number.int({"min":110,"max":131})}}.{{system.semver}}','Mozilla/5.0 (X11; Linux x86_64; rv:{{number.int({"min":75,"max":133})}}.0) Gecko/20100101 Firefox/{{number.int({"min":75,"max":133})}}.0','Mozilla/5.0 (compatible; MSIE {{number.int({"min":6,"max":10})}}.0; Windows NT {{helpers.arrayElement(["5.1","5.2","6.0","6.1","6.2","6.3","10.0"])}}; Trident/{{number.int({"min":4,"max":7})}}.0)','Mozilla/5.0 (iPhone; CPU iPhone OS {{number.int({"min":10,"max":18})}}_{{number.int({"min":0,"max":4})}} like Mac OS X) AppleWebKit/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}.{{number.int({"min":0,"max":99})}} (KHTML, like Gecko) Version/{{number.int({"min":10,"max":18})}}_{{number.int({"min":0,"max":4})}} Mobile/15E148 Safari/{{number.int({"min":536,"max":605})}}.{{number.int({"min":0,"max":99})}}'];var tt={emoji:sr,http_status_code:cr,jwt_algorithm:lr,user_agent_pattern:mr},ur=tt;var pr=[{alpha2:"AD",alpha3:"AND",numeric:"020"},{alpha2:"AE",alpha3:"ARE",numeric:"784"},{alpha2:"AF",alpha3:"AFG",numeric:"004"},{alpha2:"AG",alpha3:"ATG",numeric:"028"},{alpha2:"AI",alpha3:"AIA",numeric:"660"},{alpha2:"AL",alpha3:"ALB",numeric:"008"},{alpha2:"AM",alpha3:"ARM",numeric:"051"},{alpha2:"AO",alpha3:"AGO",numeric:"024"},{alpha2:"AQ",alpha3:"ATA",numeric:"010"},{alpha2:"AR",alpha3:"ARG",numeric:"032"},{alpha2:"AS",alpha3:"ASM",numeric:"016"},{alpha2:"AT",alpha3:"AUT",numeric:"040"},{alpha2:"AU",alpha3:"AUS",numeric:"036"},{alpha2:"AW",alpha3:"ABW",numeric:"533"},{alpha2:"AX",alpha3:"ALA",numeric:"248"},{alpha2:"AZ",alpha3:"AZE",numeric:"031"},{alpha2:"BA",alpha3:"BIH",numeric:"070"},{alpha2:"BB",alpha3:"BRB",numeric:"052"},{alpha2:"BD",alpha3:"BGD",numeric:"050"},{alpha2:"BE",alpha3:"BEL",numeric:"056"},{alpha2:"BF",alpha3:"BFA",numeric:"854"},{alpha2:"BG",alpha3:"BGR",numeric:"100"},{alpha2:"BH",alpha3:"BHR",numeric:"048"},{alpha2:"BI",alpha3:"BDI",numeric:"108"},{alpha2:"BJ",alpha3:"BEN",numeric:"204"},{alpha2:"BL",alpha3:"BLM",numeric:"652"},{alpha2:"BM",alpha3:"BMU",numeric:"060"},{alpha2:"BN",alpha3:"BRN",numeric:"096"},{alpha2:"BO",alpha3:"BOL",numeric:"068"},{alpha2:"BQ",alpha3:"BES",numeric:"535"},{alpha2:"BR",alpha3:"BRA",numeric:"076"},{alpha2:"BS",alpha3:"BHS",numeric:"044"},{alpha2:"BT",alpha3:"BTN",numeric:"064"},{alpha2:"BV",alpha3:"BVT",numeric:"074"},{alpha2:"BW",alpha3:"BWA",numeric:"072"},{alpha2:"BY",alpha3:"BLR",numeric:"112"},{alpha2:"BZ",alpha3:"BLZ",numeric:"084"},{alpha2:"CA",alpha3:"CAN",numeric:"124"},{alpha2:"CC",alpha3:"CCK",numeric:"166"},{alpha2:"CD",alpha3:"COD",numeric:"180"},{alpha2:"CF",alpha3:"CAF",numeric:"140"},{alpha2:"CG",alpha3:"COG",numeric:"178"},{alpha2:"CH",alpha3:"CHE",numeric:"756"},{alpha2:"CI",alpha3:"CIV",numeric:"384"},{alpha2:"CK",alpha3:"COK",numeric:"184"},{alpha2:"CL",alpha3:"CHL",numeric:"152"},{alpha2:"CM",alpha3:"CMR",numeric:"120"},{alpha2:"CN",alpha3:"CHN",numeric:"156"},{alpha2:"CO",alpha3:"COL",numeric:"170"},{alpha2:"CR",alpha3:"CRI",numeric:"188"},{alpha2:"CU",alpha3:"CUB",numeric:"192"},{alpha2:"CV",alpha3:"CPV",numeric:"132"},{alpha2:"CW",alpha3:"CUW",numeric:"531"},{alpha2:"CX",alpha3:"CXR",numeric:"162"},{alpha2:"CY",alpha3:"CYP",numeric:"196"},{alpha2:"CZ",alpha3:"CZE",numeric:"203"},{alpha2:"DE",alpha3:"DEU",numeric:"276"},{alpha2:"DJ",alpha3:"DJI",numeric:"262"},{alpha2:"DK",alpha3:"DNK",numeric:"208"},{alpha2:"DM",alpha3:"DMA",numeric:"212"},{alpha2:"DO",alpha3:"DOM",numeric:"214"},{alpha2:"DZ",alpha3:"DZA",numeric:"012"},{alpha2:"EC",alpha3:"ECU",numeric:"218"},{alpha2:"EE",alpha3:"EST",numeric:"233"},{alpha2:"EG",alpha3:"EGY",numeric:"818"},{alpha2:"EH",alpha3:"ESH",numeric:"732"},{alpha2:"ER",alpha3:"ERI",numeric:"232"},{alpha2:"ES",alpha3:"ESP",numeric:"724"},{alpha2:"ET",alpha3:"ETH",numeric:"231"},{alpha2:"FI",alpha3:"FIN",numeric:"246"},{alpha2:"FJ",alpha3:"FJI",numeric:"242"},{alpha2:"FK",alpha3:"FLK",numeric:"238"},{alpha2:"FM",alpha3:"FSM",numeric:"583"},{alpha2:"FO",alpha3:"FRO",numeric:"234"},{alpha2:"FR",alpha3:"FRA",numeric:"250"},{alpha2:"GA",alpha3:"GAB",numeric:"266"},{alpha2:"GB",alpha3:"GBR",numeric:"826"},{alpha2:"GD",alpha3:"GRD",numeric:"308"},{alpha2:"GE",alpha3:"GEO",numeric:"268"},{alpha2:"GF",alpha3:"GUF",numeric:"254"},{alpha2:"GG",alpha3:"GGY",numeric:"831"},{alpha2:"GH",alpha3:"GHA",numeric:"288"},{alpha2:"GI",alpha3:"GIB",numeric:"292"},{alpha2:"GL",alpha3:"GRL",numeric:"304"},{alpha2:"GM",alpha3:"GMB",numeric:"270"},{alpha2:"GN",alpha3:"GIN",numeric:"324"},{alpha2:"GP",alpha3:"GLP",numeric:"312"},{alpha2:"GQ",alpha3:"GNQ",numeric:"226"},{alpha2:"GR",alpha3:"GRC",numeric:"300"},{alpha2:"GS",alpha3:"SGS",numeric:"239"},{alpha2:"GT",alpha3:"GTM",numeric:"320"},{alpha2:"GU",alpha3:"GUM",numeric:"316"},{alpha2:"GW",alpha3:"GNB",numeric:"624"},{alpha2:"GY",alpha3:"GUY",numeric:"328"},{alpha2:"HK",alpha3:"HKG",numeric:"344"},{alpha2:"HM",alpha3:"HMD",numeric:"334"},{alpha2:"HN",alpha3:"HND",numeric:"340"},{alpha2:"HR",alpha3:"HRV",numeric:"191"},{alpha2:"HT",alpha3:"HTI",numeric:"332"},{alpha2:"HU",alpha3:"HUN",numeric:"348"},{alpha2:"ID",alpha3:"IDN",numeric:"360"},{alpha2:"IE",alpha3:"IRL",numeric:"372"},{alpha2:"IL",alpha3:"ISR",numeric:"376"},{alpha2:"IM",alpha3:"IMN",numeric:"833"},{alpha2:"IN",alpha3:"IND",numeric:"356"},{alpha2:"IO",alpha3:"IOT",numeric:"086"},{alpha2:"IQ",alpha3:"IRQ",numeric:"368"},{alpha2:"IR",alpha3:"IRN",numeric:"364"},{alpha2:"IS",alpha3:"ISL",numeric:"352"},{alpha2:"IT",alpha3:"ITA",numeric:"380"},{alpha2:"JE",alpha3:"JEY",numeric:"832"},{alpha2:"JM",alpha3:"JAM",numeric:"388"},{alpha2:"JO",alpha3:"JOR",numeric:"400"},{alpha2:"JP",alpha3:"JPN",numeric:"392"},{alpha2:"KE",alpha3:"KEN",numeric:"404"},{alpha2:"KG",alpha3:"KGZ",numeric:"417"},{alpha2:"KH",alpha3:"KHM",numeric:"116"},{alpha2:"KI",alpha3:"KIR",numeric:"296"},{alpha2:"KM",alpha3:"COM",numeric:"174"},{alpha2:"KN",alpha3:"KNA",numeric:"659"},{alpha2:"KP",alpha3:"PRK",numeric:"408"},{alpha2:"KR",alpha3:"KOR",numeric:"410"},{alpha2:"KW",alpha3:"KWT",numeric:"414"},{alpha2:"KY",alpha3:"CYM",numeric:"136"},{alpha2:"KZ",alpha3:"KAZ",numeric:"398"},{alpha2:"LA",alpha3:"LAO",numeric:"418"},{alpha2:"LB",alpha3:"LBN",numeric:"422"},{alpha2:"LC",alpha3:"LCA",numeric:"662"},{alpha2:"LI",alpha3:"LIE",numeric:"438"},{alpha2:"LK",alpha3:"LKA",numeric:"144"},{alpha2:"LR",alpha3:"LBR",numeric:"430"},{alpha2:"LS",alpha3:"LSO",numeric:"426"},{alpha2:"LT",alpha3:"LTU",numeric:"440"},{alpha2:"LU",alpha3:"LUX",numeric:"442"},{alpha2:"LV",alpha3:"LVA",numeric:"428"},{alpha2:"LY",alpha3:"LBY",numeric:"434"},{alpha2:"MA",alpha3:"MAR",numeric:"504"},{alpha2:"MC",alpha3:"MCO",numeric:"492"},{alpha2:"MD",alpha3:"MDA",numeric:"498"},{alpha2:"ME",alpha3:"MNE",numeric:"499"},{alpha2:"MF",alpha3:"MAF",numeric:"663"},{alpha2:"MG",alpha3:"MDG",numeric:"450"},{alpha2:"MH",alpha3:"MHL",numeric:"584"},{alpha2:"MK",alpha3:"MKD",numeric:"807"},{alpha2:"ML",alpha3:"MLI",numeric:"466"},{alpha2:"MM",alpha3:"MMR",numeric:"104"},{alpha2:"MN",alpha3:"MNG",numeric:"496"},{alpha2:"MO",alpha3:"MAC",numeric:"446"},{alpha2:"MP",alpha3:"MNP",numeric:"580"},{alpha2:"MQ",alpha3:"MTQ",numeric:"474"},{alpha2:"MR",alpha3:"MRT",numeric:"478"},{alpha2:"MS",alpha3:"MSR",numeric:"500"},{alpha2:"MT",alpha3:"MLT",numeric:"470"},{alpha2:"MU",alpha3:"MUS",numeric:"480"},{alpha2:"MV",alpha3:"MDV",numeric:"462"},{alpha2:"MW",alpha3:"MWI",numeric:"454"},{alpha2:"MX",alpha3:"MEX",numeric:"484"},{alpha2:"MY",alpha3:"MYS",numeric:"458"},{alpha2:"MZ",alpha3:"MOZ",numeric:"508"},{alpha2:"NA",alpha3:"NAM",numeric:"516"},{alpha2:"NC",alpha3:"NCL",numeric:"540"},{alpha2:"NE",alpha3:"NER",numeric:"562"},{alpha2:"NF",alpha3:"NFK",numeric:"574"},{alpha2:"NG",alpha3:"NGA",numeric:"566"},{alpha2:"NI",alpha3:"NIC",numeric:"558"},{alpha2:"NL",alpha3:"NLD",numeric:"528"},{alpha2:"NO",alpha3:"NOR",numeric:"578"},{alpha2:"NP",alpha3:"NPL",numeric:"524"},{alpha2:"NR",alpha3:"NRU",numeric:"520"},{alpha2:"NU",alpha3:"NIU",numeric:"570"},{alpha2:"NZ",alpha3:"NZL",numeric:"554"},{alpha2:"OM",alpha3:"OMN",numeric:"512"},{alpha2:"PA",alpha3:"PAN",numeric:"591"},{alpha2:"PE",alpha3:"PER",numeric:"604"},{alpha2:"PF",alpha3:"PYF",numeric:"258"},{alpha2:"PG",alpha3:"PNG",numeric:"598"},{alpha2:"PH",alpha3:"PHL",numeric:"608"},{alpha2:"PK",alpha3:"PAK",numeric:"586"},{alpha2:"PL",alpha3:"POL",numeric:"616"},{alpha2:"PM",alpha3:"SPM",numeric:"666"},{alpha2:"PN",alpha3:"PCN",numeric:"612"},{alpha2:"PR",alpha3:"PRI",numeric:"630"},{alpha2:"PS",alpha3:"PSE",numeric:"275"},{alpha2:"PT",alpha3:"PRT",numeric:"620"},{alpha2:"PW",alpha3:"PLW",numeric:"585"},{alpha2:"PY",alpha3:"PRY",numeric:"600"},{alpha2:"QA",alpha3:"QAT",numeric:"634"},{alpha2:"RE",alpha3:"REU",numeric:"638"},{alpha2:"RO",alpha3:"ROU",numeric:"642"},{alpha2:"RS",alpha3:"SRB",numeric:"688"},{alpha2:"RU",alpha3:"RUS",numeric:"643"},{alpha2:"RW",alpha3:"RWA",numeric:"646"},{alpha2:"SA",alpha3:"SAU",numeric:"682"},{alpha2:"SB",alpha3:"SLB",numeric:"090"},{alpha2:"SC",alpha3:"SYC",numeric:"690"},{alpha2:"SD",alpha3:"SDN",numeric:"729"},{alpha2:"SE",alpha3:"SWE",numeric:"752"},{alpha2:"SG",alpha3:"SGP",numeric:"702"},{alpha2:"SH",alpha3:"SHN",numeric:"654"},{alpha2:"SI",alpha3:"SVN",numeric:"705"},{alpha2:"SJ",alpha3:"SJM",numeric:"744"},{alpha2:"SK",alpha3:"SVK",numeric:"703"},{alpha2:"SL",alpha3:"SLE",numeric:"694"},{alpha2:"SM",alpha3:"SMR",numeric:"674"},{alpha2:"SN",alpha3:"SEN",numeric:"686"},{alpha2:"SO",alpha3:"SOM",numeric:"706"},{alpha2:"SR",alpha3:"SUR",numeric:"740"},{alpha2:"SS",alpha3:"SSD",numeric:"728"},{alpha2:"ST",alpha3:"STP",numeric:"678"},{alpha2:"SV",alpha3:"SLV",numeric:"222"},{alpha2:"SX",alpha3:"SXM",numeric:"534"},{alpha2:"SY",alpha3:"SYR",numeric:"760"},{alpha2:"SZ",alpha3:"SWZ",numeric:"748"},{alpha2:"TC",alpha3:"TCA",numeric:"796"},{alpha2:"TD",alpha3:"TCD",numeric:"148"},{alpha2:"TF",alpha3:"ATF",numeric:"260"},{alpha2:"TG",alpha3:"TGO",numeric:"768"},{alpha2:"TH",alpha3:"THA",numeric:"764"},{alpha2:"TJ",alpha3:"TJK",numeric:"762"},{alpha2:"TK",alpha3:"TKL",numeric:"772"},{alpha2:"TL",alpha3:"TLS",numeric:"626"},{alpha2:"TM",alpha3:"TKM",numeric:"795"},{alpha2:"TN",alpha3:"TUN",numeric:"788"},{alpha2:"TO",alpha3:"TON",numeric:"776"},{alpha2:"TR",alpha3:"TUR",numeric:"792"},{alpha2:"TT",alpha3:"TTO",numeric:"780"},{alpha2:"TV",alpha3:"TUV",numeric:"798"},{alpha2:"TW",alpha3:"TWN",numeric:"158"},{alpha2:"TZ",alpha3:"TZA",numeric:"834"},{alpha2:"UA",alpha3:"UKR",numeric:"804"},{alpha2:"UG",alpha3:"UGA",numeric:"800"},{alpha2:"UM",alpha3:"UMI",numeric:"581"},{alpha2:"US",alpha3:"USA",numeric:"840"},{alpha2:"UY",alpha3:"URY",numeric:"858"},{alpha2:"UZ",alpha3:"UZB",numeric:"860"},{alpha2:"VA",alpha3:"VAT",numeric:"336"},{alpha2:"VC",alpha3:"VCT",numeric:"670"},{alpha2:"VE",alpha3:"VEN",numeric:"862"},{alpha2:"VG",alpha3:"VGB",numeric:"092"},{alpha2:"VI",alpha3:"VIR",numeric:"850"},{alpha2:"VN",alpha3:"VNM",numeric:"704"},{alpha2:"VU",alpha3:"VUT",numeric:"548"},{alpha2:"WF",alpha3:"WLF",numeric:"876"},{alpha2:"WS",alpha3:"WSM",numeric:"882"},{alpha2:"YE",alpha3:"YEM",numeric:"887"},{alpha2:"YT",alpha3:"MYT",numeric:"175"},{alpha2:"ZA",alpha3:"ZAF",numeric:"710"},{alpha2:"ZM",alpha3:"ZMB",numeric:"894"},{alpha2:"ZW",alpha3:"ZWE",numeric:"716"}];var at={country_code:pr,time_zone:I},hr=at;var nt={title:"Base",code:"base"},fr=nt;var br=["/Applications","/bin","/boot","/boot/defaults","/dev","/etc","/etc/defaults","/etc/mail","/etc/namedb","/etc/periodic","/etc/ppp","/home","/home/<USER>","/home/<USER>/dir","/lib","/Library","/lost+found","/media","/mnt","/net","/Network","/opt","/opt/bin","/opt/include","/opt/lib","/opt/sbin","/opt/share","/private","/private/tmp","/private/var","/proc","/rescue","/root","/sbin","/selinux","/srv","/sys","/System","/tmp","/Users","/usr","/usr/X11R6","/usr/bin","/usr/include","/usr/lib","/usr/libdata","/usr/libexec","/usr/local/bin","/usr/local/src","/usr/obj","/usr/ports","/usr/sbin","/usr/share","/usr/src","/var","/var/log","/var/mail","/var/spool","/var/tmp","/var/yp"];var dr={"application/epub+zip":{extensions:["epub"]},"application/gzip":{extensions:["gz"]},"application/java-archive":{extensions:["jar","war","ear"]},"application/json":{extensions:["json","map"]},"application/ld+json":{extensions:["jsonld"]},"application/msword":{extensions:["doc","dot"]},"application/octet-stream":{extensions:["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/ogg":{extensions:["ogx"]},"application/pdf":{extensions:["pdf"]},"application/rtf":{extensions:["rtf"]},"application/vnd.amazon.ebook":{extensions:["azw"]},"application/vnd.apple.installer+xml":{extensions:["mpkg"]},"application/vnd.mozilla.xul+xml":{extensions:["xul"]},"application/vnd.ms-excel":{extensions:["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-fontobject":{extensions:["eot"]},"application/vnd.ms-powerpoint":{extensions:["ppt","pps","pot"]},"application/vnd.oasis.opendocument.presentation":{extensions:["odp"]},"application/vnd.oasis.opendocument.spreadsheet":{extensions:["ods"]},"application/vnd.oasis.opendocument.text":{extensions:["odt"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{extensions:["pptx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{extensions:["xlsx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{extensions:["docx"]},"application/vnd.rar":{extensions:["rar"]},"application/vnd.visio":{extensions:["vsd","vst","vss","vsw"]},"application/x-7z-compressed":{extensions:["7z"]},"application/x-abiword":{extensions:["abw"]},"application/x-bzip":{extensions:["bz"]},"application/x-bzip2":{extensions:["bz2","boz"]},"application/x-csh":{extensions:["csh"]},"application/x-freearc":{extensions:["arc"]},"application/x-httpd-php":{extensions:["php"]},"application/x-sh":{extensions:["sh"]},"application/x-tar":{extensions:["tar"]},"application/xhtml+xml":{extensions:["xhtml","xht"]},"application/xml":{extensions:["xml","xsl","xsd","rng"]},"application/zip":{extensions:["zip"]},"audio/3gpp":{extensions:["3gpp"]},"audio/3gpp2":{extensions:["3g2"]},"audio/aac":{extensions:["aac"]},"audio/midi":{extensions:["mid","midi","kar","rmi"]},"audio/mpeg":{extensions:["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/ogg":{extensions:["oga","ogg","spx","opus"]},"audio/opus":{extensions:["opus"]},"audio/wav":{extensions:["wav"]},"audio/webm":{extensions:["weba"]},"font/otf":{extensions:["otf"]},"font/ttf":{extensions:["ttf"]},"font/woff":{extensions:["woff"]},"font/woff2":{extensions:["woff2"]},"image/avif":{extensions:["avif"]},"image/bmp":{extensions:["bmp"]},"image/gif":{extensions:["gif"]},"image/jpeg":{extensions:["jpeg","jpg","jpe"]},"image/png":{extensions:["png"]},"image/svg+xml":{extensions:["svg","svgz"]},"image/tiff":{extensions:["tif","tiff"]},"image/vnd.microsoft.icon":{extensions:["ico"]},"image/webp":{extensions:["webp"]},"text/calendar":{extensions:["ics","ifb"]},"text/css":{extensions:["css"]},"text/csv":{extensions:["csv"]},"text/html":{extensions:["html","htm","shtml"]},"text/javascript":{extensions:["js","mjs"]},"text/plain":{extensions:["txt","text","conf","def","list","log","in","ini"]},"video/3gpp":{extensions:["3gp","3gpp"]},"video/3gpp2":{extensions:["3g2"]},"video/mp2t":{extensions:["ts"]},"video/mp4":{extensions:["mp4","mp4v","mpg4"]},"video/mpeg":{extensions:["mpeg","mpg","mpe","m1v","m2v"]},"video/ogg":{extensions:["ogv"]},"video/webm":{extensions:["webm"]},"video/x-msvideo":{extensions:["avi"]}};var it={directory_path:br,mime_type:dr},gr=it;var ot={color:qe,database:ar,date:nr,hacker:or,internet:ur,location:hr,metadata:fr,system:gr},Fi= exports.o =ot;exports.a = m; exports.b = Ne; exports.c = De; exports.d = Re; exports.e = be; exports.f = Pe; exports.g = Lr; exports.h = _e; exports.i = Lt; exports.j = Oe; exports.k = $; exports.l = fa; exports.m = We; exports.n = Xe; exports.o = Fi;
