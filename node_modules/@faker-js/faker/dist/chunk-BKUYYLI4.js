var e=["Ours blanc","Ours brun","Ours lippu","Ours noir","Ours \xE0 collier","Ours \xE0 lunettes","Panda g\xE9ant"];var r=["Accenteur alpin","Accenteur de Radde","Accenteur montanelle","Accenteur mouchet","Accenteur \xE0 gorge noire","Agrobate roux","Aigle bott\xE9","Aigle criard","Aigle de Bonelli","Aigle des steppes","Aigle ib\xE9rique","Aigle imp\xE9rial","Aigle pomarin","Aigle ravisseur","Aigle royal","Aigrette ardois\xE9e","Aigrette bleue","Aigrette des r\xE9cifs","Aigrette garzette","Aigrette neigeuse","Aigrette tricolore","<PERSON><PERSON><PERSON> de Tristan","<PERSON><PERSON><PERSON> de Tristan da Cunha","<PERSON><PERSON><PERSON> hurleur","<PERSON><PERSON><PERSON> \xE0 nez jaune","Alouette bilophe","Alouette calandre","<PERSON>ouette calandrelle","<PERSON><PERSON><PERSON> <PERSON>-<PERSON>","<PERSON><PERSON><PERSON> de Du<PERSON>","<PERSON><PERSON>ette de Swinhoe","Alouette gulgule","Alouette hausse-col","Alouette leucopt\xE8re","Alouette lulu","Alouette monticole","Alouette pispolette","Amadine cou-coup\xE9","Ammomane isabelline","Ammomane \xE9l\xE9gante","Anhinga d'Afrique","Arlequin plongeur","Astrild cendr\xE9","Autour des palombes","Avocette d'Am\xE9rique","Avocette \xE9l\xE9gante","Balbuzard p\xEAcheur","Barge hudsonienne","Barge rousse","Barge \xE0 queue noire","Bartramie des champs","Bateleur des savanes","Bec-crois\xE9 bifasci\xE9","Bec-crois\xE9 d'Ecosse","Bec-crois\xE9 d'\xC9cosse","Bec-crois\xE9 des sapins","Bec-en-ciseaux noir","Bengali rouge","Bergeronnette citrine","Bergeronnette de B\xE9ringie","Bergeronnette des ruisseaux","Bergeronnette printani\xE8re","Bernache cravant","Bernache de Hutchins","Bernache nonnette","Bernache \xE0 cou roux","Bihoreau gris","Bihoreau violac\xE9","Blongios de Schrenck","Blongios de Sturm","Blongios mandchou","Blongios nain","Bondr\xE9e apivore","Bondr\xE9e orientale","Bouvreuil des A\xE7ores","Bouvreuil pivoine","Bruant ardois\xE9","Bruant cendrillard","Bruant cendr\xE9","Bruant chanteur","Bruant de Lincoln","Bruant des neiges","Bruant des pr\xE9s","Bruant des roseaux","Bruant du Sahara","Bruant fauve","Bruant fuligineux","Bruant hudsonien","Bruant jaune","Bruant lapon","Bruant masqu\xE9","Bruant m\xE9lanoc\xE9phale","Bruant ortolan","Bruant proyer","Bruant roux","Bruant rustique","Bruant striol\xE9","Bruant zizi","Bruant \xE0 calotte blanche","Bruant \xE0 cou gris","Bruant \xE0 couronne blanche","Bruant \xE0 gorge blanche","Bruant \xE0 joues marron","Bruant \xE0 oreillons","Bruant \xE0 sourcils jaunes","Bruant \xE0 t\xEAte rousse","Bulbul des jardins","Bulbul orph\xE9e","Busard Saint-Martin","Busard cendr\xE9","Busard d'Am\xE9rique","Busard des marais","Busard des roseaux","Busard p\xE2le","Buse de Chine","Buse de Swainson","Buse f\xE9roce","Buse pattue","Buse variable","Butor d'Am\xE9rique","Butor \xE9toil\xE9","B\xE9casse d'Am\xE9rique","B\xE9casse des bois","B\xE9casseau cocorli","B\xE9casseau d'Alaska","B\xE9casseau de Baird","B\xE9casseau de Bonaparte","B\xE9casseau de l'Anadyr","B\xE9casseau falcinelle","B\xE9casseau maub\xE8che","B\xE9casseau minuscule","B\xE9casseau minute","B\xE9casseau rousset","B\xE9casseau rouss\xE2tre","B\xE9casseau sanderling","B\xE9casseau semipalm\xE9","B\xE9casseau spatule","B\xE9casseau tachet\xE9","B\xE9casseau variable","B\xE9casseau violet","B\xE9casseau \xE0 col roux","B\xE9casseau \xE0 cou roux","B\xE9casseau \xE0 croupion blanc","B\xE9casseau \xE0 longs doigts","B\xE9casseau \xE0 poitrine cendr\xE9e","B\xE9casseau \xE0 queue pointue","B\xE9casseau \xE0 \xE9chasses","B\xE9cassin roux","B\xE9cassin \xE0 bec court","B\xE9cassine de Swinhoe","B\xE9cassine de Wilson","B\xE9cassine des marais","B\xE9cassine double","B\xE9cassine sourde","B\xE9cassine \xE0 queue pointue","Caille des bl\xE9s","Canard branchu","Canard carolin","Canard chipeau","Canard colvert","Canard d'Am\xE9rique","Canard mandarin","Canard musqu\xE9","Canard noir","Canard pilet","Canard siffleur","Canard souchet","Canard \xE0 faucilles","Canard \xE0 front blanc","Capucin bec-d'argent","Capucin damier","Capucin \xE0 dos marron","Capucin \xE0 t\xEAte noire","Cardinal \xE0 poitrine rose","Carouge \xE0 t\xEAte jaune","Carouge \xE0 \xE9paulettes","Cassenoix mouchet\xE9","Chardonneret \xE9l\xE9gant","Chevalier arlequin","Chevalier bargette","Chevalier criard","Chevalier culblanc","Chevalier de Sib\xE9rie","Chevalier gambette","Chevalier grivel\xE9","Chevalier guignette","Chevalier semipalm\xE9","Chevalier solitaire","Chevalier stagnatile","Chevalier sylvain","Chevalier \xE0 pattes jaunes","Chev\xEAche d'Ath\xE9na","Chev\xEAchette d'Europe","Chionis blanc","Chocard \xE0 bec jaune","Choucador \xE0 oreillons bleus","Choucas de Daourie","Choucas des tours","Chouette de Tengmalm","Chouette de l'Oural","Chouette effraie","Chouette hulotte","Chouette hulotte (mauritanica)","Chouette hulotte africaine","Chouette lapone","Chouette \xE9pervi\xE8re","Cigogne blanche","Cigogne noire","Cincle plongeur","Circa\xE8te Jean-le-Blanc","Cisticole des joncs","Cochevis de Th\xE9kla","Cochevis hupp\xE9","Colin de Virginie","Combattant vari\xE9","Conure veuve","Conure \xE0 t\xEAte rouge","Corbeau brun","Corbeau familier","Corbeau freux","Corbeau pie","Corbeau \xE0 queue courte","Cormoran africain","Cormoran hupp\xE9","Cormoran pygm\xE9e","Cormoran \xE0 aigrettes","Cormoran \xE0 poitrine blanche","Corneille mantel\xE9e","Corneille noire","Coucou de Chine","Coucou de l'Himalaya","Coucou geai","Coucou gris","Coucou oriental","Coulicou \xE0 bec jaune","Coulicou \xE0 bec noir","Courlis cendr\xE9","Courlis corlieu","Courlis hudsonien","Courlis nain","Courlis \xE0 bec gr\xEAle","Courvite isabelle","Crabier chevelu","Crabier chinois","Crabier de Gray","Crave \xE0 bec rouge","Cr\xE9cerelle d'Am\xE9rique","Cygne chanteur","Cygne de Bewick","Cygne noir","Cygne siffleur","Cygne tubercul\xE9","Damier du Cap","Dendrocygne fauve","Dendrocygne veuf","Diamant mandarin","Dickcissel d'Am\xE9rique","Durbec des sapins","Effraie d'Am\xE9rique","Effraie des clochers","Eider \xE0 duvet","Eider \xE0 lunettes","Eider \xE0 t\xEAte grise","Engoulevent d'Am\xE9rique","Engoulevent d'Europe","Engoulevent du d\xE9sert","Engoulevent \xE0 collier roux","Euplecte vorab\xE9","Faisan de Colchide","Faisan de Lady Amherst","Faisan dor\xE9","Faisan v\xE9n\xE9r\xE9","Faucon concolore","Faucon cr\xE9cerelle","Faucon cr\xE9cerellette","Faucon d'\xC9l\xE9onore","Faucon de l'Amour","Faucon gerfaut","Faucon hobereau","Faucon kobez","Faucon lanier","Faucon p\xE8lerin","Faucon sacre","Faucon \xE9merillon","Fauvette babillarde","Fauvette de Chypre","Fauvette de Hume","Fauvette de Moltoni","Fauvette de M\xE9n\xE9tries","Fauvette de R\xFCppell","Fauvette de l'Atlas","Fauvette des Balkans","Fauvette des jardins","Fauvette du d\xE9sert","Fauvette grisette","Fauvette m\xE9lanoc\xE9phale","Fauvette naine","Fauvette orph\xE9ane","Fauvette orph\xE9e","Fauvette pitchou","Fauvette sarde","Fauvette \xE0 lunettes","Fauvette \xE0 t\xEAte noire","Fauvette \xE9pervi\xE8re","Flamant des Cara\xEFbes","Flamant du Chili","Flamant nain","Flamant rose","Fou brun","Fou de Bassan","Fou du Cap","Fou masqu\xE9","Foulque caroncul\xE9e","Foulque d'Am\xE9rique","Foulque macroule","Foulque \xE0 cr\xEAte","Francolin noir","Fr\xE9gate aigle-de-mer","Fr\xE9gate superbe","Fuligule milouin","Fuligule milouinan","Fuligule morillon","Fuligule nyroca","Fuligule \xE0 bec cercl\xE9","Fuligule \xE0 collier","Fuligule \xE0 dos blanc","Fuligule \xE0 t\xEAte noire","Fuligule \xE0 t\xEAte rouge","Fulmar bor\xE9al","Gallinule africaine","Gallinule d'Am\xE9rique","Gallinule poule-d'eau","Ganga cata","Ganga tachet\xE9","Ganga unibande","Ganga \xE0 ventre brun","Garde-boeufs d'Asie","Gardeboeuf d'Asie","Garrot alb\xE9ole","Garrot d'Islande","Garrot \xE0 oeil d'or","Geai des ch\xEAnes","Glar\xE9ole orientale","Glar\xE9ole \xE0 ailes noires","Glar\xE9ole \xE0 collier","Gobemouche brun","Gobemouche de Sib\xE9rie","Gobemouche de l'Atlas","Gobemouche de la ta\xEFga","Gobemouche gris","Gobemouche mugimaki","Gobemouche nain","Gobemouche noir","Gobemouche \xE0 collier","Gobemouche \xE0 demi-collier","Goglu des pr\xE9s","Gorgebleue \xE0 miroir","Go\xE9land arctique","Go\xE9land argent\xE9","Go\xE9land bourgmestre","Go\xE9land brun","Go\xE9land cendr\xE9","Go\xE9land d'Am\xE9rique","Go\xE9land d'Arm\xE9nie","Go\xE9land d'Audouin","Go\xE9land de la V\xE9ga","Go\xE9land dominicain","Go\xE9land hudsonien","Go\xE9land ichthya\xE8te","Go\xE9land leucoph\xE9e","Go\xE9land marin","Go\xE9land \xE0 ailes blanches","Go\xE9land \xE0 ailes grises","Go\xE9land \xE0 bec cercl\xE9","Go\xE9land \xE0 iris blanc","Go\xE9land \xE0 manteau ardois\xE9","Go\xE9land \xE0 queue noire","Grand Chevalier","Grand Corbeau","Grand Cormoran","Grand Gravelot","Grand Harle","Grand H\xE9ron","Grand Labbe","Grand T\xE9tras","Grand-duc ascalaphe","Grand-duc d'Europe","Grande Aigrette","Grande Outarde","Gravelot de Leschenault","Gravelot kildir","Gravelot mongol","Gravelot neigeux","Gravelot oriental","Gravelot p\xE2tre","Gravelot semipalm\xE9","Grimpereau des bois","Grimpereau des jardins","Grive de Sib\xE9rie","Grive des bois","Grive fauve","Grive litorne","Grive mauvis","Grive musicienne","Grive obscure","Grive solitaire","Grive \xE0 ailes rousses","Grive \xE0 collier","Grive \xE0 dos olive","Grive \xE0 gorge noire","Grive \xE0 gorge rousse","Grive \xE0 joues grises","Gros-bec casse-noyaux","Gros-bec errant","Grosbec casse-noyaux","Grosbec errant","Grue cendr\xE9e","Grue de Sib\xE9rie","Grue demoiselle","Grue du Canada","Gr\xE8be castagneux","Gr\xE8be esclavon","Gr\xE8be hupp\xE9","Gr\xE8be jougris","Gr\xE8be \xE0 bec bigarr\xE9","Gr\xE8be \xE0 cou noir","Guifette leucopt\xE8re","Guifette moustac","Guifette noire","Guillemot colombin","Guillemot de Br\xFCnnich","Guillemot de Tro\xEFl","Guillemot \xE0 long bec","Guillemot \xE0 miroir","Guiraca bleu","Gu\xEApier d'Orient","G\xE9linotte des bois","Harelde bor\xE9ale","Harelde kakawi","Harfang des neiges","Harle couronn\xE9","Harle hupp\xE9","Harle piette","Hibou des marais","Hibou du Cap","Hibou moyen-duc","Hirondelle bicolore","Hirondelle de Bonaparte","Hirondelle de Pallas","Hirondelle de fen\xEAtre","Hirondelle de rivage","Hirondelle de rochers","Hirondelle du d\xE9sert","Hirondelle isabelline","Hirondelle noire","Hirondelle paludicole","Hirondelle rousseline","Hirondelle rustique","Hirondelle \xE0 ailes h\xE9riss\xE9es","Hirondelle \xE0 front blanc","Huppe d'Afrique","Huppe fasci\xE9e","Hu\xEEtrier d'Am\xE9rique","Hu\xEEtrier pie","Hypola\xEFs bott\xE9e","Hypola\xEFs d'Upcher","Hypola\xEFs des oliviers","Hypola\xEFs ict\xE9rine","Hypola\xEFs obscure","Hypola\xEFs polyglotte","Hypola\xEFs p\xE2le","Hypola\xEFs rama","H\xE9ron cendr\xE9","H\xE9ron garde-boeufs","H\xE9ron interm\xE9diaire","H\xE9ron m\xE9lanoc\xE9phale","H\xE9ron pourpr\xE9","H\xE9ron stri\xE9","H\xE9ron vert","Ibis chauve","Ibis falcinelle","Ibis sacr\xE9","Ict\xE9rie polyglotte","Ins\xE9parable de Fischer","Iranie \xE0 gorge blanche","Jaseur bor\xE9al","Jaseur d'Am\xE9rique","Junco ardois\xE9","Labbe de McCormick","Labbe parasite","Labbe pomarin","Labbe \xE0 longue queue","Lagop\xE8de alpin","Lagop\xE8de des saules","Linotte m\xE9lodieuse","Linotte \xE0 bec jaune","Locustelle de Pallas","Locustelle fasci\xE9e","Locustelle fluviatile","Locustelle lanc\xE9ol\xE9e","Locustelle luscinio\xEFde","Locustelle tachet\xE9e","Loriot d'Europe","Lusciniole \xE0 moustaches","L\xE9iothrix jaune","Macareux cornu","Macareux hupp\xE9","Macareux moine","Macreuse brune","Macreuse de Sib\xE9rie","Macreuse noire","Macreuse \xE0 ailes blanches","Macreuse \xE0 bec jaune","Macreuse \xE0 front blanc","Marabout d'Afrique","Marmaronette marbr\xE9e","Marouette de Caroline","Marouette ponctu\xE9e","Marouette poussin","Marouette ray\xE9e","Marouette \xE0 bec jaune","Martin hupp\xE9","Martin triste","Martin-chasseur de Smyrne","Martin-p\xEAcheur d'Am\xE9rique","Martin-p\xEAcheur d'Europe","Martin-p\xEAcheur pie","Martinet cafre","Martinet de Sib\xE9rie","Martinet des maisons","Martinet noir","Martinet p\xE2le","Martinet ramoneur","Martinet unicolore","Martinet \xE0 ventre blanc","Martinet \xE9pineux","Maub\xE8che des champs","Mergule nain","Merle d'Am\xE9rique","Merle noir","Merle obscur","Merle p\xE2le","Merle unicolore","Merle \xE0 plastron","Milan noir","Milan royal","Milan \xE0 queue fourchue","Moineau blanc","Moineau cisalpin","Moineau de la mer Morte","Moineau domestique","Moineau espagnol","Moineau friquet","Moineau p\xE2le","Moineau soulcie","Monticole bleu","Monticole de roche","Moqueur chat","Moqueur polyglotte","Moqueur roux","Moucherolle des aulnes","Moucherolle des saules","Moucherolle ph\xE9bi","Moucherolle tch\xE9bec","Moucherolle vert","Moucherolle \xE0 c\xF4t\xE9s olive","Moucherolle \xE0 ventre jaune","Mouette atricille","Mouette blanche","Mouette de Bonaparte","Mouette de Franklin","Mouette de Ross","Mouette de Sabine","Mouette m\xE9lanoc\xE9phale","Mouette pygm\xE9e","Mouette relique","Mouette rieuse","Mouette ros\xE9e","Mouette tridactyle","Mouette \xE0 t\xEAte grise","M\xE9sange azur\xE9e","M\xE9sange bleue","M\xE9sange bor\xE9ale","M\xE9sange charbonni\xE8re","M\xE9sange hupp\xE9e","M\xE9sange lapone","M\xE9sange lugubre","M\xE9sange noire","M\xE9sange nonnette","M\xE9sange nord-africaine","M\xE9sange \xE0 longue queue","M\xE9sangeai imitateur","Naucler \xE0 queue fourchue","Nette rousse","Ninoxe hirsute","Niverolle alpine","Noddi brun","Nyctale de Tengmalm","Nymph\xE9e fuligineuse","Oc\xE9anite cul-blanc","Oc\xE9anite culblanc","Oc\xE9anite de Castro","Oc\xE9anite de Monteiro","Oc\xE9anite de Swinhoe","Oc\xE9anite de Wilson","Oc\xE9anite fr\xE9gate","Oc\xE9anite temp\xEAte","Oc\xE9anite \xE0 ventre noir","Oedicn\xE8me criard","Oie cendr\xE9e","Oie de Ross","Oie de la toundra","Oie des moissons","Oie des neiges","Oie empereur","Oie naine","Oie rieuse","Oie \xE0 bec court","Oie \xE0 t\xEAte barr\xE9e","Oriole de Baltimore","Oriole du Nord","Orite \xE0 longue queue","Ouette d'\xC9gypte","Outarde barbue","Outarde canepeti\xE8re","Outarde de Macqueen","Outarde houbara","Panure \xE0 moustaches","Paon bleu","Paradoxornis de Webb","Paradoxornis \xE0 gorge cendr\xE9e","Paruline azur\xE9e","Paruline bleue","Paruline couronn\xE9e","Paruline des mangroves","Paruline des pins","Paruline des pr\xE9s","Paruline des ruisseaux","Paruline flamboyante","Paruline jaune","Paruline masqu\xE9e","Paruline noir et blanc","Paruline obscure","Paruline polyglotte","Paruline ray\xE9e","Paruline tigr\xE9e","Paruline triste","Paruline verd\xE2tre","Paruline \xE0 ailes bleues","Paruline \xE0 ailes dor\xE9es","Paruline \xE0 capuchon","Paruline \xE0 collier","Paruline \xE0 couronne rousse","Paruline \xE0 croupion jaune","Paruline \xE0 flancs marron","Paruline \xE0 gorge grise","Paruline \xE0 gorge jaune","Paruline \xE0 gorge noire","Paruline \xE0 gorge orang\xE9e","Paruline \xE0 joues grises","Paruline \xE0 poitrine baie","Paruline \xE0 t\xEAte cendr\xE9e","Passerin azur\xE9","Passerin indigo","Percnopt\xE8re d'\xC9gypte","Perdrix bartavelle","Perdrix choukar","Perdrix de Daourie","Perdrix gambra","Perdrix grise","Perdrix rouge","Perriche veuve","Perruche alexandre","Perruche \xE0 collier","Petit Blongios","Petit Chevalier","Petit Fuligule","Petit Garrot","Petit Pingouin","Petit Puffin du Cap-Vert","Petit-duc scops","Petite Sterne","Phalarope de Wilson","Phalarope \xE0 bec large","Phalarope \xE0 bec \xE9troit","Pha\xE9ton \xE0 bec jaune","Pha\xE9ton \xE0 bec rouge","Phragmite aquatique","Phragmite des joncs","Pic cendr\xE9","Pic de Levaillant","Pic de Sharpe","Pic flamboyant","Pic macul\xE9","Pic mar","Pic noir","Pic syriaque","Pic tridactyle","Pic vert","Pic \xE0 dos blanc","Pic \xE9peiche","Pie bavarde","Pie du Maghreb","Pie ib\xE9rique","Pie-bleue ib\xE9rique","Pie-gri\xE8che bor\xE9ale","Pie-gri\xE8che brune","Pie-gri\xE8che du Turkestan","Pie-gri\xE8che grise","Pie-gri\xE8che isabelle","Pie-gri\xE8che masqu\xE9e","Pie-gri\xE8che m\xE9ridionale","Pie-gri\xE8che schach","Pie-gri\xE8che \xE0 poitrine rose","Pie-gri\xE8che \xE0 t\xEAte rousse","Pie-gri\xE8che \xE9corcheur","Pigeon biset","Pigeon colombin","Pigeon de Bolle","Pigeon des lauriers","Pigeon trocaz","Pingouin torda","Pinson bleu","Pinson bleu de Grande Canarie","Pinson de Grande Canarie","Pinson des arbres","Pinson du Nord","Pintade de Numidie","Pioui de l'Est","Pipit d'Am\xE9rique","Pipit de Berthelot","Pipit de Godlewski","Pipit de Richard","Pipit de la Petchora","Pipit des arbres","Pipit farlouse","Pipit maritime","Pipit rousseline","Pipit rousset","Pipit spioncelle","Pipit \xE0 dos olive","Pipit \xE0 long bec","Piranga vermillon","Piranga \xE9carlate","Plectrophane des neiges","Plectrophane lapon","Plongeon arctique","Plongeon catmarin","Plongeon huard","Plongeon imbrin","Plongeon \xE0 bec blanc","Pluvian fluviatile","Pluvier argent\xE9","Pluvier asiatique","Pluvier bronz\xE9","Pluvier de Leschenault","Pluvier de Mongolie","Pluvier dor\xE9","Pluvier fauve","Pluvier grand-gravelot","Pluvier guignard","Pluvier kildir","Pluvier neigeux","Pluvier oriental","Pluvier petit-gravelot","Pluvier p\xE2tre","Pluvier \xE0 collier interrompu","Pouillot bor\xE9al","Pouillot brun","Pouillot de Bonelli","Pouillot de Hume","Pouillot de Pallas","Pouillot de Schwarz","Pouillot de Temminck","Pouillot des Canaries","Pouillot du Caucase","Pouillot du Kamtchatka","Pouillot fitis","Pouillot gris\xE9ole","Pouillot ib\xE9rique","Pouillot modeste","Pouillot montagnard","Pouillot oriental","Pouillot siffleur","Pouillot verd\xE2tre","Pouillot v\xE9loce","Pouillot \xE0 deux barres","Pouillot \xE0 grands sourcils","Pouillot \xE0 gros bec","Pouillot \xE0 pattes claires","Puffin bor\xE9al","Puffin cendr\xE9","Puffin d'Audubon","Puffin de Baillon","Puffin de Boyd","Puffin de Macaron\xE9sie","Puffin de Scopoli","Puffin des Anglais","Puffin des Bal\xE9ares","Puffin du Cap-Vert","Puffin fuligineux","Puffin majeur","Puffin yelkouan","Puffin \xE0 bec gr\xEAle","Puffin \xE0 menton blanc","Puffin \xE0 pieds p\xE2les","Pygargue de Pallas","Pygargue \xE0 queue blanche","Pygargue \xE0 t\xEAte blanche","P\xE9lican blanc","P\xE9lican fris\xE9","P\xE9lican gris","P\xE9trel de Bulwer","P\xE9trel de Mad\xE8re","P\xE9trel de Schlegel","P\xE9trel de Trindade","P\xE9trel de la Trinit\xE9","P\xE9trel des Bermudes","P\xE9trel des Desertas","P\xE9trel des Kermadec","P\xE9trel diablotin","P\xE9trel du Herald","P\xE9trel du d\xE9sert","P\xE9trel gongon","P\xE9trel g\xE9ant","P\xE9trel h\xE9rault","P\xE9trel noir","P\xE9trel soyeux","Quiscale bronz\xE9","Rhynch\xE9e peinte","Robin \xE0 flancs roux","Roitelet de Mad\xE8re","Roitelet hupp\xE9","Roitelet triple-bandeau","Roitelet \xE0 couronne rubis","Rollier d'Abyssinie","Rollier d'Europe","Roselin cramoisi","Roselin de Lichtenstein","Roselin de Mongolie","Roselin familier","Roselin githagine","Roselin rose","Roselin tachet\xE9","Roselin \xE0 ailes roses","Roselin \xE0 t\xEAte grise","Rossignol bleu","Rossignol calliope","Rossignol philom\xE8le","Rossignol progn\xE9","Rossignol siffleur","Rougequeue d'Eversmann","Rougequeue de Moussier","Rougequeue fuligineux","Rougequeue noir","Rougequeue \xE0 front blanc","Rousserolle africaine","Rousserolle effarvatte","Rousserolle isabelle","Rousserolle stentor","Rousserolle turdo\xEFde","Rousserolle verderolle","Rousserolle \xE0 gros bec","R\xE2le de Virginie","R\xE2le des gen\xEAts","R\xE2le des pr\xE9s","R\xE2le ray\xE9","R\xE2le \xE0 bec jaune","R\xE2le \xE0 cr\xEAte","R\xE9miz penduline","Sarcelle cannelle","Sarcelle d'hiver","Sarcelle d'\xE9t\xE9","Sarcelle marbr\xE9e","Serin des Canaries","Serin du Cap","Serin \xE0 front d'or","Serin \xE0 front rouge","Sirli de Dupont","Sirli du d\xE9sert","Sittelle corse","Sittelle de Kr\xFCper","Sittelle de Neumayer","Sittelle torchepot","Sizerin blanch\xE2tre","Sizerin flamm\xE9","Spatule blanche","Spatule d'Afrique","Starique cristatelle","Starique perroquet","Sterne arctique","Sterne brid\xE9e","Sterne caspienne","Sterne caugek","Sterne de Cabot","Sterne de Dougall","Sterne de Forster","Sterne de Saunders","Sterne des Al\xE9outiennes","Sterne fuligineuse","Sterne hansel","Sterne naine","Sterne pierregarin","Sterne royale","Sterne royale (Afrique)","Sterne voyageuse","Sterne \xE0 dos p\xE2le","Sterne \xE0 joues blanches","Sterne \xE9l\xE9gante","Syrrhapte paradoxal","Tadorne casarca","Tadorne de Belon","Tal\xE8ve d'Afrique","Tal\xE8ve d'Allen","Tal\xE8ve sultane","Tal\xE8ve violac\xE9e","Tal\xE8ve \xE0 t\xEAte grise","Tangara vermillon","Tangara \xE9carlate","Tantale ibis","Tarier africain","Tarier d'Afrique","Tarier de Sib\xE9rie","Tarier de Stejneger","Tarier des Canaries","Tarier des pr\xE9s","Tarier pie","Tarier p\xE2tre","Tarin des aulnes","Tchagra \xE0 t\xEAte noire","Tichodrome \xE9chelette","Tisserin gendarme","Tisserin \xE0 t\xEAte noire","Tohi \xE0 flancs roux","Torcol fourmilier","Torrentaire \xE0 calotte blanche","Tournepierre \xE0 collier","Tourtelette masqu\xE9e","Tourterelle des bois","Tourterelle maill\xE9e","Tourterelle masqu\xE9e","Tourterelle orientale","Tourterelle rieuse","Tourterelle triste","Tourterelle turque","Tourterelle \xE0 queue carr\xE9e","Traquet de Chypre","Traquet de Finsch","Traquet de Seebohm","Traquet deuil","Traquet du d\xE9sert","Traquet isabelle","Traquet kurde","Traquet motteux","Traquet noir et blanc","Traquet rieur","Traquet \xE0 capuchon","Traquet \xE0 t\xEAte blanche","Traquet \xE0 t\xEAte grise","Troglodyte des marais","Troglodyte mignon","Turnix d'Andalousie","Turnix mugissant","Tyran de l'Ouest","Tyran des savanes","Tyran tritri","T\xE9tras lyre","Vacher \xE0 t\xEAte brune","Vanneau hupp\xE9","Vanneau indien","Vanneau sociable","Vanneau \xE0 queue blanche","Vanneau \xE0 t\xEAte grise","Vanneau \xE0 \xE9perons","Vanneau \xE9peronn\xE9","Vautour africain","Vautour de R\xFCppell","Vautour moine","Vautour oricou","Vautour percnopt\xE8re","Venturon montagnard","Verdier d'Europe","Vir\xE9o aux yeux blancs","Vir\xE9o aux yeux rouges","Vir\xE9o de Philadelphie","Vir\xE9o m\xE9lodieux","Vir\xE9o \xE0 gorge jaune","Vir\xE9o \xE0 oeil rouge","Vir\xE9o \xE0 t\xEAte bleue","\xC9chasse blanche","\xC9lanion blanc","\xC9pervier d'Europe","\xC9pervier \xE0 pieds courts","\xC9pervi\xE8re bor\xE9ale","\xC9rismature rousse","\xC9rismature \xE0 t\xEAte blanche","\xC9tourneau de Daourie","\xC9tourneau roselin","\xC9tourneau sansonnet","\xC9tourneau unicolore"];var a=["Abyssin","American curl","American shorthair","American wirehair","Anatoli","Angora turc","Asian","Balinais","Bengal","Bleu russe","Bobtail am\xE9ricain","Bobtail des Kouriles","Bobtail japonais","Bombay","Brazilian shorthair","British longhair","British shorthair","Burmese","Burmilla","Californian rex","Californian spangled","Ceylan","Chantilly","Chartreux","Chausie","Colorpoint shorthair","Cornish rex","Cymric","Devon rex","Donskoy","European shorthair","Exotic shorthair","German rex","Havana brown","Highland fold","Himalayen","Khao Manee","Korat","LaPerm","Maine coon","Mandarin","Manx","Mau arabe","Mau \xE9gyptien","Minskin","Munchkin","Nebelung","Norv\xE9gien","Ocicat","Ojos azules","Oriental shorthair","Persan","Peterbald","Pixie-bob","Ragamuffin","Ragdoll","Sacr\xE9 de Birmanie","Safari","Savannah","Scottish fold","Selkirk rex","Serengeti","Seychellois","Siamois","Sib\xE9rien","Singapura","Skookum","Snowshoe","Sokok\xE9","Somali","Sphynx","Tha\xEF","Tiffany","Tonkinois","Toyger","Turc de Van","Ural rex","York chocolat"];var i=["Baiji","Baleine australe","Baleine bleue","Baleine bor\xE9ale","Baleine de Cuvier","Baleine de Minke","Baleine du Groenland","Baleine franche australe","Baleine franche de Biscaye","Baleine franche de l'Atlantique Nord","Baleine franche du Pacifique Nord","Baleine grise","Baleine noire australe","Baleine noire de l'Atlantique Nord","Baleine pilote","Baleine pygm\xE9e","Baleine \xE0 bec","Baleine \xE0 bec d'Andrews","Baleine \xE0 bec d'Hector","Baleine \xE0 bec de Blainville","Baleine \xE0 bec de Gervais","Baleine \xE0 bec de Gray","Baleine \xE0 bec de Hubbs","Baleine \xE0 bec de Layard","Baleine \xE0 bec de Longman","Baleine \xE0 bec de Perrin","Baleine \xE0 bec de Sowerby","Baleine \xE0 bec de Stejneger","Baleine \xE0 bec de Travers","Baleine \xE0 bec de True","Baleine \xE0 bec pygm\xE9e","Baleine \xE0 bosse","Boto","B\xE9luga","B\xE9rardie d'Arnoux","B\xE9rardie de Baird","Cachalot nain","Cachalot pygm\xE9e","Cachalots","Costero","Dauphin Burrunan","Dauphin apt\xE8re austral","Dauphin blanc de Chine","Dauphin bleu et blanc","Dauphin clym\xE8ne","Dauphin commun d'Arabie","Dauphin commun \xE0 bec court","Dauphin commun \xE0 long bec","Dauphin d'Hector","Dauphin d'\xC9lectre","Dauphin de Chine","Dauphin de Commerson","Dauphin de Fraser","Dauphin de Gill","Dauphin de Guyane","Dauphin de Peale","Dauphin de Risso","Dauphin de l'Amazone","Dauphin de l'Indus","Dauphin de l'Irrawaddy","Dauphin de la Plata","Dauphin du Cap","Dauphin du Gange","Dauphin du Nord","Dauphin noir","Dauphin obscur","Dauphin rose de l'Amazone","Dauphin sablier","Dauphin tachet\xE9 de l'Atlantique","Dauphin tachet\xE9 pantropical","Dauphin \xE0 aileron retrouss\xE9 d'Australie","Dauphin \xE0 bec \xE9troit","Dauphin \xE0 bosse de l'Atlantique","Dauphin \xE0 bosse de l'Indo-Pacifique","Dauphin \xE0 bosse de l'oc\xE9an Indien","Dauphin \xE0 bosse du Pacifique","Dauphin \xE0 flancs blancs","Dauphin \xE0 long bec","Dauphin \xE0 nez blanc","Dauphins d'eau douce","Fausse orque","Globic\xE9phale","Globic\xE9phale noir","Globic\xE9phale tropical","Grand cachalot","Grand dauphin","Grand dauphin de l'oc\xE9an Indien","Hyperoodon austral","Hyperoodon bor\xE9al","Jubarte","Marsouin apt\xE8re","Marsouin blanc","Marsouin commun","Marsouin de Burmeister","Marsouin de Cuvier","Marsouin de Lahille","Marsouin de l'Inde","Marsouin du Golfe de Californie","Marsouin du Pacifique","Marsouin \xE0 lunettes","Marsouins","M\xE9gapt\xE8re","M\xE9soplodon japonais","Narval","Orque","Orque pygm\xE9e","Petit rorqual","Petit rorqual de l\u2019Antarctique","Rorqual bleu","Rorqual bor\xE9al","Rorqual commun","Rorqual d'Omura","Rorqual de Bryde","Rorqual de Rudolphi","Rorqual tropical","Rorqual \xE0 bosse","Rorquals","Sotalie","Sousouc","Tasmac\xE8te de Shepherd","Tucuxi","Whalphin"];var n=["Abondance","Armoricaine","Aubrac","Aure-et-Saint-Girons","Bazadaise","Bleue du Nord","Blonde d'Aquitaine","Bordelaise (Nouvelle)","Braunvieh","Brava","Bretonne pie noir","Brune","B\xE9arnaise","B\xE9tizu","Camargue","Charolaise","Corse","Coursi\xE8re","Cr\xE9ole","Ferrandaise","Froment du L\xE9on","Gasconne des Pyr\xE9n\xE9es","H\xE9rens","INRA 95","Limousine","Lourdaise","Mara\xEEchine","Marine landaise","Mirandaise","Montb\xE9liarde","Montb\xE9liardes au pr\xE9.","Nantaise","Normande","Parthenaise","Pie rouge des plaines","Prim'Holstein","Rouge des pr\xE9s","Rouge flamande","Salers","Saosnoise","Sarlabot","Sa\xEFnata","Simmental","Simmental fran\xE7aise","Tarine","Villard-de-Lans","Vosgienne","\xC9vol\xE8ne"];var o=["Alligator d'Am\xE9rique","Alligator de Chine","Ca\xEFman de Schneider","Ca\xEFman nain","Ca\xEFman noir","Ca\xEFman \xE0 lunettes","Ca\xEFman \xE0 museau large","Crocodile am\xE9ricain","Crocodile d'Afrique de l'Ouest","Crocodile de Cuba","Crocodile de Johnston","Crocodile de Morelet","Crocodile de Nouvelle-Guin\xE9e","Crocodile de l'Or\xE9noque","Crocodile des Philippines","Crocodile des marais","Crocodile du Nil","Crocodile du Siam","Crocodile marin","Crocodile nain","Faux-gavial d'Afrique","Faux-gavial de Malaisie","Gavial du Gange","Jacara"];var l=["Affenpinscher","Airedale Terrier","Akita Am\xE9ricain","Akita Inu","American Staffordshire Terrier","Ancien chien d'arr\xEAt danois","Anglo-Fran\xE7ais de Petite V\xE8nerie","Ari\xE9geois","Barbet","Barbu Tch\xE8que","Barzo\xEF","Basenji","Basset Art\xE9sien-Normand","Basset Bleu de Gascogne","Basset Fauve de Bretagne","Basset Hound","Basset de Westphalie","Basset des Alpes","Beagle","Beagle-Harrier","Bearded Collie","Beauceron","Bedlington Terrier","Berger Allemand","Berger Am\xE9ricain Miniature","Berger Australien","Berger Belge Groenendael","Berger Belge Laekenois","Berger Belge Malinois","Berger Belge Tervueren","Berger Blanc Suisse","Berger Catalan","Berger Hollandais","Berger Islandais","Berger Polonais de Plaine","Berger Polonais de Podhale","Berger Portugais","Berger Yougoslave","Berger d'Anatolie","Berger d'Asie Centrale","Berger de Bergame","Berger de Boh\xEAme","Berger de Brie","Berger de Maremme et des Abruzzes","Berger de Picardie","Berger de Russie","Berger de Savoie","Berger de l'Atlas","Berger des Pyr\xE9n\xE9es","Berger des Shetland","Berger du Caucase","Berger du massif du Karst","Berger finnois de Laponie","Bichon Bolonais","Bichon Havanais","Bichon Maltais","Bichon \xE0 poil fris\xE9","Biewer Yorkshire","Billy","Black and Tan Coonhound","Bobtail","Boerbull","Border Collie","Border Terrier","Boston Terrier","Bouledogue Am\xE9ricain","Bouledogue Fran\xE7ais","Bouvier Australien","Bouvier Bernois","Bouvier d'Appenzell","Bouvier de l'Entlebuch","Bouvier des Ardennes","Bouvier des Flandres","Boxer","Brachet Allemand","Brachet Polonais","Brachet Tyrolien","Brachet autrichien noir et feu","Brachet de Styrie \xE0 poil dur","Braque Fran\xE7ais","Braque Hongrois \xE0 poil court","Braque Italien","Braque Saint-Germain","Braque Slovaque \xE0 poil dur","Braque allemand \xE0 poil court","Braque allemand \xE0 poil dur","Braque d'Auvergne","Braque de Burgos","Braque de Weimar","Braque de l'Ari\xE8ge","Braque du Bourbonnais","Braque hongrois \xE0 poil dur","Briquet Griffon Vend\xE9en","Broholmer","Buhund Norv\xE9gien","Bull Terrier","Bulldog Anglais","Bulldog Continental","Bullmastiff","Cairn Terrier","Cane Corso","Caniche","Cao de Castro Laboreiro","Carlin","Cavalier King Charles Spaniel","Cavapoo","Chesapeake Bay Retriever","Chien Finnois de Laponie","Chien Jindo Cor\xE9en","Chien Loup Tch\xE9coslovaque","Chien Loup de Saarloos","Chien chinois \xE0 cr\xEAte","Chien courant Finnois","Chien courant d'Istrie \xE0 poil dur","Chien courant d'Istrie \xE0 poil ras","Chien courant de Bosnie","Chien courant de Halden","Chien courant de Hamilton","Chien courant de Hygen","Chien courant de Posavatz","Chien courant de Schiller","Chien courant de Transylvanie","Chien courant du Sm\xE5land","Chien courant espagnol","Chien courant grec","Chien courant italien","Chien courant norv\xE9gien","Chien courant serbe","Chien courant slovaque","Chien courant suisse","Chien courant yougoslave de montagne","Chien courant yougoslave tricolore","Chien d'Eau Portugais","Chien d'Oysel","Chien d'arr\xEAt frison","Chien d'arr\xEAt portugais","Chien d'eau am\xE9ricain","Chien d'eau espagnol","Chien d'eau frison","Chien d'eau irlandais","Chien d'eau romagnol","Chien d'ours de Car\xE9lie","Chien de Berger Roumain de Mioritza","Chien de Berger Roumain des Carpathes","Chien de Canaan","Chien de Saint Hubert","Chien de berger de Croatie","Chien de berger de Majorque","Chien de montagne des Pyr\xE9n\xE9es","Chien de montagne portugais","Chien du Groenland","Chien du pharaon","Chien d\u2019Artois","Chien d\u2019arr\xEAt allemand \xE0 poil long","Chien d\u2019\xC9lan Norv\xE9gien Noir","Chien d\u2019\xE9lan norv\xE9gien gris","Chien d\u2019\xE9lan su\xE9dois","Chien norv\xE9gien de Macareux","Chien nu du P\xE9rou","Chien nu mexicain","Chien rouge de Bavi\xE8re","Chien rouge de Hanovre","Chien su\xE9dois de Laponie","Chien tha\xEFlandais","Chihuahua","Chow Chow","Cirneco de l\u2019Etna","Clumber-Spaniel","Cocker Am\xE9ricain","Cocker Anglais","Colley \xE0 poil court","Colley \xE0 poil long","Coton de Tul\xE9ar","Curly Coated Retriever","Cursinu","Dalmatien","Dandie-Dinmont-Terrier","Dobermann","Dogo Canario","Dogue Allemand","Dogue Argentin","Dogue de Bordeaux","Dogue de Majorque","Dogue du Tibet","Drever","English Toy Terrier","Epagneul Bleu de Picardie","Epagneul Breton","Epagneul Fran\xE7ais","Epagneul Japonais","Epagneul Nain Continental","Epagneul Picard","Epagneul Tib\xE9tain","Epagneul de Pont-Audemer","Epagneul \xE0 perdrix de Drente","Eurasier","Field-Spaniel","Fila Brasileiro","Fila de Sao Miguel","Flat-Coated Retriever","Fox Terrier","Foxhound am\xE9ricain","Foxhound anglais","Golden Retriever","Goldendoodle","Grand Anglo-Fran\xE7ais blanc et noir","Grand Anglo-Fran\xE7ais blanc et orange","Grand Anglo-Fran\xE7ais tricolore","Grand Basset Griffon vend\xE9en","Grand Bouvier Suisse","Grand Gascon saintongeois","Grand Griffon Vend\xE9en","Grand \xC9pagneul de M\xFCnster","Greyhound","Griffon Belge","Griffon Bleu de Gascogne","Griffon Bruxellois","Griffon Korthals","Griffon Nivernais","Griffon fauve de Bretagne","Harrier","Hokka\xEFdo Ken","Hovawart","Husky Sib\xE9rien","Irish Glen of Imaal Terrier","Irish Terrier","Irish Terrier \xE0 poil doux","Jack Russell Terrier","Jagdterrier","Kai","Kelpie","Kerry Blue Terrier","King Charles Spaniel","Kishu","Komondor","Kromfohrl\xE4nder","Kuvasz Hongrois","Labradoodle","Labrador Retriever","Lakeland Terrier","Landseer","La\xEFka Russo-Europ\xE9en","La\xEFka de Sib\xE9rie occidentale","La\xEFka de Sib\xE9rie orientale","Leonberger","Lhassa Apso","L\xE9vrier Afghan","L\xE9vrier Azawakh","L\xE9vrier Espagnol","L\xE9vrier Hongrois","L\xE9vrier Irlandais","L\xE9vrier Polonais","L\xE9vrier \xC9cossais","Malamute de l'Alaska","Mastiff","Mudi","M\xE2tin Espagnol","M\xE2tin de Naples","M\xE2tin de l'Alentejo","M\xE2tin des Pyr\xE9n\xE9es","Norfolk Terrier","Norwich Terrier","Otterhound","Parson Russell Terrier","Petit Basset Griffon vend\xE9en","Petit Braban\xE7on","Petit Chien Courant Suisse","Petit Chien Lion","Petit Epagneul de M\xFCnster","Petit L\xE9vrier Italien","Petit chien hollandais de chasse au gibier d'eau","Pinscher","Pinscher Nain","Pinscher autrichien \xE0 poil court","Pitbull","Podenco Canario","Podenco Ibicenco","Podengo portugais","Pointer Anglais","Poitevin","Pomsky","Porcelaine","Pudelpointer","Puli","Pumi","P\xE9kinois","Ratonero Bodeguero Andaluz","Retriever de la Nouvelle-\xC9cosse","Rhodesian-Ridgeback","Rottweiler","Saint-Bernard","Saluki","Samoy\xE8de","Schapendoes","Schipperke","Schnauzer","Sealyham Terrier","Setter Anglais","Setter Gordon","Setter Irlandais Rouge","Shar-Pei","Shiba Inu","Shih Tzu","Shikoku","Silky Terrier","Skye Terrier","Sloughi","Smous des Pays-Bas","Spinone","Spitz Allemand","Spitz Finlandais","Spitz Japonais","Spitz de Norrbotten","Spitz des Wisigoths","Springer Anglais","Staffordshire Bull Terrier","Sussex-Spaniel","Tchouvatch Slovaque","Teckel","Terre-Neuve","Terrier Ecossais","Terrier Noir Russe","Terrier Tib\xE9tain","Terrier australien","Terrier br\xE9silien","Terrier de Manchester","Terrier japonais","Terrier tch\xE8que","Tosa","Volpino Italien","Welsh Corgi Cardigan","Welsh Corgi Pembroke","Welsh Springer Spaniel","Welsh Terrier","West Highland White Terrier","Whippet","Yorkshire Terrier"];var t=["Able de Heckel","Ablette","Achigan de mer","Aiglefin","Aiguillat commun","Aiguillat noir","Alose","Amour blanc","Anchois","Anguille","Apogon","Apron du Rh\xF4ne","Aspe","Baliste","Bar","Bar blanc","Barbeau","Bardot","Barracuda","Baudroie","Baudroie abyssale de Johnson","Baudroie commune","Baudroie des abysses","Baudroie d\u2019Am\xE9rique","Beaux yeux","Billard","Black-Bass","Blade","Blageon","Blanchet","Blennie","Bogue","Bonite","Bouvi\xE8re","Brochet","Brosme","Br\xE8me","Cabillaud","Capelan","Capret","Carassin","Carassin dor\xE9","Cardine franche","Carpe","Carrelet","Castagnole","Cernier","Chabot","Chapon","Chat","Chevesne","Claresse","Colin","Congre","Corb","Coryph\xE8ne","Cor\xE9gone","Courbine","Cr\xE9nilabre","Cyprinodonte","Daubenet","Denti","Dorade","Dormelle","Dor\xE9 jaune","Dragonnet","Elbot","Escolier","Espadon","Esturgeon","Fanfre","Fl\xE9tan","Gallinette","Gardon","Girelle","Gobie","Gobio","Goret","Gorette","Goujon","Grand-gueule","Grande vive","Grenadier","Grenadier de roche","Grondin","Gr\xE9mille","Guppy","Hareng","Hippocampe","Hotu","Huchon","Iba\xEFa","Ide m\xE9lanote","Julienne","Labre","Lamproie","Lan\xE7on","Liche","Lieu appel\xE9","Lieu jaune","Lieu noir","Limande","Lingue","Loche","Lompe","Loquette d'Europe","Lorette","Lotte","Loubine","Loup de mer","Maigre","Makaire","Mako","Malachigan","Mandoule","Maquereau","Mara\xEEche","Marbr\xE9","Marigane noire","Marlin","Maskinong\xE9","Merlan","Merlu","Merval","Meunier","Mirandelle","Mora","Morue","Motelle","Muge","Mulet","Mur\xE8ne","M\xE2choiron","M\xE9nomini rond","M\xE9rou","M\xF4le","Napol\xE9on","Oblade","Omble chevalier","Omble de fontaine1","Ombre","Opah","Ouananiche","Pageot","Pagre","Panga","Pataclet","Perche","Perche du Nil","Petite vive","Phrynorhombe","Piranha","Plie","Poisson clown","Poisson lanterne","Poisson rouge","Poisson z\xE8bre","Poisson-chat","Poisson-chien","Poisson-coffre","Poisson-lune","Poisson-pilote","Raie","Rascasse","Rason","Requin","Requin blanc","Requin gris","Requin marteau","Requin p\xE8lerin","Requin \xE0 pointes noires","Requin-baleine","Requin-nourrice","Requin-taureau","Requin-tigre","Rondin","Rotengle","Roucaou","Rouget","Roussette","Rouvet","R\xE9mora commun","Saint-pierre","Sandre","Sar","Sardine","Sarran","Saumon","Saupe","Sigan Corail","Silure","Sole","Sprat","S\xE9baste","S\xE9riole","S\xE9verau","Tacaud","Tanche","Tanche-tautogue","Tanude","Targeur","Tassergal","Tautogue noir","Thazard","Thon","Thon albacore","Thon blanc","Thon listao","Thon rouge","Tilapia du Nil","Truite","Truite arc-en-ciel","Truite de mer","Truite fario","Turbot","Turbot de Terre-Neuve","Turbot de sable","T\xE9traodon","Uranoscope","Vairon","Vandoise","Vieille","Vivaneau","Vive","Vive araign\xE9e","Vive ray\xE9e","mehdia","\xC9glefin","\xC9perlan","\xC9pinoche","\xC9pinochette","\xC9quille"];var u=["Abaga","Abyssinien","Achetta","Adaev","Aegidienberger","Akhal-Teke","Alaca","Albanais","Alt-W\xFCrttemberger","Alta\xEF","American Walking Pony","American warmblood","Anatolien","Andalou","Andin","Andravida","Anglo-Argentino","Anglo-arabe","Anglo-kabarde","Appaloosa","Appaloosa britannique","Appaloosa n\xE9erlandais","Appenninico","AraAppaloosa","Arabe","Arabe Shagya","Arabe syrien","Arabe-Barbe","Arabe-lusitanien","Arabo-Haflinger","Arabo-boulonnais","Arabo-frison","Aralusian","Aravani","Ardahan","Ardennais","Ardennais palatin","Ardennais russe","Ardennais su\xE9dois","Arenberg-Nordkirchener","Ar\xE9wa","Astrakhan","Asturc\xF3n","Autre que Pur-sang","Auxois","Axios","Azt\xE8que","A\xE9nos","Bachkir","Bagual","Baguio","Bagzan","Bahiano","Bahr el-Ghazal","Baicha","Baise","Baixadeiro","Baixo-Amazona","Bajau","Baladi","Bale","Bali","Balikun","Baloutche","Bandiagara","Bangladais","Barbe","Bardigiano","Barock Pinto","Barraquand","Batak","Bauernpferd","Ba\xEFkal","Berba","Bergmann","Bhirum","Bhotia","Bima","Bi\u0142goraj","Blazer","Bobo","Boer du Cap","Boer sud-africain","Borana","Bornu","Bosnien","Boudienny","Boulonnais","Bouriate","Breton","Britannique sang-chaud","Brumby","Bucovine","Bulgare oriental","Buohai","Burguete","B\xE9l\xE9dougou","Cabadin","Calabrais","Camargue","Camarillo white","Cambodgien","Campeiro","Campolina","Canadien","Canik","Capitanata","Carneddau","Carolina Marsh Tacky","Caspien","Castillan","Castillonnais","Chaidamu","Chakouyi","Chara","Cheju","Cherokee","Cheval corse","Cheval cr\xE8me","Cheval d'Auvergne","Cheval d'Azerba\xEFdjan","Cheval de Catria","Cheval de Heck","Cheval de Koro","Cheval de Meg\xE8ve","Cheval de Miquelon","Cheval de Nangchen","Cheval de Pentro","Cheval de Posavina","Cheval de Riwoch\xE9","Cheval de Senne","Cheval de ban'ei","Cheval de l'\xEEle de Cumberland","Cheval de l'\xEEle de sable","Cheval de la Sierra Tarahumara","Cheval de la mer noire","Cheval de selle luxembourgeois","Cheval de sport belge","Cheval de sport br\xE9silien","Cheval de sport bulgare","Cheval de sport canadien","Cheval de sport croate","Cheval de sport espagnol","Cheval de sport estonien","Cheval de sport frison","Cheval de sport hongrois","Cheval de sport polonais","Cheval de sport roumain","Cheval de sport suisse","Cheval des Marquises","Cheval des Nez-Perc\xE9s","Cheval des Outer Banks","Cheval des montagnes du Pays basque","Cheval dor\xE9 de la Boh\xEAme","Cheval du Delta","Cheval du Don","Cheval du Haut-Ienisse\xEF","Cheval du Namib","Cheval du Ventasso","Cheval du Vercors de Barraquand","Cheval du delta du Danube","Cheval du plateau persan","Cheval marocain de sport","Cheval miniature am\xE9ricain","Cheval miniature fran\xE7ais","Cheval mongol","Cheval portugais de sport","Chilien","Chilote","Chincoteague","Choctaw","Chumbivilcas","Chumysh","Cleveland Bay","Clydesdale","Cob Gypsy","Cob normand","Coffin Bay Brumby","Colorado ranger","Comtois","Connemara","Corajoso","Coste\xF1o","Criollo","Criollo militar","Criollo paraguayen","Criollo uruguayen","Criollo v\xE9n\xE9zu\xE9lien","Crioulo","Cr\xE9tois","Curly","Daghestan","Dales","Danois sang-chaud","Danubien","Darashouri","Darkhad","Dartmoor","Datong","Deccani","Deliboz","Demi-sang d'Inde","Djavakh\xE9ti","Dongola","Dosanko","Dun hongrois","D\xF8le","D\xFClmen","Edelbluthaflinger","Emben","Eriskay","Erlunchun","Estonien","Exmoor","Falabella","Feli\u0144ski","Fell","Finlandais","Finlandais sang-chaud","Fiorello","Fjord","Flamand","Fleuve","Flores","Florida Cracker Horse","Foutank\xE9","Franches-Montagnes","Frederiksborg","Frison","Frison oriental","Furioso-North Star","F\xE9ro\xE9","Galiceno","Galicien","Galshar","Ganja","Ganzi","Garrano","Garron","Garwolin","Gashghai","Gayo","Gelderland","Gemlik","Georgian Grande","Gharkawi","Giara","Giarab","Giawf","Gidran","Glasinacki","Gobir","Gocan","Goklan","Golden American Saddlebred","Gondo","Gotland","Graditz","Grand \u017Demaitukas","Groningen","Guangxi","Guanzhong","Guba","Guizhou","Guoxia","Habash","Hackney","Haflinger","Haflo-arabe","Halla","Hanovrien","Haomeng","Heihe","Heilongjiang","Henan","Henson","Hequ","Herati","Hessois","Highland","Hirzai","Hispano","Hispano-Bret\xF3n","Hispano-arabe","Hmong","Hodh","Hoho","Hokka\xEFdo washu","Holsteiner","Horro","Hunter irlandais","Hu\xE7ul","H\u0131n\u0131s","Iakoute","Iomud","Irish Cob","Irish Sport Horse","Islandais","Jabe","Jaca Navarra","Jaf","Jata","Java","Jianchang","Jilin","Jinhong","Jinjiang","Jofi","Jomud","Jumli","Jutland","KWPN","Kabardin","Kabia","Kabouli","Kafa","Kaimanawa","Kajlan","Kalmouk","Kandachime","Karabakh","Karaba\xEFr","Karachai","Karakatchan","Kathiawari","Kazakh","Kazakh chinois","Kentucky Mountain Saddle Horse","Kerqin","Kerry bog","Kiger Mustang","Kinsky","Kirdi","Kirdimi","Kirghiz","Kisber Felver","Kiso","Kladruber","Knabstrup","Kohband","Konik","Kordofani","Koto-koli","Kouznetsk","Kuda Padi","Kuda-Pacu","Kumyk","Kundudo","Kuningan","Kurde","Kushum","Kustanair","Kwangok","La Silla","Lakka","Landais","Lao","Lavradeiro","Lebaniega","Lehmkuhlener","Lesbos","Letea","Letton","Leutstettener","Lewitzer","Lezgian","Lichuan","Lidzbark","Liebenthaler","Lijiang","Lipizzan","Lippitt Morgan","Livno","Llanero","Logone","Loka\xEF","Lombok","Losino","Lovets","Lowicz","Lundy","Lusitanien","Lynghest","L\xE4mminverinen Ravuri","M'Bayar","M'Par","Macassar","Makra","Malakan","Mallorqu\xEDn","Malopolski","Manga","Mangalarga marchador","Mangalarga paulista","Mangolina","Marajoara","Marazi","Maremmano","Marisme\xF1a","Marwari","Mayray","Mbai","Mecklembourg","Megezh","Megruli","Merak-Saktenpata","Messara","Messeri","Mezen","Me\u0111imurje","Midilli","Minahasa","Mini Shetland","Minianka","Miniature Toy Horse","Miniature n\xE9erlandais","Miniature sud-africain","Minorquin","Minoussinsk","Misaki","Missouri Fox Trotter","Miyako","Moldave","Monchina","Mongol chinois","Montana Traveler","Morab","Morave","Morgan","Morna","Morocco spotted horse","Morochuco","Mossi","Mountain Pleasure","Moyle","Muniqi","Murakosi - Murakoz","Murgese","Murinsulaner","Mustang","Mustang de Cerbat","Mustang des monts Pryor","Mustang espagnol","Myangad","M\xE9rens","Napolitain","Narym","National Show Horse","New Forest","Ngua Noi","Nig\xE9rian","Ningqiang","Nokota","Nonius","Nooitgedacht","Nordestin","Nordlandshest","Noriker","Noriker sil\xE9sien","Nouvel Alta\xEF","Novoalexandrovsk","Novokirghize","Ob","Oldenbourg","Oldenbourg danois","Ondorshil","Orlov-Rostopchin","Padang","Pagan","Paint Horse","Pampa","Pampa marchador","Panje","Pantaneiro","Paso Fino","Paso cubain","Paso du Costa Rica","Paso fino colombien","Paso p\xE9ruvien","Patibarcino","Pechora","Percheron","Persano","Petiso argentino","Petit cheval de selle allemand","Pfalz-ardenner","Pindos","Pinkafeld","Pintabian","Pinto cubain","Pinzgauer","Piquira","Pleven","Poitevin mulassier","Polesskaya","Polo argentin","Poney Appaloosa europ\xE9en","Poney Hackney","Poney Noma","Poney australien","Poney basque","Poney classique allemand","Poney de Birmanie","Poney de Born\xE9o","Poney de Kagoshima","Poney de Manipur","Poney de Monterufoli","Poney de P\xE9n\xE9e","Poney de R\xE2jsh\xE2h\xEE","Poney de Somalie","Poney de Terre-Neuve","Poney de l'Esperia","Poney de polo","Poney de selle allemand","Poney de selle belge","Poney de selle britannique","Poney de selle finlandais","Poney de selle polonais","Poney de selle su\xE9dois","Poney de sport danois","Poney de sport nord-am\xE9ricain","Poney de sport slovaque","Poney de sport tch\xE8que","Poney des Am\xE9riques","Poney des A\xE7ores","Poney des Carpates","Poney des Mogods","Poney du Darfour","Poney du Sri Lanka","Poney fran\xE7ais de selle","Poney indien du lac La Croix","Poney roumain","Poney rustique canadien","Poney sardinien","Poney tachet\xE9 britannique","Poney tha\xEFlandais","Poney tib\xE9tain","Poney westphalien","Pottok","Priangan","Priob","Pur-sang","Pur-sang arabe","Pure race espagnole","Puruca","Pyongwon","Pyr\xE9n\xE9es catalanes","Qashqai","Qatgani","Qazal","Quarab","Quarter Horse","Quarter pony","Racking horse","Rahvan","Retuerta","Reziegi Taaishi","Rhodos","Rh\xE9nan sang-chaud","Rocky Mountain Horse","Rodope","Romano della Maremma Laziale","Rottaler","Sabyol","Saddlebred","Saklawi","Salernitano","Samand","Samolaco","Sandalwood","Sanfratellano","Sang-chaud lourd de Saxe-Thuringe","Sanhe","Sara","Sarcidano","Selale","Selle argentin","Selle australien","Selle autrichien","Selle de Thuringe","Selle fran\xE7ais","Selle italien","Selle russe","Selle slovaque","Selle slov\xE8ne","Selle sud-africain","Selle su\xE9dois","Selle tch\xE8que","Selle ukrainien","Selle uruguayen","Serrano","Shagya","Shan","Shandan","Shetland","Shetland am\xE9ricain","Shirazi","Shire","Shirvan","Siaen","Siciliano","Siglavy","Sikang","Sil\xE9sien","Sindh Desi","Sini","Sistani","Skogsruss","Skyros","Sok\xF3lski","Sorra\xEFa","Soudanais","Spanish Jennet","Spanish Norman","Spiti","Sportaloosa","Spotted saddle horse","Standardbred","Stock Horse australien","Sud-africain sang-chaud","Suffolk Punch","Sulphur","Sumba","Sumbar-Sandel-Arabe","Sumbawa","Sunicho","Su\xE9dois du nord","Sztumski","Tadjik","Tagaytay","Taishuh","Tar\u0101i","Tavda","Tawleed","Tchenaran","Tchernomor","Tch\xE8que sang-froid","Tch\xE9co-morave belge","Tennessee Walker","Tersk","Tes","Thessalien","Tieling","Tiger horse","Timor","Tokara","Tolfetano","Tonga","Tooraq","Topu\u011Fu K\u0131ll\u0131","Tori","Touchine","Touva","Trait allemand du Sud","Trait argentin","Trait australien","Trait belge","Trait bi\xE9lorusse","Trait bulgare","Trait cr\xE8me am\xE9ricain","Trait de Croatie","Trait de Rh\xE9nanie","Trait de Saxe-Thuringe","Trait de l'Altmark","Trait de la For\xEAt-Noire","Trait du Nord","Trait du Schleswig","Trait estonien","Trait hanovrien d'origine Schleswig","Trait hongrois","Trait irlandais","Trait italien","Trait lituanien","Trait n\xE9erlandais","Trait pie am\xE9ricain","Trait polonais","Trait roumain","Trait russe","Trait sovi\xE9tique","Trait westphalien","Trakehner","Trakya","Transba\xEFkal","Trocha Pura Colombiana","Trote y galope","Trotteur D\xF8le","Trotteur Latgale","Trotteur Orlov","Trotteur allemand","Trotteur croate","Trotteur cubain","Trotteur danois","Trotteur de Ljutomer","Trotteur espagnol","Trotteur finlandais sang-chaud","Trotteur fran\xE7ais","Trotteur italien","Trotteur m\xE9tis","Trotteur roumain","Trotteur russe","Trotteur scandinave","Trotteur yougoslave","Tuigpaard","Turkestani","Ujumqin","Unmol","Uzunyayla","Viatka","Virginia highlander","Vlaamperd","Vladimir","Voronej","Warlander","Waziri","Welara","Welsh","Welsh cob","Welsh de type cob","Welsh mountain","Wenshan","Westphalien","Wielkopolski","Wilwal","Wushen","W\xFCrttemberger","Xiangfen","Xilinguole","Xinihe","Yabu","Yanqi","Yargha","Yili","Yiwu","Yonaguni","Yongning","Yunnan","Yushu","Yuta","Zabaikal","Zakynthos","Zanskari","Zhongdian","Zweibr\xFCcker","cheval en Afghanistan","isra\xE9lien","sBs","\xC7ukurova","\xD8stland","\u017Demaitukas"];var s=["Abeille d\xE9coupeuse de la luzerne","Abeille europ\xE9enne","Abeille tueuse","Abeille \xE0 miel","Agrion jouvencelle","Agrion \xE9l\xE9gant","Ammophile des sables","Anax empereur","Anoph\xE8le","Apollon","Argus bleu","Ascalaphe soufr\xE9","Atlas","Aurore","Azur\xE9 du serpolet","Balanin des noisettes","Blastophage","Blatte","Blatte am\xE9ricaine","Blatte de Madagascar","Blatte germanique","Blatte orientale","Bombyle","Bombyx du m\xFBrier","Bombyx du ricin","Bombyx \xE9ri","Bostryche typographe","Bourdon","Bourdon des champs","Bourdon des pierres","Bourdon des pr\xE9s","Bourdon terrestre","Bousier","Calliphorid\xE9","Calopt\xE9ryx vierge","Capricorne de l'\xE9pine de J\xE9rusalem","Capricorne des cactus","Capricorne des maisons","Capricorne du ch\xEAne","Capricorne musqu\xE9","Carabe dor\xE9","Carabe violet","Cercope","Charan\xE7on du bl\xE9","Charan\xE7on rouge des palmiers","Chironome","Chouette","Chrysope","Chrysope nacr\xE9e","Chrysope verte","Cicadelle blanche","Cicind\xE8le","Cigale","Cigale bossue","Cigale bossue du ch\xEAne","Cigale grise","Cigale pl\xE9b\xE9ienne","Citron","Citron de Provence","Clairon des abeilles","Clairon des ruches","Coccinelle","Coccinelle asiatique","Coccinelle \xE0 deux points","Coccinelle \xE0 quatorze points","Coccinelle \xE0 sept points","Coccinelle \xE0 vingt-deux points","Courtili\xE8re","Criquet","Criquet migrateur","C\xE9toine","C\xE9toine dor\xE9e","C\xE9toine grise","C\xE9toine margin\xE9e","C\xE9toine verte","Demi diable","Demi-deuil","Doryphore de la pomme de terre","Drosophile","Dynaste Hercule","D\xE9esse pr\xE9cieuse","Euglossine","Fausse teigne de la cire","Flamb\xE9","Fourmi","Fourmi balle de fusil","Fourmi de feu","Fourmi ensanglant\xE9e","Fourmilion","Frelon asiatique","Frelon europ\xE9en","Frelon g\xE9ant japonais","Gaz\xE9","Gendarme","Gerris","Grand Planeur","Grand diable","Grand paon de nuit","Grande aeschne","Grande saperde du peuplier","Grande sauterelle verte","Graphosome d'Italie","Greta oto","Grillon","Grillon champ\xEAtre","Grillon domestique","Gu\xEApe commune","Gu\xEApe germanique","Gu\xEApe-papillon","Hanneton","Hercule","Hulotte","Insecte","Lepture cordig\xE8re","Leste fianc\xE9","Libellule d\xE9prim\xE9e","Lucane cerf-volant","Lucilie bouch\xE8re","Lucilie bufonivore","Luciole","Lyctus","L\xE9thoc\xE8re d'Am\xE9rique","Machaon","Magicienne dentel\xE9e","Mante religieuse","Mante-orchid\xE9e","Monarque","Morpho cypris","Mouche domestique","Mouche du vinaigre","Mouche ts\xE9-ts\xE9","Mouche verte commune","Moustique tigre","M\xE9lipone","Neuropt\xE8re","N\xE8pe","Osmie cornue","Papillon","Papillon du palmier","Perle","Petit apollon","Petit capricorne","Petit diable","Phasme","Phasme b\xE2ton","Phrygane","Phyllie","Poisson d'argent","Poliste","Poliste g\xE9ant","Polyph\xE8me","Pou","Pou du pubis","Prom\xE9th\xE9e","Puce","Puceron","Punaise","Punaise d'eau g\xE9ante","Punaise de la Morelle","Punaise de lit","Punaise orn\xE9e","Punaise-\xE9pine","Rosalie alpine","Saturnie C\xE9cropia","Saturnie du cerisier","Saturnie du poirier","Sauterelle","Scarab\xE9e du d\xE9sert","Scarab\xE9e rhinoc\xE9ros europ\xE9en","Scarab\xE9e rhinoc\xE9ros japonais","Scolie des jardins","Sorci\xE8re blanche","Sphinx de Morgan","Sphinx de Wallace","Sphinx du tilleul","Taon","Termite","Tipule","Titan","Titiwai","Turquoise","T\xE9n\xE9brion meunier","Ver de bancoule","Ver luisant","Vrillette","Vrillette du pain","Weta","Weta des arbres","Weta des cavernes","Weta g\xE9ant","Xylocope violet","Zabre des c\xE9r\xE9ales","Zyg\xE8ne","\xC6schne","\xC6schne bleue","\xC6schne des joncs","\xC9ph\xE9m\xE8re"];var d=["Lion d'Afrique","Lion d'Asie","Lion de l'Atlas","Lion du Cap"];var c=["Alaska","Angora fran\xE7ais","Argent\xE9 de Champagne","Argent\xE9 de Saint Hubert","Blanc de Hotot","Blanc de Vend\xE9e","Bleu de Beveren","Bleu de Vienne","Brun marron de Lorraine","B\xE9lier anglais","B\xE9lier fran\xE7ais","Californien","Castorrex","Chamois de Thuringe","Fauve de Bourgogne","Feh de Marbourg","Feu Feh","Feu havane","Feu noir","Grand Chinchilla","Grand Russe","Gris bleu de Vienne","Gris de Vienne","Gris du Bourbonnais","G\xE9ant blanc du Bouscat","G\xE9ant des Flandres","G\xE9ant papillon fran\xE7ais","Havane fran\xE7ais","Hermine de Lutterbach","Hollandais","Japonais","Lapin blanc danois","Lapin chinchilla","Lapin ch\xE8vre","Li\xE8vre belge","Lynx","Nain Rex","Nain angora","Nain b\xE9lier Rex","Nain de couleur","Nain renard","Nain satin","Noir de Vienne","Normand","N\xE9o-Z\xE9landais","Papillon anglais","Papillon rh\xE9nan","Perlfeh","Petit b\xE9lier","Petit papillon","Polonais","Renard","Rex","Rhoen","Russe","Sabl\xE9 des Vosges","Satin","Separator","Zibeline","lapin b\xE9lier"];var m=["Alsophis ater","Anaconda bolivien","Anaconda curiy\xFA","Anaconda de Barbour","Anaconda de Bolivie","Anaconda de Deschauense","Anaconda du Paraguay","Anaconda g\xE9ant","Anaconda jaune","Anaconda vert","Anaconda \xE0 taches sombres","Barba amarilla","Boa","Boa arboricole de Madagascar","Boa arc-en-ciel","Boa arc-en-ciel cubain","Boa canin","Boa caoutchouc","Boa cubain","Boa d'Amazonie","Boa de Cuba","Boa de Dum\xE9ril","Boa de Madagascar","Boa de Maurice","Boa de Porto Rico","Boa de l'\xEEle Ronde","Boa de l'\xEEle Ronde de Dussumier","Boa de l'\xEEle Ronde de Schlegel","Boa de la Jama\xEFque","Boa des for\xEAts","Boa des for\xEAts de Madagascar","Boa des perroquets","Boa des sables","Boa des sables d'Afrique","Boa des savanes de Dum\xE9ril","Boa devin","Boa fouisseur de l'\xEEle Maurice","Boa fouisseur de l'\xEEle Ronde","Boa nain","Boa sobre","Boa terrestre de Madagascar","Boa \xE9meraude","Bongare","Bongare annel\xE9","Bongare candide","Bongare fasci\xE9","Bongare indien","Bongare ray\xE9","Boomslang","Bungare","Cinglard","Cobra","Cobra chinois","Cobra cracheur","Cobra cracheur du Mozambique","Cobra cracheur indon\xE9sien","Cobra cracheur rouge","Cobra cracheur \xE0 cou noir","Cobra cracheur \xE9quatorial","Cobra d'eau","Cobra d'eau ray\xE9","Cobra de mer","Cobra des Philipines du Nord","Cobra du Cap","Cobra du Mozambique","Cobra indien","Cobra royal","Cobra \xE0 lunettes","Cobra \xE0 monocle","Cobra \xE9gyptien","Coronelle","Coronelle girondine","Coronelle lisse","Couleuvre","Couleuvre brune","Couleuvre d'Esculape","Couleuvre d'eau","Couleuvre de Forsskal","Couleuvre de Montpellier","Couleuvre de l'Amour","Couleuvre de l'Ouest","Couleuvre des Plaines","Couleuvre diad\xE8me","Couleuvre du Nord-Ouest","Couleuvre fauve de l'Est","Couleuvre faux-corail","Couleuvre l\xE9opard","Couleuvre mauresque","Couleuvre mince","Couleuvre obscure","Couleuvre ray\xE9e","Couleuvre royale","Couleuvre tachet\xE9e","Couleuvre tessell\xE9e","Couleuvre verte","Couleuvre verte et jaune","Couleuvre vip\xE9rine","Couleuvre \xE0 capuchon","Couleuvre \xE0 collier","Couleuvre \xE0 collier am\xE9ricaine","Couleuvre \xE0 dos rouge","Couleuvre \xE0 jarreti\xE8re de Santa Cruz","Couleuvre \xE0 nez plat","Couleuvre \xE0 nez retrouss\xE9","Couleuvre \xE0 quatre raies","Couleuvre \xE0 ventre rouge","Couleuvre \xE0 \xE9chelons","Couleuvre-jarreti\xE8re","Couresse du Banc d'Anguilla","Couresse \xE0 ventre rouge","Crotale","Crotale cascabelle","Crotale cent pas","Crotale de Malaisie","Crotale de Mojave","Crotale de l'ouest","Crotale des bambous","Crotale des bois","Crotale des prairies","Crotale des tropiques","Crotale diamantin","Crotale diamantin de l'Ouest","Crotale du Texas","Crotale du Venezuela","Crotale du temple","Crotale muet","Crotale tigr\xE9","Crotale \xE0 losange","Cuatronarices","Curiy\xFA","Faux-corail de Sinaloa","Fer de lance","Fer de lance centro-am\xE9ricain","Fer de lance commun","Fer de lance de la Martinique","Grage grands carreaux","Grand anaconda","Grands carreaux","Habu","Habu Hime","Habu d'Arisa","Habu d'Okinawa","Habu de Kikuchi","Habu de Kume-jima","Habu de Sakishima","Habu de Ta\xEFwan","Habu de Tokara","Jaracac\xE1 amarilla","Jaracac\xE1 jaune","Jararaca","Jararaca Pintada","Jararaca-cruzeira","Jararaca-ilhoa","Jararacu\xE7u","Katuali","Macagua","Mamba de Jameson","Mamba noir","Mamba vert","Mamba vert de Guin\xE9e","Mamba vert de Jameson","Mamba vert de l'Est","Mamba vert de l'Ouest","Mapanare","Ma\xEEtre de la brousse","Mocassin d'eau","Plature","Python","Python am\xE9thyste","Python arboricole vert","Python arboricole vert australien","Python birman","Python boule","Python d'Am\xE9rique centrale","Python d'Angola","Python de Boelen","Python de Children","Python de Macklot","Python de Papouasie","Python de Ramsay","Python de Seba","Python de Stimson","Python de Timor","Python fouisseur du Mexique","Python indien","Python malais","Python molure","Python olive","Python pygm\xE9","Python royal","Python r\xE9ticul\xE9","Python \xE0 l\xE8vres blanches","Python \xE0 t\xEAte noire","Python \xE0 t\xEAte noire d'Australie","P\xE9lamide","\xC9chide","\xC9chide car\xE9n\xE9e","\xC9chide des pyramides","\xC9chide \xE0 ventre blanc","\xD1acanin\xE1"];var p=["chat","cheval","chien","crocodilien","c\xE9tac\xE9","insecte","lapin","lion","oiseau","ours","poisson","serpent","vache"];var qe={bear:e,bird:r,cat:a,cetacean:i,cow:n,crocodilia:o,dog:l,fish:t,horse:u,insect:s,lion:d,rabbit:c,snake:m,type:p},h=qe;var g=["abricot","acajou","aigue-marine","amande","amarante","ambre","am\xE9thyste","anthracite","argent","aubergine","aurore","avocat","azur","basan\xE9","beurre","bis","bisque","bistre","bitume","blanc cass\xE9","blanc lunaire","bleu acier","bleu bleuet","bleu canard","bleu charrette","bleu ciel","bleu de Prusse","bleu de cobalt","bleu givr\xE9","bleu marine","bleu nuit","bleu outremer","bleu paon","bleu persan","bleu p\xE9trole","bleu roi","bleu saphir","bleu turquin","bleu \xE9lectrique","bl\xE9","bouton d'or","brique","bronze","brou de noix","caca d'oie","cacao","cachou","caf\xE9","cannelle","capucine","caramel","carmin","carotte","chamois","chartreuse","chocolat","cinabre","citrouille","coquille d'\u0153uf","corail","couleurs de Mars","cramoisi","cuisse de nymphe","cuivre","cyan","c\u0153ruleum","fauve","flave","fraise","framboise","fum\xE9e","garance","glauque","glycine","grenadine","grenat","gris acier","gris de Payne","gris fer","gris perle","gris souris","groseille","gr\xE8ge","gueules","h\xE9liotrope","incarnat","indigo","isabelle","jaune canari","jaune citron","jaune de Naples","jaune de cobalt","jaune imp\xE9rial","jaune mimosa","lavalli\xE8re","lavande","lie de vin","lilas","lime","lin","magenta","malachite","mandarine","marron","mastic","mauve","ma\xEFs","menthe","moutarde","nacarat","nankin","noisette","ocre","ocre rouge","olive","or","orange br\xFBl\xE9","orchid\xE9e","orpiment","outremer v\xE9ritable","oxyde de fer","paille","parme","pelure d'oignon","pervenche","pistache","poil de chameau","ponceau","pourpre","prasin","prune","puce","rose Mountbatten","rouge cardinal","rouge cerise","rouge d'Andrinople","rouge de Falun","rouge feu","rouge tomate","rouille","rubis","sable","safre","sang de b\u0153uf","sanguine","sarcelle","saumon","sinople","smalt","soufre","s\xE9pia","tabac","terre d'ombre","tomette","topaze","tourterelle","turquoise","vanille","vermeil","vermillon","vert V\xE9ron\xE8se","vert bouteille","vert c\xE9ladon","vert d'eau","vert de Hooker","vert de vessie","vert imp\xE9rial","vert lichen","vert oxyde de chrome","vert perroquet","vert poireau","vert pomme","vert prairie","vert printemps","vert sapin","vert sauge","vert tilleul","vert \xE9pinard","vert-de-gris","violet","violet d'\xE9v\xEAque","viride","zinzolin","\xE9carlate","\xE9cru","\xE9meraude"];var Ge={human:g},b=Ge;var A=["Automobile","Beaut\xE9","Bijoux","B\xE9b\xE9","Chaussures","Electronique","Enfants","Films","Industrie","Jardin","Jeux","Jouets","Livres","Maison","Musique","Ordinateurs","Outils","Plein air","Sant\xE9","Sports","V\xEAtements","\xC9picerie"];var C=["Bicyclette \xE0 1 vitesse, pneus 1/2 ballon. Cadre de 52cm. Jantes chrom\xE9es. Roue Hore.  Moyeux ind\xE9r\xE9glables. 2 freins sur jantes. Guidon trials. Garde-boue et couvre chaine en acier \xE9maill\xE9. Porte-bagages. Gardejupes. P\xE9dales \xE0 blocs caoutchouc. \xC9mail couleur. Selle route cuir. Sacoche avec outillage. Pompe de cadre. Timbre avertisseur.","Cadre raccord bras\xE9 de 53 ou 58 %. Jantes en acier \xE9maill\xE9es. Pneus \u201CHiron\u201D 700 x 35, garantis 12 mois. Pignon roue libre \xE0 emboitement hexagonal. Frein \u201CHirondelle\u201D sur jante arri\xE8re. Garde-boue m\xE9tal.","Carrosserie en t\xF4le d'acier laqu\xE9 blanc mont\xE9e sur roues pour faciliter le d\xE9placement, couvercle laqu\xE9 blanc, dessus et cuve en m\xE9tal \xE9maille marron, inalt\xE9rable a l'eau de lessive et a la chaleur,","Ce magnifique radio-phono comprend un excellent r\xE9cepteur radioL'ensemble, dans une belle \xE9b\xE9nisterie teint\xE9e palissandre, forme un tr\xE8s phonique 6 lampes et un tourne-disque de vitesses plac\xE9 sous le couvercle. Beau meuble. Ce mod\xE8le, dont le montage particuli\xE8rement soigne assure un tr\xE8s bon rendement aussi bien en radio qu'en phono, est garanti mn an.","Ces m\xE9dailles et \xE9pingles sont en argent avec patine artistique. Elles ont \xE9t\xE9 compos\xE9es et frapp\xE9es sp\xE9cialement dans les ateliers de l'\xC9tat pour la Soci\xE9t\xE9 l'Hirondelle.","Lanterne cuivre fort, finement nickel\xE9, chute d'eau r\xE9glable, suspension antivibratrice, projecteur diam\xE8tre cm2, avec verre bomb\xE9. Dur\xE9e d'\xE9clairage 3 heures. Poids 395 grammes.","Lunettes \xE9tanches, monture caoutchouc moul\xE9 de 1re qual. glaces rondes de 55 mm de diam. en verre clair. Les lunettes prot\xE8gent les yeux contre les poussi\xE8res, fum\xE9es et gaz industriels et se portent av. nos masques 5862-5864. Pds 60 gr.","Maillot en coton fin \xE0 rayures se boutonnant devant pour enfants.","Montre-bracelet, dite \u201Cd'Aviateur\u201D, m\xE9tal inalt\xE9rable, diam. 435ym. Mouvement de pr\xE9cision chronographe, cadran avec grande aiguille trotteuse, permettant la lecture 1/25de seconde.","Moteur Villiers. Puissance au frein : 7 HP, 3 vitesses, lancement au pied, \xE9clairage \xE9lectrique, carrosserie 2 places, coffre \xE0 outils, outillage complet, capote et pare-brise.","Petite griffe \xE0 sarcler. 5 dents en acier, largeur 8 cm. poign\xE9e estamp\xE9e, longueur 26 cm. poids 150 gr. Pour ameublir le sol, arracher les herbes entre les plantes ou fleurs cultiv\xE9es en lignes rapproch\xE9es.","Rasoir de s\xFBret\xE9 \u201CPrima\u201D tout en laiton massif nickel\xE9 chrom\xE9, manche molet\xE9 bien en main. Peigne et contre-peigne galb\xE9s tenant tout d'une pi\xE8ce, fermeture \xE0 charni\xE8re, blocage instantan\xE9 de la lame.","R\xE9cepteurs de t\xE9l\xE9vision \xE0 haute d\xE9finition 819 lignes, donnant une image tr\xE8s nette et d'un contraste agr\xE9able ne fatiguant pas la vue, le montage de la partie radio donne un son absolument remarquable.","Tous nos appareils sont blind\xE9s pour que leur rayonnement ne trouble pas les r\xE9cepteurs radiophoniques, et ils fonctionnent sur courant alternatif 50 riodes 110 et 220 volts. Ils sont garantis pendant 1 an; toutefois, suivant la r\xE8gle, le tube cathodique est garanti pour 6 mois seulement."];var v={adjective:["Artisanal","Ergonomique","Fait main","Fantastique","G\xE9nial","G\xE9n\xE9rique","Incroyable","Intelligent","Licenci\xE9","Luxueux","Magnifique","Moderne","Oriental","Petit","Pratique","Raffin\xE9","Recycl\xE9","Rustique","Sans marque","Savoureux","Sur mesure","\xC9lectronique","\xC9l\xE9gant"],material:["Acier","Bois","Bronze","B\xE9ton","Caoutchouc","Congel\xE9","Coton","Doux","Frais","Granit","M\xE9tal","Plastique"],product:["Boule","Chaise","Chapeau","Chaussures","Chemise","Clavier","Frites","Fromage","Gants","Lard","Ordinateur","Pantalon","Pizza","Poisson","Poulet","Salade","Saucisses","Savon","Serviettes","Souris","Table","Thon","Voiture","V\xE9lo"]};var Te={department:A,product_description:C,product_name:v},f=Te;var B=["EI","EURL","GIE","SA","SARL","SAS","SASU","SCA","SCOP","SCS","SEM","SNC"];var P=["{{person.last_name.generic}} et {{person.last_name.generic}}","{{person.last_name.generic}} {{company.legal_entity_type}}"];var xe={legal_entity_type:B,name_pattern:P},y=xe;var M={wide:["Ao\xFBt","Avril","D\xE9cembre","F\xE9vrier","Janvier","Juillet","Juin","Mai","Mars","Novembre","Octobre","Septembre"],wide_context:["ao\xFBt","avril","d\xE9cembre","f\xE9vrier","janvier","juillet","juin","mai","mars","novembre","octobre","septembre"],abbr:["ao\xFBt","avril","d\xE9c.","f\xE9vr.","janv.","juil.","juin","mai","mars","nov.","oct.","sept."]};var S={wide:["Dimanche","Jeudi","Lundi","Mardi","Mercredi","Samedi","Vendredi"],wide_context:["dimanche","jeudi","lundi","mardi","mercredi","samedi","vendredi"],abbr:["Dim","Jeu","Lun","Mar","Mer","Sam","Ven"],abbr_context:["dim","jeu","lun","mar","mer","sam","ven"]};var Le={month:M,weekday:S},q=Le;var G=["Carte de cr\xE9dit","Ch\xE8que","Epargne","Investissement","March\xE9 mon\xE9taire","Pr\xEAt immobilier","Pr\xEAt personnel"];var T=[{name:"Dollar",code:"USD",symbol:"$",numericCode:"840"},{name:"Dollar canadien",code:"CAD",symbol:"$",numericCode:"124"},{name:"Peso mexicain",code:"MXN",symbol:"$",numericCode:"484"},{name:"Florin",code:"AWG",symbol:"\u0192",numericCode:"533"},{name:"Baisse du dollar",code:"BBD",symbol:"$",numericCode:"052"},{name:"Dollar bermudien",code:"BMD",symbol:"$",numericCode:"060"},{name:"Dollar des Bahamas",code:"BSD",symbol:"$",numericCode:"044"},{name:"Peso dominicain",code:"DOP",symbol:"$",numericCode:"214"},{name:"Dollar jama\xEFcain",code:"JMD",symbol:"$",numericCode:"388"},{name:"Quetzal guat\xE9malt\xE8que",code:"GTQ",symbol:"Q",numericCode:"320"},{name:"Balboa panam\xE9en",code:"PAB",symbol:"B/.",numericCode:"590"},{name:"Dollar des Cara\xEFbes",code:"XCD",symbol:"$",numericCode:"951"},{name:"euro",code:"EUR",symbol:"\u20AC",numericCode:"978"},{name:"Livre sterling",code:"GBP",symbol:"\xA3",numericCode:"826"},{name:"Course g\xE9orgienne",code:"GEL",symbol:"\u20BE",numericCode:"981"},{name:"Lev bulgare",code:"BGN",symbol:"\u043B\u0432",numericCode:"975"},{name:"Franc suisse",code:"CHF",symbol:"CHF",numericCode:"756"},{name:"Couronne danoise",code:"DKK",symbol:"kr",numericCode:"208"},{name:"R\xE9publique tch\xE8que Couronne",code:"CZK",symbol:"K\u010D",numericCode:"203"},{name:"Kuna croate",code:"HRK",symbol:"kn",numericCode:"191"},{name:"Forint hongrois",code:"HUF",symbol:"ft",numericCode:"348"},{name:"couronne norv\xE9gienne",code:"NOK",symbol:"kr",numericCode:"578"},{name:"Rouble russe",code:"RUB",symbol:"\u20BD",numericCode:"643"},{name:"Zloty polonais",code:"PLN",symbol:"z\u0142",numericCode:"985"},{name:"Leu roumain",code:"RON",symbol:"lei",numericCode:"946"},{name:"couronne su\xE9doise",code:"SEK",symbol:"kr",numericCode:"752"},{name:"Hryvnia ukrainienne",code:"UAH",symbol:"\u20B4",numericCode:"980"},{name:"lire turque",code:"TRY",symbol:"\u20BA",numericCode:"949"},{name:"Argentine Peso",code:"ARS",symbol:"$",numericCode:"032"},{name:"Bolivien bolivien",code:"BOB",symbol:"Bs.",numericCode:"068"},{name:"Real br\xE9silien",code:"BRL",symbol:"R$",numericCode:"986"},{name:"Peso chilien",code:"CLP",symbol:"$",numericCode:"152"},{name:"Peso colombien",code:"COP",symbol:"$",numericCode:"170"},{name:"Nouveau Sol P\xE9ruvien",code:"PEN",symbol:"S/.",numericCode:"604"},{name:"Guarani paraguayen",code:"PYG",symbol:"\u20B2",numericCode:"600"},{name:"Peso uruguayen",code:"UYU",symbol:"$",numericCode:"858"},{name:"Bolivar v\xE9n\xE9zu\xE9lien",code:"VES",symbol:"Bs.",numericCode:"928"},{name:"Yen japonais",code:"JPY",symbol:"\xA5",numericCode:"392"},{name:"Bangladesh Taka",code:"BDT",symbol:"\u09F3",numericCode:"050"},{name:"Le yuan chinois",code:"CNY",symbol:"\xA5",numericCode:"156"},{name:"Dollar de Hong Kong",code:"HKD",symbol:"$",numericCode:"344"},{name:"Roupie indienne",code:"INR",symbol:"\u20B9",numericCode:"356"},{name:"Riel cambodgien",code:"KHR",symbol:"\u17DB",numericCode:"116"},{name:"Poulet",code:"LAK",symbol:"\u20AD",numericCode:"418"},{name:"Roupie sri lankaise",code:"LKR",symbol:"\u0DBB\u0DD4",numericCode:"144"},{name:"Rufiyaa",code:"MVR",symbol:".\u0783",numericCode:"462"},{name:"cloche malaisienne",code:"MYR",symbol:"RM",numericCode:"458"},{name:"Roupie n\xE9palaise",code:"NPR",symbol:"\u0930\u0942",numericCode:"524"},{name:"Peso philippin",code:"PHP",symbol:"\u20B1",numericCode:"608"},{name:"Roupie pakistanaise",code:"PKR",symbol:"\u20A8",numericCode:"586"},{name:"Dollar de Singapour",code:"SGD",symbol:"$",numericCode:"702"},{name:"Baht tha\xEFlandais",code:"THB",symbol:"\u0E3F",numericCode:"764"},{name:"Nouveau dollar de Ta\xEFwan",code:"TWD",symbol:"$",numericCode:"901"},{name:"Dong vietnamien",code:"VND",symbol:"\u20AB",numericCode:"704"},{name:"Dollar australien",code:"AUD",symbol:"$",numericCode:"036"},{name:"Dollar fidjien",code:"FJD",symbol:"$",numericCode:"242"},{name:"Dollar n\xE9o-z\xE9landais",code:"NZD",symbol:"$",numericCode:"554"},{name:"Franc CFP",code:"XPF",symbol:"\u20A3",numericCode:"953"},{name:"Livre \xE9gyptienne",code:"EGP",symbol:"\xA3",numericCode:"818"},{name:"C\xE9di ghan\xE9en",code:"GHS",symbol:"\u20B5",numericCode:"936"},{name:"Dalasi",code:"GMD",symbol:"D",numericCode:"270"},{name:"Shilling kenyan",code:"KES",symbol:"Sh",numericCode:"404"},{name:"Dirham marocain",code:"MAD",symbol:"DH",numericCode:"504"},{name:"Ariary malgache",code:"MGA",symbol:"Ar",numericCode:"969"},{name:"Roupie mauricienne",code:"MUR",symbol:"\u20A8",numericCode:"480"},{name:"Dollar namibien",code:"NAD",symbol:"$",numericCode:"516"},{name:"Naira nig\xE9rian",code:"NGN",symbol:"\u20A6",numericCode:"566"},{name:"Roupie",code:"SCR",symbol:"\u20A8",numericCode:"690"},{name:"Dinar tunisien",code:"TND",symbol:"DT",numericCode:"788"},{name:"Shilling ougandais",code:"UGX",symbol:"Sh",numericCode:"800"},{name:"CFA Franc BEAC",code:"XAF",symbol:"Fr",numericCode:"950"},{name:"CFA Franc BCEAO",code:"XOF",symbol:"Fr",numericCode:"952"},{name:"Rand sud-africain",code:"ZAR",symbol:"Br",numericCode:"710"},{name:"Dirham des \xC9mirats arabes unis",code:"AED",symbol:"\u062F.\u0625",numericCode:"784"},{name:"Nouveau Shekel isra\xE9lien",code:"ILS",symbol:"\u20AA",numericCode:"376"},{name:"Livre syrienne",code:"SYP",symbol:"\xA3",numericCode:"760"},{name:"Dinar jordanien",code:"JOD",symbol:"\u062F.\u0627",numericCode:"400"},{name:"Dinar kowe\xEFtien",code:"KWD",symbol:"\u062F.\u0643",numericCode:"414"},{name:"Livre libanaise",code:"LBP",symbol:"\u0644.\u0644",numericCode:"422"},{name:"Rial omanais",code:"OMR",symbol:"\u0631.\u0639.",numericCode:"512"},{name:"Rial qatari",code:"QAR",symbol:"\u0631.\u0642",numericCode:"634"},{name:"Riyal saoudien",code:"SAR",symbol:"",numericCode:"682"},{name:"Bitcoin",code:"BTC",symbol:"\u20BF",numericCode:"000"},{name:"Ethereum",code:"ETH",symbol:"\u039E",numericCode:"000"},{name:"Litecoin",code:"LTC",symbol:"\u0141",numericCode:"000"},{name:"Ripples",code:"XRP",symbol:"XRP",numericCode:"000"}];var x=["d\xE9p\xF4t","facture","paiement","retrait"];var Re={account_type:G,currency:T,transaction_type:x},L=Re;var R=["com","eu","fr","info","name","net","org"];var D=["gmail.com","hotmail.fr","yahoo.fr"];var De={domain_suffix:R,free_email:D},F=De;var H=["####","###","##","#"];var k=["Aix-en-Provence","Ajaccio","Amiens","Angers","Antibes","Antony","Argenteuil","Asni\xE8res-sur-Seine","Aubervilliers","Aulnay-sous-Bois","Avignon","Beauvais","Besan\xE7on","B\xE9ziers","Bordeaux","Boulogne-Billancourt","Bourges","Brest","Caen","Calais","Cannes","Cayenne","Cergy","Chamb\xE9ry","Champigny-sur-Marne","Cholet","Clermont-Ferrand","Clichy","Colmar","Colombes","Courbevoie","Cr\xE9teil","Dijon","Drancy","Dunkerque","Fort-de-France","Grenoble","Hy\xE8res","Issy-les-Moulineaux","Ivry-sur-Seine","La Rochelle","La Seyne-sur-Mer","Le Havre","Le Mans","Le Tampon","Les Abymes","Levallois-Perret","Lille","Limoges","Lorient","Lyon","Marseille","M\xE9rignac","Metz","Montauban","Montpellier","Montreuil","Mulhouse","Nancy","Nanterre","Nantes","Neuilly-sur-Seine","Nice","N\xEEmes","Niort","Noisy-le-Grand","Orl\xE9ans","Paris","Pau","Perpignan","Pessac","Poitiers","Quimper","Reims","Rennes","Roubaix","Rouen","Rueil-Malmaison","Saint-Denis","Saint-\xC9tienne","Saint-Maur-des-Foss\xE9s","Saint-Nazaire","Saint-Paul","Saint-Pierre","Saint-Quentin","Sarcelles","Strasbourg","Toulon","Toulouse","Tourcoing","Tours","Troyes","Valence","V\xE9nissieux","Versailles","Villejuif","Villeneuve-d'Ascq","Villeurbanne","Vitry-sur-Seine"];var N=["{{location.city_name}}"];var z=["Afghanistan","Albanie","Alg\xE9rie","Andorre","Angola","Antigua-et-Barbuda","Argentine","Arm\xE9nie","Australie","Autriche","Azerba\xEFdjan","Bahamas","Bahre\xEFn","Bangladesh","Barbade","Bi\xE9lorussie","Belgique","Belize","B\xE9nin","Bhoutan","Bolivie","Bosnie-Herz\xE9govine","Botswana","Br\xE9sil","Brunei","Bulgarie","Burkina Faso","Burundi","Cambodge","Cameroun","Canada","Cap-Vert","R\xE9publique centrafricaine","Tchad","Chili","Chine","Colombie","Comores","Costa Rica","C\xF4te d\u2019Ivoire","Croatie","Cuba","Chypre","R\xE9publique tch\xE8que","R\xE9publique d\xE9mocratique du Congo","Danemark","Djibouti","Dominique","R\xE9publique dominicaine","Timor oriental","\xC9quateur","\xC9gypte","Salvador","Guin\xE9e \xE9quatoriale","\xC9rythr\xE9e","Estonie","Eswatini","\xC9thiopie","Fidji","Finlande","France","Gabon","Gambie","G\xE9orgie","Allemagne","Ghana","Gr\xE8ce","Grenade","Guatemala","Guin\xE9e","Guin\xE9e-Bissau","Guyane","Ha\xEFti","Honduras","Hongrie","Islande","Inde","Indon\xE9sie","Iran","Irak","Irlande","Isra\xEBl","Italie","Jama\xEFque","Japon","Jordan","Kazakhstan","Kenya","Kiribati","Kowe\xEFt","Kirghizistan","Laos","Lettonie","Liban","Lesotho","Liberia","Libye","Liechtenstein","Lituanie","Luxembourg","Madagascar","Malawi","Malaisie","Maldives","Mali","Malte","\xCEles Marshall","Mauritanie","Maurice","Mexique","Micron\xE9sie","Moldavie","Monaco","Mongolie","Mont\xE9n\xE9gro","Maroc","Mozambique","Birmanie","Namibie","Nauru","N\xE9pal","Pays-Bas","Nouvelle-Z\xE9lande","Nicaragua","Niger","Nigeria","Cor\xE9e du Nord","Norv\xE8ge","Oman","Pakistan","Palau","Panama","Papouasie-Nouvelle-Guin\xE9e","Paraguay","P\xE9rou","Philippines","Pologne","Portugal","Qatar","R\xE9publique du Congo","Mac\xE9doine","Roumanie","Russie","Rwanda","Saint-Christophe-et-Ni\xE9v\xE8s","Sainte-Lucie","Saint-Vincent-et-les-Grenadines","Samoa","Saint-Marin","S\xE3o Tom\xE9-et-Principe","Arabie saoudite","S\xE9n\xE9gal","Serbie","Seychelles","Sierra Leone","Singapour","Slovaquie","Slov\xE9nie","\xCEles Salomon","Somalie","Afrique du Sud","Cor\xE9e du Sud","Soudan du Sud","Espagne","Sri Lanka","Soudan","Suriname","Su\xE8de","Suisse","Syrie","Tadjikistan","Tanzanie","Tha\xEFlande","Togo","Tonga","Trinit\xE9-et-Tobago","Tunisie","Turquie","Turkm\xE9nistan","Tuvalu","Ouganda","Ukraine","\xC9mirats arabes unis","Royaume-Uni","\xC9tats-Unis","Uruguay","Ouzb\xE9kistan","Vanuatu","Venezuela","Vi\xEAt Nam","Y\xE9men","Zambie","Zimbabwe"];var V={cardinal:["Nord","Est","Sud","Ouest"],cardinal_abbr:["N","E","S","O"],ordinal:["Nord-est","Nord-ouest","Sud-est","Sud-ouest"],ordinal_abbr:["NE","NO","SE","SO"]};var j=["#####"];var J=["Apt. ###","# \xE9tage"];var E=["Alsace","Aquitaine","Auvergne","Basse-Normandie","Bourgogne","Bretagne","Centre","Champagne-Ardenne","Corse","Franche-Comt\xE9","Haute-Normandie","\xCEle-de-France","Languedoc-Roussillon","Limousin","Lorraine","Midi-Pyr\xE9n\xE9es","Nord-Pas-de-Calais","Pays de la Loire","Picardie","Poitou-Charentes","Provence-Alpes-C\xF4te d'Azur","Rh\xF4ne-Alpes"];var O={normal:"{{location.buildingNumber}} {{location.street}}",full:"{{location.buildingNumber}} {{location.street}} {{location.secondaryAddress}}"};var K=["{{location.street_prefix}} {{location.street_suffix}}"];var I=["All\xE9e","Voie","Rue","Avenue","Boulevard","Quai","Passage","Impasse","Place"];var w=["de l'Abbaye","Adolphe Mille","d'Al\xE9sia","d'Argenteuil","d'Assas","du Bac","de Paris","La Bo\xE9tie","Bonaparte","de la B\xFBcherie","de Caumartin","Charlemagne","du Chat-qui-P\xEAche","de la Chauss\xE9e-d'Antin","du Dahomey","Dauphine","Delesseux","du Faubourg Saint-Honor\xE9","du Faubourg-Saint-Denis","de la Ferronnerie","des Francs-Bourgeois","des Grands Augustins","de la Harpe","du Havre","de la Huchette","Joubert","Laffitte","Lepic","des Lombards","Marcadet","Moli\xE8re","Monsieur-le-Prince","de Montmorency","Montorgueil","Mouffetard","de Nesle","Oberkampf","de l'Od\xE9on","d'Orsel","de la Paix","des Panoramas","Pastourelle","Pierre Charron","de la Pompe","de Presbourg","de Provence","de Richelieu","de Rivoli","des Rosiers","Royale","d'Abbeville","Saint-Honor\xE9","Saint-Bernard","Saint-Denis","Saint-Dominique","Saint-Jacques","Saint-S\xE9verin","des Saussaies","de Seine","de Solf\xE9rino","Du Sommerard","de Tilsitt","Vaneau","de Vaugirard","de la Victoire","Zadkine"];var Fe={building_number:H,city_name:k,city_pattern:N,country:z,direction:V,postcode:j,secondary_address:J,state:E,street_address:O,street_pattern:K,street_prefix:I,street_suffix:w},W=Fe;var _=["alias","consequatur","aut","perferendis","sit","voluptatem","accusantium","doloremque","aperiam","eaque","ipsa","quae","ab","illo","inventore","veritatis","et","quasi","architecto","beatae","vitae","dicta","sunt","explicabo","aspernatur","odit","fugit","sed","quia","consequuntur","magni","dolores","eos","qui","ratione","sequi","nesciunt","neque","dolorem","ipsum","dolor","amet","consectetur","adipisci","velit","non","numquam","eius","modi","tempora","incidunt","ut","labore","dolore","magnam","aliquam","quaerat","enim","ad","minima","veniam","quis","nostrum","exercitationem","ullam","corporis","nemo","ipsam","voluptas","suscipit","laboriosam","nisi","aliquid","ex","ea","commodi","autem","vel","eum","iure","reprehenderit","in","voluptate","esse","quam","nihil","molestiae","iusto","odio","dignissimos","ducimus","blanditiis","praesentium","laudantium","totam","rem","voluptatum","deleniti","atque","corrupti","quos","quas","molestias","excepturi","sint","occaecati","cupiditate","provident","perspiciatis","unde","omnis","iste","natus","error","similique","culpa","officia","deserunt","mollitia","animi","id","est","laborum","dolorum","fuga","harum","quidem","rerum","facilis","expedita","distinctio","nam","libero","tempore","cum","soluta","nobis","eligendi","optio","cumque","impedit","quo","porro","quisquam","minus","quod","maxime","placeat","facere","possimus","assumenda","repellendus","temporibus","quibusdam","illum","fugiat","nulla","pariatur","at","vero","accusamus","officiis","debitis","necessitatibus","saepe","eveniet","voluptates","repudiandae","recusandae","itaque","earum","hic","tenetur","a","sapiente","delectus","reiciendis","voluptatibus","maiores","doloribus","asperiores","repellat"];var He={word:_},Y=He;var ke={title:"French",code:"fr",language:"fr",endonym:"Fran\xE7ais",dir:"ltr",script:"Latn"},U=ke;var Z=["Blues","Classique","Country","Folk","Funk","Hip Hop","Jazz","Latine","Lofi","Metal","Pop","Rap","Reggae","Rock","Soul","Vari\xE9t\xE9","World","\xC9lectronique"];var Ne={genre:Z},Q=Ne;var $={generic:["Aaron","Abdon","Abdonie","Abel","Abelin","Abeline","Abigaelle","Abiga\xEFl","Abondance","Abraham","Absalon","Ab\xE9lard","Acace","Acacie","Acanthe","Achaire","Achille","Adalard","Adalbald","Adalbaude","Adalbert","Adalb\xE9ron","Adalric","Adalsinde","Adam","Adegrin","Adegrine","Adel","Adelin","Adeline","Adelphe","Adeltrude","Adenet","Adh\xE9mar","Adjutor","Adolphe","Adolphie","Adonis","Adonise","Adrast\xE9e","Adrehilde","Adrien","Adrienne","Ad\xE8le","Ad\xE9la\xEFde","Ad\xE9lie","Ad\xE9odat","Agapet","Agathange","Agathe","Agathon","Agilbert","Agilberte","Agla\xE9","Agnan","Agnane","Agnefl\xE8te","Agn\xE8s","Agrippin","Agrippine","Aimable","Aim\xE9","Aim\xE9e","Alain","Alaine","Ala\xEFs","Alban","Albane","Albert","Alberte","Alb\xE9rade","Alb\xE9ric","Alcibiade","Alcide","Alcidie","Alcime","Alcine","Alcyone","Aldegonde","Aldonce","Aldric","Aleaume","Aleth","Alexandre","Alexandrine","Alexanne","Alexine","Alexis","Alice","Aliette","Aline","Alix","Aliz\xE9","Ali\xE9nor","Alliaume","Almine","Almire","Aloyse","Alo\xEFs","Alo\xEFse","Alphonse","Alphonsine","Alph\xE9e","Alpinien","Alth\xE9e","Alver\xE8de","Amaliane","Amalric","Amalth\xE9e","Amande","Amandin","Amandine","Amant","Amante","Amarande","Amaranthe","Amaryllis","Ambre","Ambroise","Ambroisie","Ameline","Amiel","Aminte","Amour","Am\xE9d\xE9e","Am\xE9liane","Am\xE9lie","Am\xE9lien","Am\xE9thyste","Anastase","Anastasie","Anatole","Anatolie","Ana\xEBl","Ana\xEBlle","Ana\xEFs","Ancelin","Anceline","Andoche","Andr\xE9","Andr\xE9e","And\xE9ol","Angadr\xEAme","Ange","Angeline","Angilbe","Angilberte","Angilran","Angoustan","Ang\xE8le","Ang\xE9lina","Ang\xE9lique","Anicet","Anicette","Anic\xE9e","Annabelle","Anne","Annette","Annibal","Annonciade","Ansbert","Ansberte","Anselme","Anstrudie","Anthelme","Anthelmette","Antide","Antigone","Antoine","Antoinette","Antonin","Antonine","An\xE9mone","Aph\xE9lie","Apollinaire","Apolline","Aquilin","Aquiline","Arabelle","Arcade","Arcadie","Archambaud","Archange","Archibald","Argine","Arian","Ariane","Aricie","Ariel","Arielle","Ariste","Aristide","Arlette","Armance","Armand","Armande","Armandine","Armel","Armeline","Armelle","Armide","Armin","Armine","Arnaud","Arnaude","Arnould","Arolde","Arsino\xE9","Ars\xE8ne","Ars\xE8nie","Arthaud","Arthur","Arthurine","Arth\xE8me","Art\xE9mis","Ascelin","Asceline","Ascension","Assomption","Astart\xE9","Astride","Astr\xE9e","Ast\xE9rie","Athalie","Athanase","Athanasie","Athina","Ath\xE9na\xEFs","Aube","Aubertine","Aubry","Aude","Audebert","Audeline","Audouin","Audran","Audrey","Auguste","Augustine","Aure","Aurelle","Aurian","Auriane","Aurore","Aur\xE8le","Aur\xE9lie","Aur\xE9lienne","Auxane","Auxence","Aveline","Aviga\xEBlle","Avoye","Axel","Axeline","Axelle","Aymard","Aymardine","Aymeric","Aymon","Aymonde","Azal\xE9e","Azeline","Az\xE9lie","Balthazar","Baptiste","Barbe","Barnab\xE9","Barth\xE9lemy","Bartim\xE9e","Basile","Basilisse","Bastien","Bathilde","Baudouin","Benjamin","Beno\xEEt","Bernadette","Bernard","Berthe","Bertille","Bertrand","Betty","Beuve","Blaise","Blanche","Blandine","Boh\xE9mond","Bon","Boniface","Bouchard","Briac","Brice","Brieuc","Brigitte","Brunehaut","Brunehilde","Bruno","B\xE9atrice","B\xE9nigne","B\xE9n\xE9dicte","B\xE9ranger","B\xE9rang\xE8re","B\xE9rard","Calixte","Camille","Camillien","Cam\xE9lien","Candide","Capucine","Caribert","Carine","Carloman","Caroline","Cassandre","Cassien","Catherine","Chantal","Charlaine","Charlemagne","Charles","Charline","Charlotte","Childebert","Chilp\xE9ric","Chlo\xE9","Christelle","Christian","Christiane","Christine","Christodule","Christophe","Chrysole","Chrysostome","Chr\xE9tien","Claire","Clara","Clarence","Clarisse","Claude","Claudien","Claudine","Clio","Clotaire","Clotilde","Clovis","Cl\xE9andre","Cl\xE9lie","Cl\xE9mence","Cl\xE9ment","Cl\xE9mentine","Cl\xE9ry","Colin","Coline","Conception","Constance","Constant","Constantin","Coralie","Coraline","Corentin","Corentine","Corinne","Cyprien","Cyriaque","Cyrielle","Cyrille","C\xE9cile","C\xE9dric","C\xE9leste","C\xE9lestin","C\xE9lestine","C\xE9lien","C\xE9line","C\xE9saire","C\xE9sar","C\xF4me","Damien","Daniel","Danielle","Daphn\xE9","David","Delphin","Delphine","Denis","Denise","Diane","Didier","Dieudonn\xE9","Dieudonn\xE9e","Dimitri","Dominique","Dorian","Doriane","Dorine","Doroth\xE9e","Douce","D\xE9bora","D\xE9sir\xE9","Edgard","Edmond","Edm\xE9e","Ella","Elsa","Emma","Emmanuel","Emmanuelle","Emmelie","Enguerrand","Ernest","Ernestine","Estelle","Esther","Eubert","Eudes","Eudoxe","Eudoxie","Eug\xE8ne","Eug\xE9nie","Eulalie","Euphrasie","Eustache","Eus\xE8be","Eus\xE9bie","Eva","Fabien","Fabrice","Falba","Fanny","Fantin","Fantine","Faustine","Ferdinand","Fiacre","Fid\xE8le","Firmin","Flavie","Flavien","Fleur","Flodoard","Flore","Florence","Florent","Florestan","Florian","Florie","Fortun\xE9","Fortun\xE9e","Foulques","France","Francette","Francia","Francine","Francisque","Fran\xE7ois","Fran\xE7oise","Fr\xE9d\xE9ric","Fr\xE9d\xE9rique","Fulbert","Fulcran","Fulgence","F\xE9licie","F\xE9licit\xE9","F\xE9lix","Gabin","Gabriel","Gabrielle","Garance","Garnier","Gaspar","Gaspard","Gaston","Gatien","Gaud","Gautier","Ga\xEBl","Ga\xEBlle","Genevi\xE8ve","Geoffroy","Georges","Georgette","Gerberge","Gerbert","Germain","Germaine","Gertrude","Gervais","Ghislain","Gilbert","Gilles","Girart","Gislebert","Gis\xE8le","Gondebaud","Gonthier","Gontran","Gonzague","Gr\xE9goire","Gueni\xE8vre","Gui","Guilhemine","Guillaume","Guillemette","Gustave","Gustavine","Guy","Guyot","Gu\xE9rin","Gwena\xEBlle","G\xE9d\xE9on","G\xE9rard","G\xE9raud","Hardouin","Hector","Henri","Henriette","Herbert","Herluin","Hermine","Herv\xE9","Hilaire","Hildebert","Hincmar","Hippolyte","Honorine","Honor\xE9","Hortense","Hubert","Hugues","Huguette","H\xE9delin","H\xE9lier","H\xE9lo\xEFse","H\xE9l\xE8ne","Innocent","In\xE8s","Irina","Iris","Ir\xE8ne","Isabeau","Isabelle","Iseult","Isidore","Ism\xE9rie","Jacinthe","Jacqueline","Jacques","Jade","Janine","Japhet","Jason","Jean","Jeanne","Jeanne d\u2019Arc","Jeannel","Jeannot","Jehanne","Joachim","Joanny","Job","Jocelyn","Jocelyne","Johan","Jonas","Jonathan","Joseph","Josse","Josselin","Jos\xE9phine","Jourdain","Jo\xEBl","Jo\xEBlle","Jude","Judica\xEBl","Judith","Jules","Julia","Julie","Julien","Juliette","Juste","Justin","Justine","J\xE9r\xE9mie","J\xE9r\xF4me","Lambert","Landry","Laura","Laurane","Laure","Laureline","Laurence","Laurent","Lauriane","Laurine","Laur\xE8ne","Lazare","Leu","Leufroy","Lib\xE8re","Lionel","Li\xE9tald","Longin","Lorrain","Lorraine","Lothaire","Louis","Loup","Lo\xEFc","Luc","Lucas","Lucie","Lucien","Lucienne","Lucille","Ludivine","Ludolphe","Ludovic","Lydie","L\xE9a","L\xE9andre","L\xE9na","L\xE9on","L\xE9onard","L\xE9onie","L\xE9onne","L\xE9opold","L\xE9opoldine","Macaire","Madeleine","Magali","Maguelone","Mahaut","Mallaury","Malo","Mamert","Manass\xE9","Manon","Marc","Marceau","Marcel","Marcelin","Marceline","Margot","Marguerite","Marianne","Marie","Marine","Marion","Marius","Marl\xE8ne","Marthe","Martial","Martin","Martine","Mathilde","Mathurin","Matthias","Matthieu","Maud","Maugis","Maureen","Maurice","Mauricette","Maxellende","Maxence","Maxime","Maximilien","Mayeul","Melchior","Mence","Merlin","Micha\xEBl","Michel","Mich\xE8le","Mireille","Miriam","Monique","Morgan","Morgane","Mo\xEFse","Mo\xEFsette","Muriel","Myl\xE8ne","M\xE9d\xE9ric","M\xE9gane","M\xE9lanie","M\xE9lisande","M\xE9lissa","M\xE9lissandre","M\xE9lodie","M\xE9rov\xE9e","Nadine","Nad\xE8ge","Narcisse","Nathalie","Nathan","Nathana\xEBl","Naudet","Nestor","Nicolas","Nicole","Nic\xE9phore","Nine","Norbert","Normand","No\xE9","No\xE9mie","No\xEBl","No\xEBlle","N\xE9h\xE9mie","Octave","Oc\xE9ane","Odette","Odile","Odilon","Odon","Oger","Olive","Olivier","Olympe","Ombline","Oph\xE9lie","Oriande","Oriane","Orlane","Oury","Ozanne","Pac\xF4me","Pal\xE9mon","Parfait","Pascal","Pascale","Paterne","Patrice","Paul","Paule","Paulette","Pauline","Perceval","Perrine","Philibert","Philippe","Philippine","Philom\xE8ne","Philoth\xE9e","Phil\xE9mon","Pie","Pierre","Pierrick","Primerose","Priscille","Prosper","Prudence","Pulch\xE9rie","P\xE9cine","P\xE9lagie","P\xE9n\xE9lope","P\xE9pin","P\xE9tronille","Quentin","Quentine","Quintia","Qui\xE9ta","Rachel","Rachid","Raoul","Rapha\xEBl","Rapha\xEBlle","Raymond","Raymonde","Rebecca","Reine","Renaud","Ren\xE9","Ren\xE9e","Reybaud","Richard","Rita","Robert","Roch","Rodolphe","Rodrigue","Roger","Roland","Rolande","Romain","Romane","Romuald","Rom\xE9o","Ronan","Rosalie","Rose","Roselin","Roseline","R\xE9gine","R\xE9gis","R\xE9jean","R\xE9jeanne","R\xE9mi","Sabine","Salomon","Salom\xE9","Samuel","Sandra","Sandrine","Sarah","Sauveur","Savin","Savinien","Scholastique","Serge","Sibylle","Sidoine","Sigebert","Sigismond","Silv\xE8re","Simon","Simone","Sixte","Sixtine","Solange","Soline","Sophie","Stanislas","St\xE9phane","St\xE9phanie","Suzanne","Suzon","Swassane","Sylvain","Sylvestre","Sylviane","Sylvie","S\xE9bastien","S\xE9gol\xE8ne","S\xE9raphin","S\xE9verin","S\xE9verine","Tancr\xE8de","Tanguy","Tatiana","Taurin","Tha\xEFs","Thibault","Thibert","Thierry","Thomas","Th\xE9odora","Th\xE9odore","Th\xE9odose","Th\xE9ophile","Th\xE9ophraste","Th\xE9r\xE8se","Tim","Timol\xE9on","Timoth\xE9e","Tiphaine","Titien","Tonnin","Toussaint","Trajan","Tristan","Turold","Ulysse","Urbain","Ursule","Valentin","Valentine","Val\xE8re","Val\xE9rie","Val\xE9ry","Venance","Venceslas","Vianney","Victoire","Victor","Victorien","Victorin","Vigile","Vincent","Vinciane","Violette","Virginie","Vital","Viviane","Vivien","V\xE9ronique","Waleran","Wandrille","Xavier","Xavi\xE8re","X\xE9nophon","Yoann","Yolande","Ysaline","Yseult","Yves","Yvette","Yvonne","Zacharie","Zach\xE9","Zo\xE9","Zo\xE9va","Z\xE9phirin","\xC8ve","\xC9dith","\xC9douard","\xC9leuth\xE8re","\xC9lia","\xC9liane","\xC9lie","\xC9lisabeth","\xC9lise","\xC9lis\xE9e","\xC9lodie","\xC9lo\xEFse","\xC9lz\xE9ar","\xC9l\xE9onore","\xC9meline","\xC9meric","\xC9mile","\xC9milie","\xC9m\xE9rance","\xC9m\xE9rencie","\xC9piphane","\xC9ric","\xC9tienne","\xC9vang\xE9line","\xC9variste","\xC9velyne","\xC9vrard"],female:["Abdonie","Abeline","Abigaelle","Abiga\xEFl","Acacie","Acanthe","Adalbaude","Adalsinde","Adegrine","Adeline","Adeltrude","Adolphie","Adonise","Adrast\xE9e","Adrehilde","Adrienne","Ad\xE8le","Ad\xE9la\xEFde","Ad\xE9lie","Agathe","Agilberte","Agla\xE9","Agnane","Agnefl\xE8te","Agn\xE8s","Agrippine","Aim\xE9e","Alaine","Ala\xEFs","Albane","Alberte","Alb\xE9rade","Alcidie","Alcine","Alcyone","Aldegonde","Aleth","Alexandrine","Alexanne","Alexine","Alice","Aliette","Aline","Alix","Aliz\xE9","Ali\xE9nor","Aloyse","Alo\xEFse","Alphonsine","Alth\xE9e","Amaliane","Amalth\xE9e","Amande","Amandine","Amante","Amarande","Amaranthe","Amaryllis","Ambre","Ambroisie","Ameline","Aminte","Am\xE9liane","Am\xE9lie","Am\xE9thyste","Anastasie","Anatolie","Ana\xEBlle","Ana\xEFs","Anceline","Andr\xE9e","Angadr\xEAme","Angeline","Angilberte","Ang\xE8le","Ang\xE9lina","Ang\xE9lique","Anicette","Anic\xE9e","Annabelle","Anne","Annette","Annonciade","Ansberte","Anstrudie","Anthelmette","Antigone","Antoinette","Antonine","An\xE9mone","Aph\xE9lie","Apolline","Aquiline","Arabelle","Arcadie","Archange","Argine","Ariane","Aricie","Ariel","Arielle","Arlette","Armance","Armande","Armandine","Armeline","Armelle","Armide","Armine","Arnaude","Arsino\xE9","Ars\xE8nie","Arthurine","Art\xE9mis","Asceline","Ascension","Assomption","Astart\xE9","Astride","Astr\xE9e","Ast\xE9rie","Athalie","Athanasie","Athina","Ath\xE9na\xEFs","Aube","Aubertine","Aude","Audeline","Audrey","Augustine","Aure","Aurelle","Auriane","Aurore","Aur\xE9lie","Aur\xE9lienne","Auxane","Aveline","Aviga\xEBlle","Avoye","Axeline","Axelle","Aymardine","Aymonde","Azal\xE9e","Azeline","Az\xE9lie","Barbe","Basilisse","Bathilde","Bernadette","Berthe","Bertille","Betty","Beuve","Blanche","Blandine","Brigitte","Brunehaut","Brunehilde","B\xE9atrice","B\xE9n\xE9dicte","B\xE9rang\xE8re","Camille","Capucine","Carine","Caroline","Cassandre","Catherine","Chantal","Charlaine","Charline","Charlotte","Chlo\xE9","Christelle","Christiane","Christine","Claire","Clara","Clarisse","Claude","Claudine","Clio","Clotilde","Cl\xE9lie","Cl\xE9mence","Cl\xE9mentine","Coline","Conception","Constance","Coralie","Coraline","Corentine","Corinne","Cyrielle","C\xE9cile","C\xE9leste","C\xE9lestine","C\xE9line","Danielle","Daphn\xE9","Delphine","Denise","Diane","Dieudonn\xE9e","Dominique","Doriane","Dorine","Doroth\xE9e","Douce","D\xE9bora","Edm\xE9e","Ella","Elsa","Emma","Emmanuelle","Emmelie","Ernestine","Estelle","Esther","Eudoxie","Eug\xE9nie","Eulalie","Euphrasie","Eus\xE9bie","Eva","Fanny","Fantine","Faustine","Flavie","Fleur","Flore","Florence","Florie","Fortun\xE9e","France","Francette","Francia","Francine","Fran\xE7oise","Fr\xE9d\xE9rique","F\xE9licie","Gabrielle","Garance","Ga\xEBlle","Genevi\xE8ve","Georgette","Gerberge","Germaine","Gertrude","Gis\xE8le","Gueni\xE8vre","Guilhemine","Guillemette","Gustavine","Gwena\xEBlle","Henriette","Hermine","Hippolyte","Honorine","Hortense","Huguette","H\xE9lo\xEFse","H\xE9l\xE8ne","In\xE8s","Irina","Iris","Ir\xE8ne","Isabeau","Isabelle","Iseult","Ism\xE9rie","Jacinthe","Jacqueline","Jade","Janine","Jeanne","Jeanne d\u2019Arc","Jehanne","Jocelyne","Jos\xE9phine","Jo\xEBlle","Judith","Julia","Julie","Juliette","Justine","Laura","Laurane","Laure","Laureline","Laurence","Lauriane","Laurine","Laur\xE8ne","Lorraine","Lucie","Lucienne","Lucille","Ludivine","Lydie","L\xE9a","L\xE9na","L\xE9onie","L\xE9onne","L\xE9opoldine","Madeleine","Magali","Maguelone","Mahaut","Mallaury","Manon","Marceline","Margot","Marguerite","Marianne","Marie","Marine","Marion","Marl\xE8ne","Marthe","Martine","Mathilde","Maud","Maureen","Mauricette","Maxellende","Maxime","Mich\xE8le","Mireille","Miriam","Monique","Morgane","Mo\xEFsette","Muriel","Myl\xE8ne","M\xE9gane","M\xE9lanie","M\xE9lisande","M\xE9lissa","M\xE9lissandre","M\xE9lodie","Nadine","Nad\xE8ge","Nathalie","Nicole","Nine","No\xE9mie","No\xEBlle","Oc\xE9ane","Odette","Odile","Olive","Olympe","Ombline","Oph\xE9lie","Oriande","Oriane","Orlane","Ozanne","Pascale","Paule","Paulette","Pauline","Perrine","Philippine","Philom\xE8ne","Philoth\xE9e","Primerose","Priscille","Prudence","Pulch\xE9rie","P\xE9cine","P\xE9lagie","P\xE9n\xE9lope","P\xE9tronille","Quentine","Quintia","Qui\xE9ta","Rachel","Rapha\xEBlle","Raymonde","Rebecca","Reine","Ren\xE9e","Rita","Rolande","Romane","Rosalie","Rose","Roseline","R\xE9gine","R\xE9jeanne","Sabine","Salom\xE9","Sandra","Sandrine","Sarah","Scholastique","Sibylle","Simone","Sixtine","Solange","Soline","Sophie","St\xE9phanie","Suzanne","Suzon","Swassane","Sylviane","Sylvie","S\xE9gol\xE8ne","S\xE9verine","Tatiana","Tha\xEFs","Th\xE9odora","Th\xE9r\xE8se","Tiphaine","Ursule","Valentine","Val\xE9rie","Victoire","Vinciane","Violette","Virginie","Viviane","V\xE9ronique","Xavi\xE8re","Yolande","Ysaline","Yseult","Yvette","Yvonne","Zo\xE9","Zo\xE9va","\xC8ve","\xC9dith","\xC9lia","\xC9liane","\xC9lisabeth","\xC9lise","\xC9lodie","\xC9lo\xEFse","\xC9l\xE9onore","\xC9meline","\xC9milie","\xC9m\xE9rance","\xC9m\xE9rencie","\xC9vang\xE9line","\xC9velyne"],male:["Aaron","Abdon","Abel","Abelin","Abondance","Abraham","Absalon","Ab\xE9lard","Acace","Achaire","Achille","Adalard","Adalbald","Adalbert","Adalb\xE9ron","Adalric","Adam","Adegrin","Adel","Adelin","Adelphe","Adenet","Adh\xE9mar","Adjutor","Adolphe","Adonis","Adrien","Ad\xE9odat","Agapet","Agathange","Agathon","Agilbert","Agnan","Agrippin","Aimable","Aim\xE9","Alain","Alban","Albert","Alb\xE9ric","Alcibiade","Alcide","Alcime","Aldonce","Aldric","Aleaume","Alexandre","Alexis","Alix","Alliaume","Almine","Almire","Alo\xEFs","Alphonse","Alph\xE9e","Alpinien","Alver\xE8de","Amalric","Amandin","Amant","Ambroise","Amiel","Amour","Am\xE9d\xE9e","Am\xE9lien","Anastase","Anatole","Ana\xEBl","Ancelin","Andoche","Andr\xE9","And\xE9ol","Ange","Angilbe","Angilran","Angoustan","Anicet","Anne","Annibal","Ansbert","Anselme","Anthelme","Antide","Antoine","Antonin","Apollinaire","Aquilin","Arcade","Archambaud","Archange","Archibald","Arian","Ariel","Ariste","Aristide","Armand","Armel","Armin","Arnaud","Arnould","Arolde","Arsino\xE9","Ars\xE8ne","Arthaud","Arthur","Arth\xE8me","Ascelin","Athanase","Aubry","Audebert","Audouin","Audran","Auguste","Aurian","Aur\xE8le","Auxence","Axel","Aymard","Aymeric","Aymon","Balthazar","Baptiste","Barnab\xE9","Barth\xE9lemy","Bartim\xE9e","Basile","Bastien","Baudouin","Benjamin","Beno\xEEt","Bernard","Bertrand","Blaise","Boh\xE9mond","Bon","Boniface","Bouchard","Briac","Brice","Brieuc","Bruno","B\xE9nigne","B\xE9ranger","B\xE9rard","Calixte","Camille","Camillien","Cam\xE9lien","Candide","Caribert","Carloman","Cassandre","Cassien","Charlemagne","Charles","Childebert","Chilp\xE9ric","Christian","Christodule","Christophe","Chrysole","Chrysostome","Chr\xE9tien","Clarence","Claude","Claudien","Clotaire","Clovis","Cl\xE9andre","Cl\xE9ment","Cl\xE9ry","Colin","Constance","Constant","Constantin","Corentin","Cyprien","Cyriaque","Cyrille","C\xE9dric","C\xE9leste","C\xE9lestin","C\xE9lien","C\xE9saire","C\xE9sar","C\xF4me","Damien","Daniel","David","Delphin","Denis","Didier","Dieudonn\xE9","Dimitri","Dominique","Dorian","D\xE9sir\xE9","Edgard","Edmond","Emmanuel","Enguerrand","Ernest","Eubert","Eudes","Eudoxe","Eug\xE8ne","Eustache","Eus\xE8be","Fabien","Fabrice","Falba","Fantin","Ferdinand","Fiacre","Fid\xE8le","Firmin","Flavien","Flodoard","Florent","Florestan","Florian","Fortun\xE9","Foulques","Francisque","Fran\xE7ois","Fr\xE9d\xE9ric","Fulbert","Fulcran","Fulgence","F\xE9licit\xE9","F\xE9lix","Gabin","Gabriel","Garnier","Gaspar","Gaspard","Gaston","Gatien","Gaud","Gautier","Ga\xEBl","Geoffroy","Georges","Gerbert","Germain","Gervais","Ghislain","Gilbert","Gilles","Girart","Gislebert","Gondebaud","Gonthier","Gontran","Gonzague","Gr\xE9goire","Gui","Guillaume","Gustave","Guy","Guyot","Gu\xE9rin","G\xE9d\xE9on","G\xE9rard","G\xE9raud","Hardouin","Hector","Henri","Herbert","Herluin","Herv\xE9","Hilaire","Hildebert","Hincmar","Hippolyte","Honor\xE9","Hubert","Hugues","H\xE9delin","H\xE9lier","Innocent","Isabeau","Isidore","Jacques","Japhet","Jason","Jean","Jeannel","Jeannot","Joachim","Joanny","Job","Jocelyn","Johan","Jonas","Jonathan","Joseph","Josse","Josselin","Jourdain","Jo\xEBl","Jude","Judica\xEBl","Jules","Julien","Juste","Justin","J\xE9r\xE9mie","J\xE9r\xF4me","Lambert","Landry","Laurent","Lazare","Leu","Leufroy","Lib\xE8re","Lionel","Li\xE9tald","Longin","Lorrain","Lothaire","Louis","Loup","Lo\xEFc","Luc","Lucas","Lucien","Ludolphe","Ludovic","L\xE9andre","L\xE9on","L\xE9onard","L\xE9opold","Macaire","Malo","Mamert","Manass\xE9","Marc","Marceau","Marcel","Marcelin","Marius","Martial","Martin","Mathurin","Matthias","Matthieu","Maugis","Maurice","Maxence","Maxime","Maximilien","Mayeul","Melchior","Mence","Merlin","Micha\xEBl","Michel","Morgan","Mo\xEFse","M\xE9d\xE9ric","M\xE9rov\xE9e","Narcisse","Nathan","Nathana\xEBl","Naudet","Nestor","Nicolas","Nic\xE9phore","Norbert","Normand","No\xE9","No\xEBl","N\xE9h\xE9mie","Octave","Odilon","Odon","Oger","Olivier","Oury","Pac\xF4me","Pal\xE9mon","Parfait","Pascal","Paterne","Patrice","Paul","Perceval","Philibert","Philippe","Philoth\xE9e","Phil\xE9mon","Pie","Pierre","Pierrick","Prosper","P\xE9pin","Quentin","Rachid","Raoul","Rapha\xEBl","Raymond","Renaud","Ren\xE9","Reybaud","Richard","Robert","Roch","Rodolphe","Rodrigue","Roger","Roland","Romain","Romuald","Rom\xE9o","Ronan","Roselin","R\xE9gis","R\xE9jean","R\xE9mi","Salomon","Samuel","Sauveur","Savin","Savinien","Scholastique","Serge","Sidoine","Sigebert","Sigismond","Silv\xE8re","Simon","Sixte","Stanislas","St\xE9phane","Sylvain","Sylvestre","S\xE9bastien","S\xE9raphin","S\xE9verin","Tancr\xE8de","Tanguy","Taurin","Thibault","Thibert","Thierry","Thomas","Th\xE9odore","Th\xE9odose","Th\xE9ophile","Th\xE9ophraste","Tim","Timol\xE9on","Timoth\xE9e","Titien","Tonnin","Toussaint","Trajan","Tristan","Turold","Ulysse","Urbain","Valentin","Val\xE8re","Val\xE9ry","Venance","Venceslas","Vianney","Victor","Victorien","Victorin","Vigile","Vincent","Vital","Vivien","Waleran","Wandrille","Xavier","X\xE9nophon","Yoann","Yves","Zacharie","Zach\xE9","Z\xE9phirin","\xC9douard","\xC9leuth\xE8re","\xC9lie","\xC9lis\xE9e","\xC9lz\xE9ar","\xC9meric","\xC9mile","\xC9piphane","\xC9ric","\xC9tienne","\xC9variste","\xC9vrard"]};var X=["de configuration","de division","de groupe","de la communication","de la cr\xE9ation","de la marque","de la mise en \u0153uvre","de la mobilit\xE9","de la qualit\xE9","de la r\xE9ponse","de la responsabilit\xE9","de la s\xE9curit\xE9","de la tactique","de marque","de paradigme","de programme","de recherche","des applications","des comptes","des directives","des donn\xE9es","des facteurs","des fonctionnalit\xE9s","des infrastructures","des interactions","des march\xE9s","des m\xE9triques","des op\xE9rations","des solutions","du marketing","du web","de l'assurance","de l'identit\xE9","de l'int\xE9gration","de l'intranet","de l'optimisation","de l'utilisabilit\xE9"];var ee=["central","client","direct","futur","humain","international","interne","mondial","national","principal","r\xE9gional"];var re=["{{person.jobType}} {{person.jobArea}} {{person.jobDescriptor}}"];var ae=["Superviseur","Executif","Manager","Ingenieur","Specialiste","Directeur","Coordinateur","Administrateur","Architecte","Analyste","Designer","Technicien","Developpeur","Producteur","Consultant","Assistant","Agent","Stagiaire"];var ie={generic:["Adam","Andre","Arnaud","Aubert","Aubry","Barbier","Baron","Barre","Benoit","Berger","Bernard","Bertrand","Blanc","Blanchard","Bonnet","Bourgeois","Boyer","Breton","Brun","Brunet","Caron","Carpentier","Carre","Charles","Charpentier","Chevalier","Clement","Colin","Collet","Cousin","Da silva","David","Denis","Deschamps","Dubois","Dufour","Dumas","Dumont","Dupont","Dupuis","Dupuy","Durand","Duval","Fabre","Faure","Fernandez","Fleury","Fontaine","Fournier","Francois","Gaillard","Garcia","Garnier","Gauthier","Gautier","Gerard","Girard","Giraud","Gonzalez","Guerin","Guillaume","Guillot","Guyot","Henry","Hubert","Huet","Jacquet","Jean","Joly","Julien","Lacroix","Laine","Lambert","Laurent","Le gall","Le roux","Leclerc","Leclercq","Lecomte","Lefebvre","Lefevre","Legrand","Lemaire","Lemoine","Leroux","Leroy","Lopez","Louis","Lucas","Maillard","Marchal","Marchand","Marie","Martin","Martinez","Marty","Masson","Mathieu","Menard","Mercier","Meunier","Meyer","Michel","Moreau","Morel","Morin","Moulin","Muller","Nguyen","Nicolas","Noel","Olivier","Paris","Paul","Perez","Perrin","Perrot","Petit","Philippe","Picard","Pierre","Poirier","Pons","Prevost","Remy","Renard","Renaud","Renault","Rey","Richard","Riviere","Robert","Robin","Roche","Rodriguez","Roger","Rolland","Rousseau","Roussel","Roux","Roy","Royer","Sanchez","Schmitt","Schneider","Simon","Thomas","Vasseur","Vidal","Vincent"]};var ne={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var oe=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:2},{value:"{{person.firstName}} {{person.lastName}}",weight:8}];var le={generic:["Dr","M","Mlle","Mme","Prof"],female:["Dr","Mlle","Mme","Prof"],male:["Dr","M","Prof"]};var te=["Femme","Homme"];var ze={first_name:$,job_area:X,job_descriptor:ee,job_title_pattern:re,job_type:ae,last_name:ie,last_name_pattern:ne,name:oe,prefix:le,sex:te},ue=ze;var se=["01########","02########","03########","04########","05########","06########","07########","+33 1########","+33 2########","+33 3########","+33 4########","+33 5########","+33 6########","+33 7########"];var de=["+331########","+332########","+333########","+334########","+335########","+336########","+337########"];var ce=["01 ## ## ## ##","02 ## ## ## ##","03 ## ## ## ##","04 ## ## ## ##","05 ## ## ## ##","06 ## ## ## ##","07 ## ## ## ##"];var Ve={human:se,international:de,national:ce},me=Ve;var je={format:me},pe=je;var he=["Biporteur","Cruiser","Cyclo-draisine","Draisienne","Fatbike","Gocycle","Grand bi","Gravel","Longtail","Lowrider bikes","Michaudine","Rickshaw","Rosalie","Singlespeed","Tall bike","Tandem","Tricycle","Tricycle couch\xE9","Triplette","Triporteur","V\xE9lo cargo","V\xE9lo couch\xE9","V\xE9lo de piste","V\xE9lo de route","V\xE9lo en bambou","V\xE9lo fant\xF4me","V\xE9lo festif","V\xE9lo hollandais","V\xE9lo pliant","V\xE9lo tout chemin","V\xE9lo tout-terrain","V\xE9lo \xE0 assistance \xE9lectrique","V\xE9lo \xE0 voile","V\xE9lo \xE9lectrique","V\xE9lo-taxi","V\xE9locar","V\xE9locip\xE8de","V\xE9locip\xE8draisiavaporianna","V\xE9lomobile","Whike"];var ge=["Diesel","Essence","Hybride","\xC9lectrique"];var be=["Berlines","Berlines compactes","Citadines polyvalentes","Grands monospaces","Micro-urbaines","Mini-citadines","Minispaces","Monospaces compacts","Pick-up","SUV","Tout-terrains"];var Je={bicycle_type:he,fuel:ge,type:be},Ae=Je;var Ce=["\xE2cre","adorable","affable","agr\xE9able","aigre","aimable","altruiste","amorphe","antique","apte","avare","bl\xEAme","brave","brusque","calme","candide","charitable","circulaire","consid\xE9rable","coupable","cyan","d\xE9bile","d\xE9lectable","dense","d\xE9sagr\xE9able","dynamique","efficace","\xE9go\xEFste","\xE9m\xE9rite","\xE9nergique","\xE9norme","espi\xE8gle","extatique","extra","fade","ferme","fid\xE8le","fourbe","gai","g\xE9om\xE9trique","gigantesque","habile","hebdomadaire","hirsute","horrible","hypocrite","hyst\xE9rique","immense","incalculable","infime","innombrable","insipide","insolite","intr\xE9pide","jeune","l\xE2che","large","loufoque","lunatique","magenta","magnifique","maigre","malade","marron","mature","m\xE9lancolique","mince","minuscule","moderne","multiple","neutre","novice","orange","pacifique","pauvre","perplexe","placide","pourpre","propre","raide","rapide","rectangulaire","rose","sage","sale","sauvage","s\xE9culaire","s\xE9dentaire","serviable","simple","sinc\xE8re","snob","solitaire","sombre","souple","sp\xE9cialiste","splendide","super","svelte","sympathique","t\xE9m\xE9raire","tendre","terne","timide","tranquille","triangulaire","triste","turquoise","vaste","v\xE9tuste","vide","vivace","vorace"];var ve=["admirablement","ainsi","aussi","bien","comme","comment","debout","doucement","\xE9galement","ensemble","expr\xE8s","franco","gratis","impromptu","incognito","lentement","mal","mieux","pis","plut\xF4t","presque","recta","vite","volontiers","\xE0 peine","\xE0 peu pr\xE8s","absolument","\xE0 demi","assez","autant","autrement","approximativement","beaucoup","carr\xE9ment","combien","compl\xE8tement","davantage","diablement","divinement","dr\xF4lement","encore","enti\xE8rement","environ","extr\xEAmement","fort","grandement","gu\xE8re","infiniment","insuffisamment","joliment","m\xEAme","moins","pas mal","passablement","peu","plus","prou","quasi","quasiment","quelque","rudement","si","suffisamment","tant","tellement","terriblement","totalement","tout","tout \xE0 fait","tr\xE8s","trop","trop peu","un peu","alors","apr\xE8s","apr\xE8s-demain","aujourd'hui","auparavant","aussit\xF4t","autrefois","avant","avant-hier","bient\xF4t","cependant","d'abord","d\xE9j\xE0","demain","depuis","derechef","d\xE9sormais","dor\xE9navant","enfin","ensuite","entre-temps","hier","jadis","jamais","longtemps","lors","maintenant","nagu\xE8re","parfois","premi\xE8rement","puis","quand ?","quelquefois","sit\xF4t","soudain","souvent","subito","tant\xF4t","tard","t\xF4t","toujours","ailleurs","alentour","arri\xE8re","au-del\xE0","au-dessous","au-dessus","au-devant","autour","\xE7a","c\xE9ans","ci","contre","de\xE7\xE0","dedans","dehors","derri\xE8re","dessous","dessus","devant","ici","l\xE0","l\xE0-haut","loin","o\xF9","outre","partout","pr\xE8s","proche","sus","y","apparemment","assur\xE9ment","bon","certainement","certes","en v\xE9rit\xE9","oui","peut-\xEAtre","pr\xE9cis\xE9ment","probablement","sans doute","soit","toutefois","vraiment","vraisemblablement"];var fe=["que","afin que","pour que","de sorte que","de fa\xE7on \xE0 ce que","de mani\xE8re \xE0 ce que","de peur que","de crainte que","puisque","parce que","comme","vu que","\xE9tant donn\xE9 que","du fait que","du moment que","d\u2019autant que","m\xEAme si","quoique","bien que","si","dans la mesure o\xF9","\xE0 condition que","pourvu que","au cas o\xF9","si bien que","de fa\xE7on que","au point que","tant","tellement","assez","trop","avant que","jusqu\u2019\xE0 ce que","lorsque","quand","aussit\xF4t que","sit\xF4t que","d\xE8s que","apr\xE8s que","pendant que","tant que","alors que","tandis que","sans que"];var Be=["ah","a\xEFe","areu areu","atchoum","badaboum","bang","b\xE8","blablabla","bof","boum","broum","bzzz","chut","clac","coac coac","cocorico","coin-coin","cot cot","crac","cro\xE2","cuicui","ding","drelin","dring","euh","glouglou","groin groin","grrr","ha","ha ha","h\xE9","hi","meuh","miam","miaou","oh","ouah","ouch","ouf","ouille","ouin","oups","paf","pff","pin-pon","plic","plouf","prout","pschitt","psitt","ronron","smack","snif","tchou tchouu","tic-tac","toc","toc-toc","tsoin-tsoin","vlan","vouh","vroum","zzzz"];var Pe=["cadre","fonctionnaire","commis de cuisine","adepte","diplomate","camarade","actionnaire","jeune enfant","biathl\xE8te","responsable","chef de cuisine","partenaire","coll\xE8gue","adversaire","guide","commissionnaire","parlementaire","di\xE9t\xE9tiste","gestionnaire","chef","membre du personnel","antagoniste","membre de l\u2019\xE9quipe","sp\xE9cialiste","prestataire de services","juriste","h\xF4te","membre titulaire","membre \xE0 vie","commis","porte-parole","secouriste","athl\xE8te","triathl\xE8te","touriste","administration","conseil d\u2019administration","\xE9quipe de recherche","client\xE8le","concurrence","conseil municipal","d\xE9l\xE9gation","direction","\xE9lectorat","personnel","corps enseignant","\xE9quipe","communaut\xE9 \xE9tudiante","gens","lectorat","mairie","patient\xE8le","police","pr\xE9sidence","personnel professionnel","population du Qu\xE9bec","rectorat","r\xE9daction","secours","foule","main-d\u2019\u0153uvre"];var ye=["a","apr\xE8s","avant","avex","chez","concernant","contre","dans","de","depuis","derri\xE8re","d\xE8s","devant","durant","en","entre","envers","hormis","hors","jusque","malgr\xE9","moyennant","nonobstant","outre","par","parmi","pendant","pour","pr\xE8s","sans","sauf","selon","sous","suivant","sur","touchant","vers","via","\xE0 bas de","\xE0 cause de","\xE0 c\xF4t\xE9 de","\xE0 d\xE9faut de","afin de","\xE0 force de","\xE0 la merci","\xE0 la faveur de","\xE0 l'\xE9gard de","\xE0 l'encontre de","\xE0 l'entour de","\xE0 l'exception de","\xE0 l'instar de","\xE0 l'insu de","\xE0 m\xEAme","\xE0 moins de","\xE0 partir de","\xE0 raison de","\xE0 seule fin de","\xE0 travers","au-dedans de","au d\xE9faut de","au-dehors","au-dessous de","au-dessus de","au lieu de","au moyen de","aupr\xE8s de","aux environs de","au prix de","autour de","aux alentours de","au d\xE9pens de","avant de","d'apr\xE8s","d'avec","de fa\xE7on \xE0","de la part de","de mani\xE8re \xE0","d'entre","de par","de peur de","du c\xF4t\xE9 de","en bas de","en dec\xE0 de","en dedans de","en dehors de","en d\xE9pit de","en face de","en faveur de","en guise de","en outre de","en plus de","gr\xE2ce \xE0","hors de","loin de","lors de","par rapport \xE0","par suite de","pr\xE8s de","proche de","quant \xE0","quitte \xE0","sauf \xE0","sous couleur de","vis-\xE0-vie de"];var Me=["abaisser","abandonner","abattre","abolir","aborder","aboutir","abriter","absorber","abuser","ab\xEEmer","accentuer","accepter","accommoder","accompagner","accomplir","accorder","accrocher","accro\xEEtre","accueillir","accumuler","accuser","acc\xE9der","acc\xE9l\xE9rer","acheter","achever","acqu\xE9rir","adapter","adh\xE9rer","admettre","administrer","admirer","adopter","adresser","affecter","afficher","affirmer","affranchir","affronter","aggraver","agir","agiter","aider","aimer","ajouter","aligner","alimenter","aller","allonger","allumer","amener","amorcer","amuser","am\xE9liorer","am\xE9nager","analyser","animer","annoncer","an\xE9antir","apaiser","apercevoir","appara\xEEtre","appartenir","appeler","applaudir","appliquer","apporter","apprendre","apprivoiser","approcher","approfondir","approuver","appr\xE9cier","appuyer","arracher","arranger","arriver","arr\xEAter","articuler","aspirer","assassiner","asseoir","assigner","assimiler","assister","associer","assumer","assurer","attacher","attaquer","attarder","atteindre","attendre","attendrir","attirer","attraper","attribuer","att\xE9nuer","augmenter","autoriser","avaler","avancer","aventurer","avertir","avoir","avouer","baigner","baiser","baisser","balancer","balayer","barrer","basculer","battre","bavarder","blesser","boire","bondir","boucher","bouffer","bouger","boulanger","bousculer","briller","briser","brouiller","br\xFBler","buter","b\xE2tir","b\xE9n\xE9ficier","b\xFBcher","cacher","calculer","calmer","camper","capter","caract\xE9riser","caresser","casser","causer","cerner","cesser","changer","chanter","charger","chasser","chauffer","chercher","chialer","chier","choir","choisir","circuler","citer","claquer","classer","clocher","cocher","cogner","collaborer","coller","combattre","combiner","combler","commander","commencer","commenter","commettre","communiquer","comparer","compenser","compliquer","compl\xE9ter","comporter","composer","comprendre","compromettre","compter","concentrer","concevoir","concilier","conclure","condamner","conduire","confesser","confier","confirmer","confondre","conformer","confronter","conf\xE9rer","conna\xEEtre","conqu\xE9rir","consacrer","conseiller","consentir","conserver","consid\xE9rer","consoler","consommer","constater","constituer","construire","consulter","contempler","contenir","contenter","contester","continuer","contourner","contraindre","contrarier","contredire","contribuer","contr\xF4ler","convaincre","convenir","convertir","coordonner","copier","corner","correspondre","corriger","coucher","coudre","couler","couper","courir","couvrir","co\xEFncider","co\xFBter","cracher","craindre","craquer","creuser","crever","crier","critiquer","croire","croiser","cro\xEEtre","cr\xE9er","cueillir","cultiver","c\xE9der","c\xE9l\xE9brer","danser","dater","demander","demeurer","descendre","dessiner","devenir","deviner","devoir","diff\xE9rencier","diff\xE9rer","diminuer","dire","diriger","discerner","discuter","dispara\xEEtre","dispenser","disperser","disposer","disputer","dissimuler","dissiper","dissocier","dissoudre","distinguer","distraire","distribuer","diviser","dominer","donner","dormir","doter","doubler","douter","dresser","durer","d\xE9barquer","d\xE9barrasser","d\xE9battre","d\xE9border","d\xE9boucher","d\xE9brouiller","d\xE9celer","d\xE9charger","d\xE9chiffrer","d\xE9chirer","d\xE9cider","d\xE9clarer","d\xE9clencher","d\xE9coller","d\xE9couper","d\xE9courager","d\xE9couvrir","d\xE9crire","d\xE9crocher","d\xE9duire","d\xE9faire","d\xE9fendre","d\xE9fier","d\xE9filer","d\xE9finir","d\xE9gager","d\xE9jeuner","d\xE9livrer","d\xE9marrer","d\xE9montrer","d\xE9noncer","d\xE9passer","d\xE9pendre","d\xE9penser","d\xE9placer","d\xE9ployer","d\xE9poser","d\xE9pouiller","d\xE9ranger","d\xE9river","d\xE9rober","d\xE9rouler","d\xE9shabiller","d\xE9signer","d\xE9sirer","d\xE9tacher","d\xE9tailler","d\xE9tecter","d\xE9tendre","d\xE9terminer","d\xE9tester","d\xE9tourner","d\xE9truire","d\xE9velopper","d\xE9voiler","d\xE9vorer","d\xEEner","effacer","effectuer","effondrer","effrayer","embarquer","embrasser","emmener","emmerder","emplir","employer","emporter","emprunter","emp\xEAcher","encourager","endormir","enfermer","enfiler","enfoncer","engager","engendrer","engloutir","enlever","ennuyer","enregistrer","enrichir","enseigner","entamer","entendre","enterrer","entourer","entra\xEEner","entreprendre","entrer","entretenir","entrevoir","envahir","envelopper","envisager","envoyer","errer","esp\xE9rer","esquisser","essayer","essuyer","estimer","exag\xE9rer","examiner","exciter","exclure","excuser","exc\xE9der","exercer","exiger","exister","expliquer","exploiter","explorer","exploser","exposer","exprimer","exp\xE9dier","ex\xE9cuter","fabriquer","faciliter","faire","falloir","favoriser","feindre","fendre","fermer","ficher","fier","figurer","filer","financer","finir","fixer","flatter","fleurir","flotter","foncer","fonctionner","fonder","fondre","forcer","former","formuler","fouiller","fournir","fourrer","franchir","frapper","frayer","freiner","frotter","fr\xE9mir","fr\xE9quenter","fumer","fusiller","f\xE9liciter","f\xEAter","gagner","garantir","garder","glisser","gonfler","gouverner","go\xFBter","grandir","gratter","gravir","grimper","grossir","grouper","guetter","gueuler","guider","g\xE2cher","g\xE9mir","g\xE9n\xE9raliser","g\xE9rer","g\xEAner","habiller","habiter","habituer","hausser","heurter","hisser","honorer","hurler","h\xE2ter","h\xE9siter","identifier","ignorer","illustrer","imaginer","imiter","imposer","impressionner","imprimer","incarner","inciter","incliner","incorporer","indiquer","influencer","informer","initier","inqui\xE9ter","inscrire","insister","inspecter","inspirer","installer","instituer","instruire","insulter","ins\xE9rer","interdire","interpr\xE9ter","interroger","interrompre","intervenir","introduire","int\xE9grer","int\xE9resser","inventer","inviter","invoquer","isoler","jaillir","jeter","jouer","jouir","juger","jurer","justifier","laisser","lancer","lasser","laver","lever","lib\xE9rer","lier","limiter","liquider","lire","livrer","loger","louer","lutter","l\xE2cher","l\xE9cher","maintenir","manger","manier","manifester","manipuler","marcher","marier","marquer","masquer","mater","ma\xEEtriser","menacer","mener","mentionner","mentir","mesurer","mettre","meubler","modeler","modifier","monter","montrer","moquer","mordre","mouiller","mourir","multiplier","murmurer","m\xE9conna\xEEtre","m\xE9diter","m\xE9langer","m\xE9nager","m\xE9priser","m\xE9riter","m\xEAler","nager","na\xEEtre","nettoyer","nier","nommer","noter","nouer","nourrir","noyer","n\xE9gliger","n\xE9gocier","obliger","observer","obtenir","ob\xE9ir","occuper","officier","offrir","opposer","op\xE9rer","ordonner","organiser","orienter","oser","oublier","pallier","para\xEEtre","parcourir","pardonner","parer","parler","partager","participer","partir","parvenir","passager","passer","pater","payer","peindre","pencher","pendre","penser","percer","percevoir","perdre","perfectionner","permettre","persuader","peser","photographier","piquer","pisser","placer","plaider","plaindre","plaire","plaisanter","plancher","planquer","planter","pleurer","pleuvoir","plier","plonger","pointer","porter","poser","poss\xE9der","pourrir","poursuivre","pourvoir","pousser","pouvoir","pratiquer","prendre","pressentir","presser","prier","priver","proclamer","procurer","proc\xE9der","produire","profiter","progresser","projeter","prolonger","promener","promettre","promouvoir","prononcer","proposer","protester","prot\xE9ger","prouver","provoquer","pr\xE9cipiter","pr\xE9ciser","pr\xE9f\xE9rer","pr\xE9occuper","pr\xE9parer","pr\xE9senter","pr\xE9server","pr\xE9tendre","pr\xE9valoir","pr\xE9venir","pr\xE9voir","pr\xEAter","publier","p\xE9n\xE9trer","p\xE9rir","p\xEAcher","qualifier","quitter","raccrocher","racheter","raconter","rafra\xEEchir","raisonner","ralentir","rallier","ramasser","ramener","ramper","ranger","ranimer","rappeler","rapporter","rapprocher","raser","rassembler","rassurer","rater","rattacher","rattraper","recevoir","rechercher","recommander","recommencer","reconna\xEEtre","reconstituer","reconstruire","recourir","recouvrir","recruter","recueillir","reculer","redescendre","redevenir","redire","redonner","redouter","redresser","refaire","refermer","refroidir","refuser","regagner","regarder","regretter","regrouper","rejeter","rejoindre","relever","relier","relire","remarquer","rembourser","remercier","remettre","remonter","remplacer","remplir","remuer","rem\xE9dier","rena\xEEtre","rencontrer","rendre","renforcer","renier","renoncer","renouveler","renseigner","rentrer","renverser","renvoyer","repara\xEEtre","repartir","repasser","repentir","replacer","reporter","reposer","repousser","reprendre","reprocher","reproduire","repr\xE9senter","rep\xE9rer","respecter","respirer","ressembler","ressentir","ressortir","ressusciter","restaurer","rester","restituer","restreindre","retarder","retenir","retentir","retirer","retomber","retourner","retracer","retrouver","revenir","revivre","revoir","rev\xEAtir","rigoler","rire","risquer","rocher","rompre","ronfler","rouler","ruiner","r\xE9agir","r\xE9aliser","r\xE9chauffer","r\xE9citer","r\xE9clamer","r\xE9concilier","r\xE9cup\xE9rer","r\xE9diger","r\xE9duire","r\xE9fl\xE9chir","r\xE9f\xE9rer","r\xE9gler","r\xE9gner","r\xE9jouir","r\xE9pandre","r\xE9parer","r\xE9partir","r\xE9pondre","r\xE9primer","r\xE9p\xE9ter","r\xE9server","r\xE9signer","r\xE9sister","r\xE9sonner","r\xE9soudre","r\xE9sulter","r\xE9sumer","r\xE9tablir","r\xE9unir","r\xE9ussir","r\xE9veiller","r\xE9v\xE9ler","r\xEAver","r\xF4der","sacrifier","saigner","saluer","satisfaire","sauter","sauvegarder","sauver","savoir","secouer","sembler","semer","sentir","serrer","servir","siffler","signaler","signer","signifier","simplifier","situer","soigner","solliciter","sombrer","songer","sonner","sortir","soucier","souffler","souffrir","souhaiter","soulager","soulever","souligner","soumettre","souper","soup\xE7onner","sourire","soustraire","soutenir","souvenir","subsister","substituer","succ\xE9der","suffire","sugg\xE9rer","suivre","super","supporter","supposer","supprimer","surmonter","surprendre","sursauter","surveiller","survivre","susciter","s\xE9cher","s\xE9duire","s\xE9parer","tailler","taire","taper","tarder","tendre","tenir","tenter","terminer","tirer","tisser","tol\xE9rer","tomber","tordre","toucher","tourner","tousser","tracer","traduire","traiter","trancher","transformer","transmettre","transporter","travailler","traverser","tra\xEEner","trembler","tremper","trier","triompher","tromper","troubler","trouver","tuer","t\xE2cher","t\xE2ter","t\xE9l\xE9phoner","t\xE9moigner","user","vaincre","valoir","vanter","varier","veiller","vendre","venger","venir","verger","verser","vibrer","vider","violer","virer","viser","visiter","vivre","voir","voler","voter","vouloir","voyager","v\xE9rifier","\xE9branler","\xE9carter","\xE9changer","\xE9chapper","\xE9chouer","\xE9claircir","\xE9clairer","\xE9clater","\xE9conomiser","\xE9couter","\xE9craser","\xE9crire","\xE9difier","\xE9garer","\xE9laborer","\xE9largir","\xE9lever","\xE9liminer","\xE9loigner","\xE9merger","\xE9mettre","\xE9mouvoir","\xE9noncer","\xE9num\xE9rer","\xE9panouir","\xE9pargner","\xE9pouser","\xE9prouver","\xE9puiser","\xE9quilibrer","\xE9quiper","\xE9tablir","\xE9taler","\xE9teindre","\xE9tendre","\xE9tonner","\xE9touffer","\xE9tranger","\xE9trangler","\xE9tudier","\xE9vacuer","\xE9valuer","\xE9veiller","\xE9viter","\xE9voluer","\xE9voquer","\xEAtre","\xF4ter"];var Ee={adjective:Ce,adverb:ve,conjunction:fe,interjection:Be,noun:Pe,preposition:ye,verb:Me},Se=Ee;var Oe={animal:h,color:b,commerce:f,company:y,date:q,finance:L,internet:F,location:W,lorem:Y,metadata:U,music:Q,person:ue,phone_number:pe,vehicle:Ae,word:Se},Ei=Oe;export{Ei as a};
