import{a as e}from"./chunk-KERBADJJ.js";import{n as o,o as a}from"./chunk-PC2QB7VM.js";var i=["amerika nigra urso","azia nigra urso","blanka urso","bruna urso","granda pando","longlipa urso","malaja urso","okulvitra urso"];var r=["amerika krokodilo","a\u016Dstralia krokodilo","filipina krokodilo","gavialo","kuba krokodilo","mara krokodilo","mar\u0109a krokodilo","meksika krokodilo","misisipa aligatoro","nana krokodilo","nigra kajmano","nila krokodilo","okcidentafrika krokodilo","orinoka krokodilo","siama krokodilo","\u0109ina aligatoro"];var n=["azia leono","berbera leono","kaba leono","masaja leono","okcidentafrika leono","senegala leono","transvala leono"];var t=["birdo","bovo","cetaco","fi\u015Do","hundo","insekto","kato","krokodilulo","kuniklo","leono","serpento","urso","\u0109evalo"];var Q={bear:i,crocodilia:r,lion:n,type:t},m=Q;var l=["ar\u011Denta","blanka","blua","bruna","cejana","ebura","flava","griza","indiga","kakia","lavenda","lazura","malva","ma\u011Denta","nigra","okra","ora","oran\u011Da","purpura","roza","ru\u011Da","sukcena","turkisa","verda","viola"];var $={human:l},s=$;var u={adjective:["bela","bona","eleganta","elektra","ergonomia","inteligenta","luksa","mala\u0109a","malgranda","manfarita","mirinda","moderna","mojosa","nekredebla","oportuna","ordinara","populara","praktika","rafinita","recikligita","rustika","senmarka","tajlorita","ta\u016Dga","unika"],material:["betona","bronza","ceramika","fre\u015Da","frostigita","granita","kartona","ka\u016D\u0109uka","kotona","ligna","metala","plasta","trikita","vitra","\u015Dtala"],product:["a\u016Dto","biciklo","ekrano","flago","ganto","klavaro","komputilo","mantuko","muso","nom\u015Dildo","pantalono","pilko","sako","sapo","se\u011Do","tablo","valizo","\u0109apelo","\u0109emizo","\u015Duo"]};var oo={product_name:u},b=oo;var d=["grupo","identigilo","kategorio","komento","nomo","pasvorto","profilbildo","stato","telefonnumero","titolo"];var ao={column:d},k=ao;var p={wide:["aprilo","a\u016Dgusto","decembro","februaro","januaro","julio","junio","majo","marto","novembro","oktobro","septembro"],abbr:["apr","a\u016Dg","dec","feb","jan","jul","jun","maj","mar","nov","okt","sep"]};var c={wide:["diman\u0109o","lundo","mardo","merkredo","sabato","vendredo","\u0135a\u016Ddo"],abbr:["di","lu","ma","me","sa","ve","\u0135a"]};var eo={month:p,weekday:c},f=eo;var v=["altkvalita","a\u016Dtomata","bludenta","cifereca","defa\u016Dlta","enreta","helpa","hibrida","interna","malfermitkoda","nesinkrona","ne\u016Drona","nuba","optika","plenekrana","plurbajta","plurplatforma","portebla","realtempa","redunda","sendrata","solidstata","universala","virtuala","\u0109efa"];var g=["analizado","ar\u0125ivado","bitigado","densigado","enigado","generado","indeksado","inter\u015Dan\u011Dado","kalkulado","kodrompado","kodumado","kompilado","konektado","kopiado","muntado","nuligado","preterpasado","programado","restartigado","savkopiado","sinkronigado","sintezado","transpasado","transsendado","validigado"];var y=["alarmo","aplika\u0135o","bendlar\u011Do","buso","cirkvito","datumbazo","datumcentro","disko","dosiero","ekrano","fajro\u015Dirmilo","fluo","interfaco","karto","kondensilo","konektilo","konektingo","konekto","matrico","operaciumo","panelo","pelilo","programo","protokolo","rastrumero","reto","sentilo","servilo","sistemo","transsendilo"];var N=["La {{abbreviation}}-{{noun}} ne funkcias, provu {{verb}} la {{adjective}}n {{noun}}n, por ke ni povu {{verb}} la {{abbreviation}}-{{noun}}n!","Mi provos {{verb}} la {{adjective}}n {{abbreviation}}-{{noun}}n, tio devus {{verb}} la {{abbreviation}}-{{noun}}n!","Ne funkcios {{ingverb}} de la {{noun}}, ni devas {{verb}} la {{adjective}}n {{abbreviation}}-{{noun}}n!","Ne gravas, se ni ne povus {{verb}} la {{noun}}n, ni devas nur {{verb}} la {{adjective}}n {{abbreviation}}-{{noun}}!","Ni devas {{verb}} la {{adjective}}n {{abbreviation}}-{{noun}}n!","Provu {{verb}} la {{abbreviation}}-{{noun}}n, eble ni sukcesos {{verb}} la {{adjective}}n {{noun}}n!","Se ni povus {{verb}} la {{noun}}n, ni povus aliri la {{abbreviation}}-{{noun}}n per la {{adjective}} {{abbreviation}}-{{noun}}!","Uzu la {{adjective}}n {{abbreviation}}-{{noun}}n, poste vi povos {{verb}} la {{adjective}}n {{noun}}n!","Vi ne povas {{verb}} la {{noun}}n sen {{ingverb}} de la {{adjective}} {{abbreviation}}-{{noun}}!"];var j=["analizi","ar\u0125ivi","bitigi","densigi","enigi","generi","indeksi","inter\u015Dan\u011Di","kalkuli","kodrompi","kodumi","kompili","konekti","kopii","munti","nuligi","preterpasi","programi","restartigi","savkopii","sinkronigi","sintezi","transpasi","transsendi","validigi"];var io={adjective:v,ingverb:g,noun:y,phrase:N,verb:j},S=io;var M=["{{location.city_prefix}} {{person.first_name.generic}}{{location.city_suffix}}","{{person.first_name.generic}}{{location.city_suffix}}"];var A=["Norda","Orienta","Suda","Okcidenta","Bona","Nova","Malnova"];var K=["burgo","damo","fildo","forto","fuorto","grado","haveno","porto","stado","\u015Diro","urbo","valo","viko","vilao","vila\u011Do","vilo"];var z=["Afganio","Alando","Albanio","Al\u011Derio","Andoro","Angolo","Angvilo","Antarkto","Antigvo kaj Barbudo","Argentino","Armenio","Arubo","A\u016Dstralio","A\u016Dstrio","Azerbaj\u011Dano","Bahamoj","Banglade\u015Do","Barato","Barbado","Barejno","Belgio","Belizo","Belorusio","Benino","Bermudo","Bocvano","Bolivio","Bosnio kaj Hercegovino","Brazilo","Brita Hindoceana Teritorio","Britaj Virgulinsuloj","Brunejo","Bulgario","Burkino","Burundo","Butano","Buvet-Insulo","\u0108ado","\u0108e\u0125io","Centr-Afrika Respubliko","\u0108ilio","\u0108inio","Danio","Dominika Respubliko","Dominiko","Ebur-Bordo","Egiptio","Ekvadoro","Ekvatora Gvineo","Eritreo","Estonio","Etiopio","Falklandaj Insuloj","Ferooj","Fi\u011Dioj","Filipinoj","Finnlando","Franca Gujano","Franca Polinezio","Francaj Sudaj kaj Antarktaj Teritorioj","Francio","Gabono","Gambio","Ganao","Germanio","\u011Cibraltaro","\u011Cibutio","Grekio","Grenado","Gronlando","Gujano","Gvadelupo","Gvamo","Gvatemalo","Gvernsejo","Gvineo","Gvineo-Bisa\u016Do","Haitio","Herda kaj Makdonaldaj Insuloj","Hispanio","Honduro","Honkongo","Hungario","Indonezio","Irako","Irano","Irlando","Islando","Israelo","Italio","Jamajko","Japanio","Jemeno","\u0134ersejo","Jordanio","Kaboverdo","Kajmaninsuloj","Kambo\u011Do","Kameruno","Kanado","Kariba Nederlando","Kartvelio","Kataro","Kaza\u0125io","Kenjo","Kipro","Kirgizio","Kiribato","Kokosinsuloj","Kolombio","Komoroj","Kongo Brazavila","Kongo Kin\u015Dasa","Kostariko","Kristnaskinsulo","Kroatio","Kubo","Kukinsuloj","Kuracao","Kuvajto","Laoso","Latvio","Lesoto","Libano","Liberio","Libio","Li\u0125ten\u015Dtejno","Litovio","Luksemburgo","Madagaskaro","Majoto","Makao","Malajzio","Malavio","Maldivoj","Malio","Malto","Manksinsulo","Maroko","Mar\u015Dalaj Insuloj","Martiniko","Ma\u016Dricio","Ma\u016Dritanio","Meksiko","Mikronezio","Mjanmao","Moldavio","Monako","Moncerato","Mongolio","Montenegro","Mozambiko","Namibio","Nauro","Nederlando","Nepalo","Ni\u011Derio","Ni\u011Dero","Nikaragvo","Niuo","Nord-Koreio","Nord-Makedonio","Nord-Marianoj","Norfolkinsulo","Norvegio","Nov-Kaledonio","Nov-Zelando","Okcidenta Saharo","Omano","Orienta Timoro","Pakistano","Pala\u016Do","Palestino","Panamo","Papuo-Nov-Gvineo","Paragvajo","Peruo","Pitkarna Insularo","Pollando","Portugalio","Puertoriko","Reunio","Ruando","Rumanio","Rusio","Salomonoj","Salvadoro","Samoo","Sankta Bartolomeo","Sankta Heleno","Sankta Kristoforo kaj Neviso","Sankta Lucio","Sankta Marteno (franca)","Sankta Marteno (nederlanda)","Sankta Piero kaj Mikelono","Sankta Vincento kaj Grenadinoj","Sanmarino","Santomeo kaj Principeo","Sauda Arabio","Sej\u015Deloj","Senegalo","Serbio","Sieraleono","Singapuro","Sirio","Slovakio","Slovenio","Somalio","Srilanko","Sud-Afriko","Sud-Georgio kaj Sud-Sandvi\u0109insuloj","Sud-Koreio","Sud-Sudano","Sudano","Surinamo","Svalbardo kaj Janmajeno","Svazilando","Svedio","Svislando","Ta\u011Dikio","Tajlando","Tajvano","Tanzanio","Togolando","Tokelao","Tongo","Trinidado kaj Tobago","Tunizio","Turkio","Turkmenio","Turkoj kaj Kajkoj","Tuvalo","Ugando","Ukrainio","Unui\u011Dinta Re\u011Dlando","Unui\u011Dintaj Arabaj Emirlandoj","Urugvajo","Usona Samoo","Usonaj Malgrandaj Insuloj","Usonaj Virgulinsuloj","Usono","Uzbekio","Valiso kaj Futuno","Vanuatuo","Vatikano","Venezuelo","Vjetnamio","Zambio","Zimbabvo"];var h={cardinal:["nordo","oriento","sudo","okcidento"],cardinal_abbr:["N","E","S","U"],ordinal:["nordoriento","nordokcidenta","sudoriento","sudokcidento"],ordinal_abbr:["NE","NU","SE","SU"]};var B=["apartamento ###","\u0109ambro ###"];var x={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var _=["{{location.street_prefix}} de {{person.first_name.generic}}","{{location.street_prefix}} de {{person.last_name.generic}}","{{person.first_name.generic}}{{location.street_suffix}}","{{location.city_prefix}} {{person.first_name.generic}}{{location.street_suffix}}"];var L=["Aleo","Arbaro","Avenuo","Bulvardo","Digo","\u011Cardeno","Insulo","Kampo","Klifo","Lago","Monto","Parko","Placo","Ponto","Rivero","Strato","Tunelo","Viadukto","Vojo"];var P=["aleo","arbaro","avenuo","bulvardo","digo","\u011Dardeno","insulo","kampo","klifo","lago","monto","parko","placo","ponto","rivero","strato","tunelo","viadukto","vojo"];var ro={city_pattern:M,city_prefix:A,city_suffix:K,country:z,direction:h,secondary_address:B,street_address:x,street_pattern:_,street_prefix:L,street_suffix:P},D=ro;var no={title:"Esperanto",code:"eo",language:"eo",endonym:"Esperanto",dir:"ltr",script:"Latn"},E=no;var J=["aktivulo","aktoro","aku\u015Disto","amiko","artisto","astronomo","a\u016Dtoro","bakisto","biciklisto","bloganto","\u0109okoladisto","dancisto","dentisto","desegnisto","dezajnisto","dietisto","diplomato","direktoro","edukisto","ekologo","eldonisto","entreprenisto","esperantisto","esploristo","estrarano","farbisto","fervojisto","filmamanto","filmisto","filozofo","fizikisto","fondinto","fotisto","gepatro","gimnasto","ginekologo","gitaristo","GLAT-ulo","gvidanto","\u0125emiisto","\u0125irurgo","idisto","in\u011Deniero","instruisto","inventinto","\u0135urnalisto","kantisto","kasisto","komercisto","komitatano","kreanto","kuiristo","kuracisto","laboristo","leganto","lingvisto","ludanto","manlaboristo","maristo","matematikisto","modelo","muzikisto","nerdo","novulo","oficisto","okulkuracisto","parolanto","patrioto","pensiulo","pentristo","pianisto","poligloto","politikisto","po\u015Dtisto","presisto","prezidanto","programisto","psikologo","revulo","sciencisto","sekretario","senlaborulo","sta\u011Danto","stenografisto","studento","tabloludanto","terapiisto","terkulturisto","trejnisto","urbestro","vartisto","vendisto","verdulo","verkisto","veterano","videobloganto","videoludanto","voja\u011Danto","volapukisto","volontulo"];var G=["{{person.bio_part}}","{{person.bio_part}} {{internet.emoji}}","{{person.bio_part}}, {{person.bio_part}}","{{person.bio_part}}, {{person.bio_part}} {{internet.emoji}}","{{person.bio_part}}, {{person.bio_part}}, {{person.bio_part}}","{{person.bio_part}}, {{person.bio_part}}, {{person.bio_part}} {{internet.emoji}}"];var H={generic:["Abrahamo","Adolfo","Agripino","Albertino","Alcestiso","Aleksandro","Alekso","Alfonsino","Alfredo","Alico","Amalio","Anastazio","Andreo","Angelino","Anno","Antono","An\u011Delo","Arturo","A\u016Dgustino","A\u016Dgu\u0109jo","Bartolomeo","Ba\u016Dcido","Beatrico","Berenico","Bertilo","Berto","Brigito","Bruno","Cecilio","Dagoberto","Danielo","Donaldo","Doroteo","Edgaro","Eduardo","Eleonoro","Elizabeto","Elizo","Emiliano","Emilio","Ernestino","Ernesto","Erne\u0109jo","Evelino","Evo","Fatimo","Ferdinando","Fernando","Filipino","Francisko","Frederiko","Gabrielo","Gastono","Georgo","Gertrudo","Gilberto","Gustavino","Gustavo","Hasano","Hedvigo","Hektoro","Heleno","Henrieto","Hermiono","Huberto","Hugo","Ilzo","Ireno","Ivano","Ivo","Izako","Izoldo","I\u015Dmaelo","Jakelino","Jakobino","Jakobo","Janjo","Jano","Joakimo","Johanino","Johano","Johan\u0109jo","Jonatano","Josuo","Jozefino","Jozefo","Jo\u0109jo","Jo\u015Diko","Judito","Julieto","Juliino","Justeno","Karlo","Karmeno","Karolino","Karolo","Katerino","Klanjo","Klaro","Kla\u016Ddino","Kla\u016Ddo","Klementino","Kleopatro","Klitemnestro","Klotildo","Knuto","Kreuzo","Krimhildo","Kristino","Ksantipo","Lamberto","La\u016Drenco","La\u016Dro","Leono","Leopoldino","Leopoldo","Lilio","Ludovikino","Ludoviko","Luko","Magdaleno","Maksimo","Makso","Malvino","Manjo","Margareto","Marinjo","Mario","Marko","Marteno","Mateo","Miriamo","Mirto","Mi\u0109jo","Moniko","Moseo","Muhamado","Nikolao","Nikol\u0109jo","Noa\u0125o","Olimpio","Oskaro","Osvaldo","Pablo","Patriko","Pa\u016Dlino","Perpetuo","Petro","Rafaelo","Ra\u0125elo","Rebeko","Roberto","Rolando","Rozo","Rudolfo","Sabino","Samuelo","Sebastiano","Simono","Sofinjo","Sofio","Sonjo","Stanislao","Stefanino","Stefano","Susano","Terezo","Tiberio","Tomaso","Ursulo","Veroniko","Viktoro","Vilhelmino","Vilhelmo","Vil\u0109jo","Vladimiro","\u0108arloto","\u0124imeno","\u0134eromo"],female:["Agripino","Albertino","Alcestiso","Alekso","Alfonsino","Alico","Amalio","Anastazio","Angelino","Anno","A\u016Dgustino","Ba\u016Dcido","Beatrico","Berenico","Berto","Brigito","Cecilio","Doroteo","Eleonoro","Elizabeto","Elizo","Emilio","Ernestino","Evelino","Evo","Fatimo","Filipino","Gertrudo","Gustavino","Hedvigo","Heleno","Henrieto","Hermiono","Ilzo","Ireno","Izoldo","Jakelino","Jakobino","Janjo","Johanino","Jozefino","Jo\u015Diko","Judito","Julieto","Juliino","Karmeno","Karolino","Katerino","Klanjo","Klaro","Kla\u016Ddino","Klementino","Kleopatro","Klitemnestro","Klotildo","Kreuzo","Krimhildo","Kristino","Ksantipo","La\u016Dro","Leopoldino","Lilio","Ludovikino","Magdaleno","Malvino","Manjo","Margareto","Marinjo","Mario","Miriamo","Mirto","Moniko","Olimpio","Pa\u016Dlino","Perpetuo","Ra\u0125elo","Rebeko","Rozo","Sabino","Sofinjo","Sofio","Sonjo","Stefanino","Susano","Terezo","Ursulo","Veroniko","Vilhelmino","\u0108arloto","\u0124imeno"],male:["Abrahamo","Adolfo","Aleksandro","Alekso","Alfredo","Andreo","Antono","An\u011Delo","Arturo","A\u016Dgu\u0109jo","Bartolomeo","Bertilo","Bruno","Dagoberto","Danielo","Donaldo","Edgaro","Eduardo","Emiliano","Ernesto","Erne\u0109jo","Ferdinando","Fernando","Francisko","Frederiko","Gabrielo","Gastono","Georgo","Gilberto","Gustavo","Hasano","Hektoro","Huberto","Hugo","Ivano","Ivo","Izako","I\u015Dmaelo","Jakobo","Jano","Joakimo","Johano","Johan\u0109jo","Jonatano","Josuo","Jozefo","Jo\u0109jo","Justeno","Karlo","Karolo","Kla\u016Ddo","Knuto","Lamberto","La\u016Drenco","Leono","Leopoldo","Ludoviko","Luko","Maksimo","Makso","Marko","Marteno","Mateo","Mi\u0109jo","Moseo","Muhamado","Nikolao","Nikol\u0109jo","Noa\u0125o","Oskaro","Osvaldo","Pablo","Patriko","Petro","Rafaelo","Roberto","Rolando","Rudolfo","Samuelo","Sebastiano","Simono","Stanislao","Stefano","Tiberio","Tomaso","Viktoro","Vilhelmo","Vil\u0109jo","Vladimiro","\u0134eromo"]};var T=["androgino","cisgenra virino","cisgenra viro","cisgenrulo","cisulo","cisvirino","cisviro","dugenrulo","duspiritulo","genrokviro","hi\u011Dro","interseksulo","kviro","neduumulo","sengenrulo","transgenra virino","transgenra viro","transgenrulo","transulo","transvirino","transviro","traves\u0109io","trigenrulo","virino","viro"];var R={generic:["Atanasov","Auld","Aymonier","Baghy","Bailey","Bastien","Beauchemin","Becker","Bein","Bennemann","Bicknell","Boirac","Boulton","Bourlet","Bouwes","Bulthuis","Cart","Cederblad","Christaller","Corret","Cseh","Dirksen","Dor","Droogendijk","Elb","Ellis","Enderby","Engholm","Evstifejev","Frenkel","Gasse","Geurts","Grabowski","Grosjean","Hermelin","Hideo","Hodler","Holmes","Huet","Isbr\xFCcker","Junck","Kalocsay","Kotzin","Krijt","Kriss","K\xFChnl","K\xFCrsteiner","Lagrange","Lapenna","Lengyel","Liniger","Lippmann","Luyken","Makkink","Malmgren","Matton","Meyer","Migliorini","Millidge","Milsom","Mirski","Moscheles","Motteau","Mudie","Mybs","Nourmont","No\xEBl","Nyl\xE9n","Pikover","Piron","Pollen","Privat","Renard","Rhodes","Riisberg","Rittenberg","Rossetti","Sabadell","Schafer","Schleyer","Schmid","Schmidt","Schr\xF6der","Schulz","Schwartz","Set\xE4l\xE4","Simon","Sofer","Stamatiadis","Stettler","Sturmer","Thisell","Uitterdijk","Vallienne","Verax","Villanueva","Wackrill","Zaleski","Zamenhof","Zimmermann"]};var I={generic:[{value:"{{person.last_name.generic}}",weight:95},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:5}]};var F=[{value:"{{person.firstName}} {{person.lastName}}",weight:7},{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1}];var V={generic:["d-ino","d-ro","prof.","s-ino","s-ro"],female:["d-ino","d-ro","prof.","s-ino"],male:["d-ro","prof.","s-ro"]};var C=["vira","ina"];var U=["Akvisto","Fi\u015Doj","\u015Cafo","Bovo","\u011Cemeloj","Kankro","Leono","Virgulo","Pesilo","Skorpio","Pafisto","Kaprikorno"];var to={bio_part:J,bio_pattern:G,first_name:H,gender:T,last_name:R,last_name_pattern:I,name:F,prefix:V,sex:C,western_zodiac_sign:U},O=to;var w=[{symbol:"H",name:"hidrogeno",atomicNumber:1},{symbol:"He",name:"heliumo",atomicNumber:2},{symbol:"Li",name:"litio",atomicNumber:3},{symbol:"Be",name:"berilio",atomicNumber:4},{symbol:"B",name:"boro",atomicNumber:5},{symbol:"C",name:"karbono",atomicNumber:6},{symbol:"N",name:"nitrogeno",atomicNumber:7},{symbol:"O",name:"oksigeno",atomicNumber:8},{symbol:"F",name:"fluoro",atomicNumber:9},{symbol:"Ne",name:"neono",atomicNumber:10},{symbol:"Na",name:"natrio",atomicNumber:11},{symbol:"Mg",name:"magnezio",atomicNumber:12},{symbol:"Al",name:"aluminio",atomicNumber:13},{symbol:"Si",name:"silicio",atomicNumber:14},{symbol:"P",name:"fosforo",atomicNumber:15},{symbol:"S",name:"sulfuro",atomicNumber:16},{symbol:"Cl",name:"kloro",atomicNumber:17},{symbol:"Ar",name:"argono",atomicNumber:18},{symbol:"K",name:"kalio",atomicNumber:19},{symbol:"Ca",name:"kalcio",atomicNumber:20},{symbol:"Sc",name:"skandio",atomicNumber:21},{symbol:"Ti",name:"titano",atomicNumber:22},{symbol:"V",name:"vanado",atomicNumber:23},{symbol:"Cr",name:"kromo",atomicNumber:24},{symbol:"Mn",name:"mangano",atomicNumber:25},{symbol:"Fe",name:"fero",atomicNumber:26},{symbol:"Co",name:"kobalto",atomicNumber:27},{symbol:"Ni",name:"nikelo",atomicNumber:28},{symbol:"Cu",name:"kupro",atomicNumber:29},{symbol:"Zn",name:"zinko",atomicNumber:30},{symbol:"Ga",name:"galiumo",atomicNumber:31},{symbol:"Ge",name:"germaniumo",atomicNumber:32},{symbol:"As",name:"arseno",atomicNumber:33},{symbol:"Se",name:"seleno",atomicNumber:34},{symbol:"Br",name:"bromo",atomicNumber:35},{symbol:"Kr",name:"kriptono",atomicNumber:36},{symbol:"Rb",name:"rubidio",atomicNumber:37},{symbol:"Sr",name:"stroncio",atomicNumber:38},{symbol:"Y",name:"itrio",atomicNumber:39},{symbol:"Zr",name:"zirkonio",atomicNumber:40},{symbol:"Nb",name:"niobo",atomicNumber:41},{symbol:"Mo",name:"molibdeno",atomicNumber:42},{symbol:"Tc",name:"teknecio",atomicNumber:43},{symbol:"Ru",name:"rutenio",atomicNumber:44},{symbol:"Rh",name:"rodio",atomicNumber:45},{symbol:"Pd",name:"paladio",atomicNumber:46},{symbol:"Ag",name:"ar\u011Dento",atomicNumber:47},{symbol:"Cd",name:"kadmio",atomicNumber:48},{symbol:"In",name:"indio",atomicNumber:49},{symbol:"Sn",name:"stano",atomicNumber:50},{symbol:"Sb",name:"antimono",atomicNumber:51},{symbol:"Te",name:"teluro",atomicNumber:52},{symbol:"I",name:"jodo",atomicNumber:53},{symbol:"Xe",name:"ksenono",atomicNumber:54},{symbol:"Cs",name:"cezio",atomicNumber:55},{symbol:"Ba",name:"bario",atomicNumber:56},{symbol:"La",name:"lantano",atomicNumber:57},{symbol:"Ce",name:"cerio",atomicNumber:58},{symbol:"Pr",name:"prazeodimo",atomicNumber:59},{symbol:"Nd",name:"neodimo",atomicNumber:60},{symbol:"Pm",name:"prometio",atomicNumber:61},{symbol:"Sm",name:"samario",atomicNumber:62},{symbol:"Eu",name:"e\u016Dropio",atomicNumber:63},{symbol:"Gd",name:"gadolinio",atomicNumber:64},{symbol:"Tb",name:"terbio",atomicNumber:65},{symbol:"Dy",name:"disprozio",atomicNumber:66},{symbol:"Ho",name:"holmio",atomicNumber:67},{symbol:"Er",name:"erbio",atomicNumber:68},{symbol:"Tm",name:"tulio",atomicNumber:69},{symbol:"Yb",name:"iterbio",atomicNumber:70},{symbol:"Lu",name:"lutecio",atomicNumber:71},{symbol:"Hf",name:"hafnio",atomicNumber:72},{symbol:"Ta",name:"tantalo",atomicNumber:73},{symbol:"W",name:"volframo",atomicNumber:74},{symbol:"Re",name:"renio",atomicNumber:75},{symbol:"Os",name:"osmio",atomicNumber:76},{symbol:"Ir",name:"iridio",atomicNumber:77},{symbol:"Pt",name:"plateno",atomicNumber:78},{symbol:"Au",name:"oro",atomicNumber:79},{symbol:"Hg",name:"hidrargo",atomicNumber:80},{symbol:"Tl",name:"talio",atomicNumber:81},{symbol:"Pb",name:"plumbo",atomicNumber:82},{symbol:"Bi",name:"bismuto",atomicNumber:83},{symbol:"Po",name:"polonio",atomicNumber:84},{symbol:"At",name:"astato",atomicNumber:85},{symbol:"Rn",name:"radono",atomicNumber:86},{symbol:"Fr",name:"franciumo",atomicNumber:87},{symbol:"Ra",name:"radiumo",atomicNumber:88},{symbol:"Ac",name:"aktiniumo",atomicNumber:89},{symbol:"Th",name:"torio",atomicNumber:90},{symbol:"Pa",name:"protaktinio",atomicNumber:91},{symbol:"U",name:"uranio",atomicNumber:92},{symbol:"Np",name:"neptunio",atomicNumber:93},{symbol:"Pu",name:"plutonio",atomicNumber:94},{symbol:"Am",name:"americio",atomicNumber:95},{symbol:"Cm",name:"kuriumo",atomicNumber:96},{symbol:"Bk",name:"berkelio",atomicNumber:97},{symbol:"Cf",name:"kaliforniumo",atomicNumber:98},{symbol:"Es",name:"ejn\u015Dtejnio",atomicNumber:99},{symbol:"Fm",name:"fermio",atomicNumber:100},{symbol:"Md",name:"mendelevio",atomicNumber:101},{symbol:"No",name:"nobelio",atomicNumber:102},{symbol:"Lr",name:"la\u016Drencio",atomicNumber:103},{symbol:"Rf",name:"ruterfordio",atomicNumber:104},{symbol:"Db",name:"dubnio",atomicNumber:105},{symbol:"Sg",name:"seborgio",atomicNumber:106},{symbol:"Bh",name:"borio",atomicNumber:107},{symbol:"Hs",name:"hasio",atomicNumber:108},{symbol:"Mt",name:"mejtnerio",atomicNumber:109},{symbol:"Ds",name:"darm\u015Dtatio",atomicNumber:110},{symbol:"Rg",name:"rentgenio",atomicNumber:111},{symbol:"Cn",name:"kopernicio",atomicNumber:112},{symbol:"Nh",name:"nihonio",atomicNumber:113},{symbol:"Fl",name:"flerovio",atomicNumber:114},{symbol:"Mc",name:"moskovio",atomicNumber:115},{symbol:"Lv",name:"livermorio",atomicNumber:116},{symbol:"Ts",name:"teneso",atomicNumber:117},{symbol:"Og",name:"oganesono",atomicNumber:118}];var Z=[{name:"metro",symbol:"m"},{name:"sekundo",symbol:"s"},{name:"molo",symbol:"mol"},{name:"ampero",symbol:"A"},{name:"kelvino",symbol:"K"},{name:"kandelo",symbol:"cd"},{name:"kilogramo",symbol:"kg"},{name:"radiano",symbol:"rad"},{name:"herco",symbol:"Hz"},{name:"ne\u016Dtono",symbol:"N"},{name:"paskalo",symbol:"Pa"},{name:"\u0135ulo",symbol:"J"},{name:"vato",symbol:"W"},{name:"kulombo",symbol:"C"},{name:"volto",symbol:"V"},{name:"omo",symbol:"\u03A9"},{name:"teslo",symbol:"T"},{name:"celsia grado",symbol:"\xB0C"},{name:"lumeno",symbol:"lm"},{name:"bekerelo",symbol:"Bq"},{name:"grajo",symbol:"Gy"},{name:"siverto",symbol:"Sv"},{name:"steradiano",symbol:"sr"},{name:"farado",symbol:"F"},{name:"simenso",symbol:"S"},{name:"vebero",symbol:"Wb"},{name:"henro",symbol:"H"},{name:"lukso",symbol:"lx"},{name:"katalo",symbol:"kat"}];var mo={chemical_element:w,unit:Z},W=mo;var Y=["benzina","dizela","elektra","hibrida"];var lo={fuel:Y},q=lo;var so={animal:m,color:s,commerce:b,database:k,date:f,hacker:S,location:D,metadata:E,person:O,science:W,vehicle:q},X=so;var re=new o({locale:[X,e,a]});export{X as a,re as b};
