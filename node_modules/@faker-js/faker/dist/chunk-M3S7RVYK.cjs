"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkJJD46PNXcjs = require('./chunk-JJD46PNX.cjs');var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var r=["com","com.au","net","net.au","org","org.au"];var D={domain_suffix:r},n=D;var i=["####","###","##"];var l=["Bondi","Burleigh Heads","Carlton","Fitzroy","Fremantle","Glenelg","Manly","Noosa","Stones Corner","St Kilda","Surry Hills","Yarra Valley"];var m=["{{location.city_name}}"];var p=["0###","2###","3###","4###","5###","6###","7###"];var s=["New South Wales","Queensland","Northern Territory","South Australia","Western Australia","Tasmania","Australian Capital Territory","Victoria"];var f=["NSW","QLD","NT","SA","WA","TAS","ACT","VIC"];var u=["Ramsay Street","Bonnie Doon","Cavill Avenue","Queen Street"];var c=["{{location.street_name}}"];var d=["Avenue","Boulevard","Circle","Circuit","Court","Crescent","Crest","Drive","Estate Dr","Grove","Hill","Island","Junction","Knoll","Lane","Loop","Mall","Manor","Meadow","Mews","Parade","Parkway","Pass","Place","Plaza","Ridge","Road","Run","Square","Station St","Street","Summit","Terrace","Track","Trail","View Rd","Way"];var P={building_number:i,city_name:l,city_pattern:m,postcode:p,state:s,state_abbr:f,street_name:u,street_pattern:c,street_suffix:d},y=P;var T={title:"English (Australia Ocker)",code:"en_AU_ocker",country:"AU",language:"en",variant:"ocker",endonym:"English (Australia)",dir:"ltr",script:"Latn"},h=T;var A={female:["Abigail","Alice","Alyssa","Amelia","Annabelle","Audrey","Ava","Ayla","Charlotte","Chelsea","Chloe","Ella","Emily","Emma","Eva","Evelyn","Evie","Georgia","Grace","Hannah","Harper","Hayley","Holly","Imogen","Indiana","Isabella","Isabelle","Isla","Ivy","Jasmine","Layla","Lily","Lucy","Mackenzie","Madeline","Madison","Maya","Mia+3","Mila","Olivia","Pheobe","Poppy","Ruby","Sarah","Scarlett","Sienna","Sofia","Sophie","Zara","Zoe"],male:["Aiden","Alexander","Archer","Archie","Ashton","Benjamin","Blake","Charlie","Connor","Cooper","Elijah","Ethan","Flynn","Harrison","Harry","Hayden","Henry","Hunter","Isaac","Jack","Jackson","Jacob","James","Jayden","Joshua","Kai","Lachlan","Leo","Levi","Liam","Lincoln","Luca","Lucas","Mason","Matt","Max","Mitchell","Nathan","Nigel","Noah","Oliver","Oscar","Riley","Ryan","Samuel","Sean","Sebastian","Thomas","Tom","Tyler","William","Xavier","Zach","Zachery"]};var x={generic:["Anderson","Brown","Connolly","Harris","Jones","Kelly","King","LeQuesne","Lee","Martin","Morton","Nguyen","Rausch","Ridge","Robinson","Ryan","Smith","Taylor","Thomas","Thompson","Walker","White","Williams","Wilson"]};var _={generic:[{value:"{{person.last_name.generic}}",weight:95},{value:"{{person.last_name.generic}}-{{person.last_name.generic}}",weight:5}]};var k={first_name:A,last_name:x,last_name_pattern:_},g=k;var S=["0# #### ####","+61 # #### ####","04## ### ###","+61 4## ### ###"];var b=["+61#########","+614########"];var C=["(0#) #### ####","04## ### ###"];var H={human:S,international:b,national:C},L=H;var N={format:L},M=N;var R={internet:n,location:y,metadata:h,person:g,phone_number:M},v= exports.a =R;var ke=new (0, _chunkZKNYQOPPcjs.n)({locale:[v,_chunkJJD46PNXcjs.a,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = v; exports.b = ke;
