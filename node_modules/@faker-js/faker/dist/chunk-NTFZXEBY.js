import{a as n}from"./chunk-BKUYYLI4.js";import{a as i}from"./chunk-KERBADJJ.js";import{n as a,o as e}from"./chunk-PC2QB7VM.js";var r=["076 ### ## ##","079 ### ## ##","078 ### ## ##","+41 76 ### ## ##","+41 78 ### ## ##","+41 79 ### ## ##","0041 76 ### ## ##","0041 78 ### ## ##","0041 79 ### ## ##"];var j={formats:r},l=j;var o=["biz","ch","com","net"];var s=["bluewin.ch","gmail.com","hotmail.com","outlook.com"];var K={domain_suffix:o,free_email:s},t=K;var u=["####","###","##","#"];var d=["<PERSON><PERSON><PERSON>","<PERSON><PERSON>wi<PERSON>","<PERSON><PERSON><PERSON>","Affoltern am Albis","Aigle","Allschwil","Altdorf","Altst\xE4tten","Amriswil","Appenzell","Arbon","Arlesheim","Arosa","Arth","Baar","Baden","Bassersdorf","Bellinzone","Belp","Berne","Berthoud","Bienne","Binningen","Birsfelden","Brigue-Glis","Brugg","Buchs","Bulle","Bussigny","B\xE2le","B\xFClach","Carouge","Cham","Chiasso","Ch\xEAne-Bougeries","Coire","Crissier","Davos","Del\xE9mont","Dietikon","D\xFCbendorf","Ebikon","Einsiedeln","Emmen","Flawil","Frauenfeld","Freienbach","Fribourg","Gen\xE8ve","Gland","Glaris Centre","Glaris Nord","Gossau","Granges","Herisau","Hinwil","Horgen","Horw","Illnau-Effretikon","Interlaken","Ittigen","Kloten","Kreuzlingen","Kriens","K\xF6niz","K\xFCsnacht","La Chaux-de-Fonds","La Neuveville","La Tour-de-Peilz","Lancy","Langenthal","Lausanne","Le Grand-Saconnex","Le Locle","Lenzbourg","Liestal","Locarno","Lucerne","Lugano","Lyss","Martigny","Meilen","Mendrisio","Meyrin","Monthey","Montreux","Morat","Morges","Moutier","Muri bei Bern","Muttenz","M\xE4nnedorf","M\xF6hlin","M\xFCnchenbuchsee","M\xFCnchenstein","M\xFCnsingen","Neuch\xE2tel","Neuhausen am Rheinfall","Nyon","Oberwil","Oftringen","Olten","Onex","Opfikon","Ostermundigen","Payerne","Peseux","Pf\xE4ffikon","Plan-les-Ouates","Poliez-Pittet","Porrentruy","Pratteln","Prilly","Pully","Rapperswil-Jona","Regensdorf","Reinach","Renens","Rheinfelden","Richterswil","Riehen","Risch-Rotkreuz","Romanshorn","Rorschach","R\xFCti","Saint-Gall","Saint-Moritz","Sarnen","Schaffhouse","Schlieren","Schwytz","Sierre","Sion","Soleure","Spiez","Spreitenbach","Stans","Steffisburg","Steinhausen","St\xE4fa","Suhr","Sursee","Thalwil","Thoune","Th\xF4nex","Urdorf","Uster","Uzwil","Vernier","Versoix","Vevey","Veyrier","Villars-sur-Gl\xE2ne","Vi\xE8ge","Volketswil","Wallisellen","Weinfelden","Wettingen","Wetzikon","Wil","Winterthour","Wohlen","Worb","W\xE4denswil","Yverdon-les-Bains","Zermatt","Zofingue","Zollikofen","Zollikon","Zoug","Zuchwil","Zurich","\xC9cublens"];var m=["{{location.city_name}}"];var c=["Afghanistan","Albanie","Alg\xE9rie","Andorre","Angola","Antigua-et-Barbuda","Argentine","Arm\xE9nie","Australie","Autriche","Azerba\xEFdjan","Bahamas","Bahre\xEFn","Bangladesh","Barbade","Bi\xE9lorussie","Belgique","Belize","B\xE9nin","Bhoutan","Bolivie","Bosnie-Herz\xE9govine","Botswana","Br\xE9sil","Brunei","Bulgarie","Burkina Faso","Burundi","Cambodge","Cameroun","Canada","Cap-Vert","R\xE9publique centrafricaine","Tchad","Chili","Chine","Colombie","Comores","Costa Rica","C\xF4te d\u2019Ivoire","Croatie","Cuba","Chypre","R\xE9publique tch\xE8que","R\xE9publique d\xE9mocratique du Congo","Danemark","Djibouti","Dominique","R\xE9publique dominicaine","Timor oriental","\xC9quateur","\xC9gypte","Salvador","Guin\xE9e \xE9quatoriale","\xC9rythr\xE9e","Estonie","Eswatini","\xC9thiopie","Fidji","Finlande","France","Gabon","Gambie","G\xE9orgie","Allemagne","Ghana","Gr\xE8ce","Grenade","Guatemala","Guin\xE9e","Guin\xE9e-Bissau","Guyane","Ha\xEFti","Honduras","Hongrie","Islande","Inde","Indon\xE9sie","Iran","Irak","Irlande","Isra\xEBl","Italie","Jama\xEFque","Japon","Jordan","Kazakhstan","Kenya","Kiribati","Kowe\xEFt","Kirghizistan","Laos","Lettonie","Liban","Lesotho","Liberia","Libye","Liechtenstein","Lituanie","Luxembourg","Madagascar","Malawi","Malaisie","Maldives","Mali","Malte","\xCEles Marshall","Mauritanie","Maurice","Mexique","Micron\xE9sie","Moldavie","Monaco","Mongolie","Mont\xE9n\xE9gro","Maroc","Mozambique","Birmanie","Namibie","Nauru","N\xE9pal","Pays-Bas","Nouvelle-Z\xE9lande","Nicaragua","Niger","Nigeria","Cor\xE9e du Nord","Norv\xE8ge","Oman","Pakistan","Palau","Panama","Papouasie-Nouvelle-Guin\xE9e","Paraguay","P\xE9rou","Philippines","Pologne","Portugal","Qatar","R\xE9publique du Congo","Mac\xE9doine","Roumanie","Russie","Rwanda","Saint-Christophe-et-Ni\xE9v\xE8s","Sainte-Lucie","Saint-Vincent-et-les-Grenadines","Samoa","Saint-Marin","S\xE3o Tom\xE9-et-Principe","Arabie saoudite","S\xE9n\xE9gal","Serbie","Seychelles","Sierra Leone","Singapour","Slovaquie","Slov\xE9nie","\xCEles Salomon","Somalie","Afrique du Sud","Cor\xE9e du Sud","Soudan du Sud","Espagne","Sri Lanka","Soudan","Suriname","Su\xE8de","Suisse","Syrie","Tadjikistan","Tanzanie","Tha\xEFlande","Togo","Tonga","Trinit\xE9-et-Tobago","Tunisie","Turquie","Turkm\xE9nistan","Tuvalu","Ouganda","Ukraine","\xC9mirats arabes unis","Royaume-Uni","\xC9tats-Unis","Uruguay","Ouzb\xE9kistan","Vanuatu","Venezuela","Vi\xEAt Nam","Y\xE9men","Zambie","Zimbabwe"];var h=[{alpha2:"CH",alpha3:"CHE",numeric:"756"}];var A={cardinal:["Nord","Est","Sud","Ouest"],cardinal_abbr:["N","E","S","O"],ordinal:["Nord-est","Nord-ouest","Sud-est","Sud-ouest"],ordinal_abbr:["NE","NO","SE","SO"]};var L=["1###","2###","3###","4###","5###","6###","7###","8###","9###"];var M=["Apt. ###","# \xE9tage"];var p=["Argovie","Appenzell Rhodes-Int\xE9rieures","Appenzell Rhodes-Ext\xE9rieures","B\xE2le-Ville","B\xE2le-Campagne","Berne","Fribourg","Gen\xE8ve","Glaris","Grisons","Jura","Lucerne","Neuch\xE2tel","Nidwald","Obwald","Schaffhouse","Schwyt","Soleure","Saint-Gall","Thurgovie","Tessin","Uri","Valai","Vaud","Zoug","Zurich"];var y=["AG","AI","AR","BE","BL","BS","FR","GE","GL","GR","JU","LU","NE","NW","OW","SG","SH","SO","SZ","TG","TI","UR","VD","VS","ZG","ZH"];var g={normal:"{{location.buildingNumber}} {{location.street}}",full:"{{location.buildingNumber}} {{location.street}} {{location.secondaryAddress}}"};var C=["{{location.street_prefix}} {{location.street_suffix}}"];var S=["Rue","Avenue","Place","Route","Chemin"];var f=["Agassiz","Aim\xE9 Charpilloz","Albert-Gobat","Ancienne Route Romaine","aux Brebis","Baptiste Savoye","Basse","Beauregard","Beausite","Berg","Berthold Vuilleumier","Blanc","cantonale","Centrale","Champ Meusel","Champs Bruants","Champs de la Pelle","Champs Saliers","Chantemerle","Charles Sch\xE4ublin","Chautenatte","Combe-Gr\xE8de","d'Amour","d'Evilard","d'Orvin","de Beau-Site","de Beauregard","de Beausite","de Bel-Air","de Belfond","de Berne","de Beuchemaitin","de Bienne","de Blanche-Terre","de Blanchet","de Bonn\xE9","de Boron","de Bretin","de Brevoi","de Chali\xE8re","de Chaluet","de Champ Villiers","de Champ-Fleuri","de Champoz","de Chasseral","de Ch\xE2tillon","de Chavannes","de Chuffort","de Citroz","de Combatte","de Courtelary","de Diesse","de Ferreule","de Frinvillier","de Froideval","de Graitery","de Jeaurat","de l'Aiguillon","de l'Alouette","de l'Arsenal","de l'Aurore","de l'Avenir","de l'Eau des Fontaines","de l'Ecluse","de l'Ecole","de l'Ecole Primaire","de l'Eglise","de l'Endroit","de l'Enfer","de l'Envers","de l'Erguel","de l'Essor","de l'Est","de l'H\xF4pital","de l'H\xF4tel-de-Ville","de l'Industrie","de l'Or\xE9e","de l'Orge","de l'Orgerie","de l'Ouest","de la Baume","de la Bergerie","de la Blanche-Eglise","de la Borcairde","de la Bosse","de la Brigade","de la Calle","de la Carri\xE8re","de la Cascade","de la Cerni\xE8re","de la Chapelle","de la Chavonne","de la Ciblerie","de la Citadelle","de la Cit\xE9","de la Citerne","de la Clef","de la Colline","de la Combe","de la Combe-Aubert","de la Condemine","de la C\xF4te","de la Courtine","de la Creuse","de la Croix","de la Cure","de la Dout","de la Doux","de la Douzette","de la Fenatte","de la Fenette","de la Fin","de la Fiole","de la Fontaine","de la Forge","de la Foule","de la Fourchaux","de la Gare","de la Golatte","de la Halle","de la Halte","de la Joux","de la Maison de Ville","de la Maison-Blanche","de la Malade","de la Malathe","de la Marchande","de la Nancoran","de la Nant","de la Neigette","de la Neuve Charri\xE8re","de la Paix","de la Peluse","de la Petite Fin","de la Petite-Fin","de la Piscine","de la Place","de la Plage","de la Poste","de la Poya","de la Prairie","de la Praye","de la Pr\xE9fecture","de la Pr\xE9v\xF4t\xE9","de la Printani\xE8re","de la Promenade","de la Pron","de la Raisse","de la R\xE9cille","de la Reuchenette","de la Rive","de la Rochalle","de la Roche au Cros","de la Rochette","de la Rouette","de la Sagnette","de la Scierie","de la Seignette","de la Serre","de la Soci\xE9t\xE9","de la Suze","de la Tour","de la Trame","de la Valle","de la Vanne","de la Versanne","de la vieille Charri\xE8re","de la Vignette","de La vy de\u017Ftra\xFFe","de Lamboing","de Lausanne","de Lausanne \xE0 Daillens","de Ligni\xE8res","de Loville","de Malvaux","de Maupras","de Maupras du Haut","de M\xE9vilier","de Montoz","de Morges","de Moron","de Neuch\xE2tel","de Nods","de P\xE2quier","de Penthaz \xE0 Bournens","de Penthaz \xE0 Cossonay","de Penthaz \xE0 Daillens","de Penthaz \xE0 Gollion","de Penthaz \xE0 Penthalaz","de Penthaz \xE0 Sullens","de Pierre Grise","de Pierre-Pertuis","de Plein Vent","de Pontenet","de Poudeille","de Prapion","de Pr\xEAles","de Reconvilier","de Reuchenette","de Rondans","de Rouge-Terre","de Ruege","de Ruveau","de Sassagne","de Saules","de Savaronne","de Sebastopol","de S\xE9baux","de S\xE9beillon","de Soleure","de Sonceboz","de Sonrougeux","de Sonvilier","de St-Joux","de Sur-Fr\xEAte","de Tavannes","de Tivoli","de Tombain","de Tramelan","de Vallon","de Vauffelin","de Vervas","de Vigneule","de Villeret","Derri\xE8re Mahl\xE9s","des Ages","des Alouettes","des Artisans","des Aub\xE9pines","des Auges","des Aulnes","des Bains","des Biches","des Biolas","des Blanchards","des Bl\xE9s","des Bleuets","des Bosquets","des Bouleaux","des Bourdons","des Bourgognons","des Britani\xE8res","des Brues","des Carrons","des Celliers","des Cerisiers","des Champs","des Charbonni\xE8res","des Chardonnerets","des Charmilles","des Chavannes","des Ch\xEAnes","des Chenevi\xE8res","des Chevaux","des Cibles","des Clos","des Coillards","des Coll\xE8ges","des Combattes","des Combes","des Convers","des C\xF4tes Bugnot","des Deutes","des Dolaises","des Eaux","des Ecommunes","des Ecureuils","des Eglantines","des Epinettes","des Etoblons","des Faulx","des Fauvettes","des Fleurs","des Fontenettes","des Foss\xE9s","des fourches","des Fra\xEEches","des Fran\xE7ais","des Gentianes","des Golats","des Gorges","des Granges","des Graviers","des Gretions","des grilles","des Grillons","des Huit-Journaux","des Isles","des Italiens","des Jardinets","des Jardins","des Jonch\xE8res","des Jonquilles","des Levri\xE8res","des Lilas","des Longines","des Lorettes","des Lovi\xE8res","des Malterres","des Marnins","des Martinets","des M\xE9l\xE8zes","des M\xE9sanges","des Mines","des Mornets","des Mulets","des M\xFBriers","des Navaux","des Nazieux","des Noisetiers","des Nouettes","des Noy\xE8res","des Noyes","des Oeuches","des Oeuchettes","des Oeuvri\xE9s","des Otaux","des P\xE2turages","des P\xEAcheurs","des P\xE9lerins","des Pendants","des Peupliers","des Pins Gras","des Pinsons","des Planchers","des Planches","des Pl\xE2nes","des Plantes","des Platanes","des Pommiers","des Pontins","des Pr\xE9jures","des Pr\xE9s","des Pr\xE9s Bernard","des Pr\xE9s Joyeux","des pr\xE9s Vaillons","des Pr\xE9s Vaillons","des Pr\xE9s-Gu\xEBtins","des Primev\xE8res","des Puits","des ravi\xE8res","des Rives","des Rocailles","des Roches","des Rochettes","des Roses","des Rottes","des Ruaux","des Saigneules","des Sapins","des Sauges","des Soci\xE9t\xE9s","des Sorbiers","des Sources","des Tilles","des Tilleuls","des Tsch\xE8tres","des Vergers","des Vieilles Mortes","des Vignolans","des Voitats","de\u017Foubz","dit le Petit pr\xE9","Doyen Morel","du 16 Mars","du 26-Mars","du Bambois","du Brue","du Bruye","du Cagreu","du Cairli","du Chalet","du Chasseral","du Ch\xE2teau","du Ch\xE2tillon","du Chaumin","du Ch\xEAne","du Chevreuil","du Cin\xE9ma","du Clos","du Clos Michel","du Coin Dessus","du Coll\xE8ge","du Compois","du Coq","du Corps-de-Garde","du Coteau","du Cratat","du Cr\xEAt","du Cr\xEAt-Georges","du Docteur Schwab","du Dr. Eguet","du Dr.Ti\xE8che","du Droit","du Faubourg","du Faucon","du fournel","du Foyer","du Fr\xEAte","du Fuet","du G\xE9n\xE9ral Voirol","du Geni\xE8vre","du Grand-Champ","du Grand'Clos","du Haut","du Haut des Roches","du haut ou de la c\xF4te","du Jura","du Lac","du Lac Vert","du Lavoir","du Levant","du long Jorat autrement de la Cavoye","du Mamelon Vert","du Marais","du March\xE9","du Midi","du Milieu","du Monnet","du Mont","du Moulin","du Nord","du Nouveau Pont","du Paradis","du Parc","du Pasteur Fr\xEAne","du P\xE2turage","du Pensionnat","du Perset","du Petit-B\xE2le","du Petit-Val","du Pierrat","du Plan","du Pomm\xE9 au Loup","du Pont","du Port","du Prailat","du Pr\xE9","du Pr\xE9 Bayard","du Puits","du Quai","du Quart Derri\xE8re","du Raisin","du R\xEAche","du Repos","du Righi","du Roc","du Ruaul du puble","du Ryaulx","du Sc\xE9","du Sel","du Signal","du Signolet","du Soleil","du Stade","du Stand","du Temp\xE9","du Temple","du Tennis","du Tilleul","du Tirage","du Torrent","du Truit","du Tunnel","du V\xE9l\xE9","du Vergeret","du Viaduc","du Vignoble","du Village","du wie Ruaul","Edouard Ti\xE8che","Emile Villeneuve","en haut le Cret","Euchette","Ferdinand-Gonseth","Fin de l\xE0 Outre","Fin-L\xE9pine","Francillon","Fritz-Marchand","Ginnel","Grock","H.-F.Sandoz","Haute","Industrielle","Jacques David","Jacques-Ren\xE9 Fiechter","Jolimont","l'Or\xE9e du Bois","le long du Cret","Marzon","Menin","Mercier","M\xE9val","Mol","Montagu","Neuf","Neuve","Nicolas-Junker","Paul Charmillot","Pierre Jolissaint","Pierre-Alin","Plaine Fin","principale","Principale","Quart-Dessus","Ri\xE8re Ville","Robert de Vigier","Rondelle","Rosselet-Challandes","Saint-Germain","Saint-Randoald","Samuel d'Aubign\xE9","Sandoz","Sans-Souci","Sauvain","sous la C\xF4te","sous la Lampe","Sous Route","Sous-la-For\xEAt","sur Beaumont","tendant \xE0 Bioley","vers Dizy","vers La Chaux","vers La Sarraz","vers Senarclens","Virgile-Rossel"];var q={building_number:u,city_name:d,city_pattern:m,country:c,country_code:h,direction:A,postcode:L,secondary_address:M,state:p,state_abbr:y,street_address:g,street_pattern:C,street_prefix:S,street_suffix:f},b=q;var E=["alias","consequatur","aut","perferendis","sit","voluptatem","accusantium","doloremque","aperiam","eaque","ipsa","quae","ab","illo","inventore","veritatis","et","quasi","architecto","beatae","vitae","dicta","sunt","explicabo","aspernatur","odit","fugit","sed","quia","consequuntur","magni","dolores","eos","qui","ratione","sequi","nesciunt","neque","dolorem","ipsum","dolor","amet","consectetur","adipisci","velit","non","numquam","eius","modi","tempora","incidunt","ut","labore","dolore","magnam","aliquam","quaerat","enim","ad","minima","veniam","quis","nostrum","exercitationem","ullam","corporis","nemo","ipsam","voluptas","suscipit","laboriosam","nisi","aliquid","ex","ea","commodi","autem","vel","eum","iure","reprehenderit","in","voluptate","esse","quam","nihil","molestiae","iusto","odio","dignissimos","ducimus","blanditiis","praesentium","laudantium","totam","rem","voluptatum","deleniti","atque","corrupti","quos","quas","molestias","excepturi","sint","occaecati","cupiditate","provident","perspiciatis","unde","omnis","iste","natus","error","similique","culpa","officia","deserunt","mollitia","animi","id","est","laborum","dolorum","fuga","harum","quidem","rerum","facilis","expedita","distinctio","nam","libero","tempore","cum","soluta","nobis","eligendi","optio","cumque","impedit","quo","porro","quisquam","minus","quod","maxime","placeat","facere","possimus","assumenda","repellendus","temporibus","quibusdam","illum","fugiat","nulla","pariatur","at","vero","accusamus","officiis","debitis","necessitatibus","saepe","eveniet","voluptates","repudiandae","recusandae","itaque","earum","hic","tenetur","a","sapiente","delectus","reiciendis","voluptatibus","maiores","doloribus","asperiores","repellat"];var O={word:E},v=O;var w={title:"French (Switzerland)",code:"fr_CH",country:"CH",language:"fr",endonym:"Fran\xE7ais (Suisse)",dir:"ltr",script:"Latn"},N=w;var R={generic:["Aaliyah","Aaron","Abdullah","Abigail","Ada","Adam","Adea","Adele","Adina","Adrian","Adriana","Agatha","Ahmed","Aida","Aiden","Aina","Aisha","Ajan","Ajana","Ajla","Ajlin","Ajna","Alan","Alana","Alara","Alba","Albert","Alea","Alec","Alejandro","Aleksa","Aleksander","Aleksandra","Alena","Alenia","Alessandro","Alessia","Alessio","Alex","Alexander","Alexandra","Alexandre","Alexandros","Alexia","Alexis","Aleya","Aleyna","Alfred","Ali","Alia","Alice","Alicia","Alina","Aline","Alisa","Alisha","Alissa","Alissia","Alix","Aliya","Aliyah","Alma","Alva","Alya","Alyssa","Alyssia","Amadea","Amalia","Amanda","Amar","Amara","Amber","Ambra","Amea","Amelia","Amelie","Amen","Amin","Amina","Amir","Amira","Amra","Amy","Am\xE1lia","Am\xE9lia","Am\xE9lie","Ana","Anahita","Anastasia","Anastasija","Ana\xEFs","Andjelina","Andrea","Andrej","Andri","Andrin","Andrina","Angela","Angelina","Angelo","Anik","Anika","Anina","Anna","Annabel","Annika","Anouk","Anthony","Anton","Antonia","Antonio","Anuar","Aren","Ari","Aria","Arian","Ariana","Arianna","Arielle","Arion","Aris","Arisa","Armando","Armin","Arno","Aron","Arthur","Arun","Arya","Asia","Asja","Astrid","Asya","Atlas","Aurel","Aurelia","Aurelio","Auron","Aurora","Ava","Axel","Ayan","Ayana","Ayaz","Ayden","Ayla","Aylin","Azra","Beatrice","Bela","Bella","Ben","Benjamin","Bianca","Bigna","Bj\xF6rn","Bogdan","Bruno","Bryan","B\xE9la","Camilla","Can","Carl","Carla","Carlo","Carlota","Carlotta","Carolina","Cataleya","Cecilia","Cedric","Celina","Celine","Charles","Charlie","Charlotte","Chiara","Chloe","Chlo\xE9","Christian","Christopher","Claire","Clara","Clea","Clemens","Cleo","Colin","Constantin","Cristian","C\xE9dric","C\xE9leste","C\xE9line","Dahlia","Dalia","Damian","Daniel","Danilo","Dante","Daria","Darian","Dario","Daris","Darius","David","Davide","Dea","Dean","Deborah","Delia","Denis","Deniz","Devin","Diana","Diar","Diara","Diego","Dina","Dino","Dion","Dominik","Dorian","Dua","Dylan","Eda","Eddie","Eduard","Eduardo","Ela","Elea","Eleanor","Elena","Eleni","Eleonora","Eli","Elia","Eliah","Elian","Eliana","Eliano","Elias","Elif","Elija","Elijah","Elin","Elina","Eline","Elio","Elion","Eliona","Elisa","Elisabeth","Eliza","Ella","Ellen","Elli","Ellie","Elliot","Elodie","Elsa","Elyas","Ema","Emanuel","Emelie","Emil","Emilia","Emilian","Emilie","Emilija","Emilio","Emily","Emir","Emma","Ena","Enea","Enes","Enio","Ennio","Ensar","Enya","Enyo","Enzo","Eric","Erik","Eron","Esra","Estelle","Esther","Ethan","Eva","Evan","Fabian","Fabio","Federico","Felicia","Felix","Ferdinand","Filip","Filippa","Filippo","Finja","Finn","Fiona","Fionn","Flavia","Flavio","Florence","Florian","Flurin","Flurina","Flynn","Francesco","Francis","Francisco","Frederick","Frederik","Freya","Frida","Fynn","Gabriel","Gabriele","Gelila","Georg","Giada","Gian","Gianluca","Gianna","Gino","Gioele","Gioia","Gion","Giorgia","Giulia","Giuliano","Giulio","Giuseppe","Grace","Hailey","Hamza","Hana","Hanna","Hannah","Hava","Helen","Helena","Helin","Henri","Henrik","Henry","Hira","Hugo","Ian","Ida","Ignacy","Ilai","Ilaria","Ilay","Ilenia","Ilian","Iliana","Ilias","Ilja","Ilyas","Imran","Ina","Inara","Irina","Iris","Isa","Isaac","Isabel","Isabella","Isabelle","Isaiah","Iva","Ivan","Ivy","Jack","Jaden","Jael","Jakob","Jakub","Jamal","James","Jamie","Jamiro","Jan","Jana","Janina","Janis","Jannik","Jannis","Janosch","Jara","Jari","Jaro","Jaron","Jasin","Jasmin","Jasmine","Jason","Jay","Jayden","Jennifer","Jeremy","Jil","Joah","Joana","Joel","Johanna","John","Joline","Jon","Jona","Jonah","Jonas","Jonathan","Jorin","Joris","Josephine","Joshua","Jovan","Jovin","Joy","Juan","Jules","Julia","Julian","Juliana","Julie","Julien","Juliette","Julius","Juna","Juri","Jusra","Jusuf","Kai","Kaia","Kaja","Kalea","Karl","Kaya","Keano","Keanu","Kerem","Keyan","Kian","Kiano","Kiara","Kilian","Kimo","Kira","Kiyan","Klara","Klea","Konstantin","Kristijan","Kuzey","Kyan","Ladina","Laia","Laila","Lana","Lara","Larina","Larissa","Lars","Lasse","Laura","Lauri","Laurin","Lavinia","Layla","Lea","Leah","Lean","Leana","Leandra","Leandro","Leano","Leart","Ledion","Leia","Leila","Len","Lena","Leni","Lenia","Lenn","Lenni","Lennox","Lenny","Lenya","Leo","Leon","Leona","Leonard","Leonardo","Leonel","Leoni","Leonidas","Leonie","Leonis","Leonora","Leopold","Leroy","Letizia","Levi","Levin","Levio","Leya","Leyan","Leyla","Lia","Liam","Lian","Liana","Liara","Lias","Lilia","Lilian","Liliana","Lilly","Lily","Lina","Linda","Linn","Linnea","Lino","Linus","Lio","Lion","Lionel","Lior","Liron","Lisa","Liv","Liva","Livia","Livio","Liya","Liyan","Liyana","Lola","Lorena","Lorenz","Lorenzo","Lorian","Lorik","Lorin","Loris","Lotta","Lou","Louan","Louie","Louis","Louisa","Louise","Lo\xEFc","Lua","Luan","Luana","Luc","Luca","Lucas","Lucia","Lucy","Luena","Lui","Luis","Luisa","Luise","Luka","Lukas","Luke","Luna","Lya","Lyan","Lynn","L\xE9a","L\xE9o","L\xE9onie","Madlaina","Mael","Mahir","Maia","Maila","Mailo","Maira","Maja","Maksim","Malea","Malena","Malia","Malik","Malin","Malina","Malou","Manuel","Mara","Marcel","Marco","Marcus","Margaux","Maria","Marie","Marina","Marino","Mario","Marius","Mark","Marlo","Marlon","Marta","Martim","Martin","Marvin","Matej","Mateo","Mateus","Matheo","Mathias","Mathilda","Mathis","Matias","Matilda","Matilde","Mats","Mattea","Matteo","Matthias","Matti","Mattia","Mattis","Maurice","Mauro","Max","Maxim","Maxime","Maximilian","Maya","Mayla","Ma\xEBl","Ma\xEBlle","Ma\u0161a","Medina","Melanie","Melia","Melina","Melisa","Melissa","Melody","Merjem","Mete","Mia","Micha","Michael","Michelle","Miguel","Mika","Mikail","Mila","Milan","Milena","Miles","Milla","Milo","Mina","Mira","Miran","Miriam","Miro","Mischa","Mohammed","Moira","Mona","Moritz","Muhammad","Muhammed","Musa","Nael","Najla","Nala","Nando","Naomi","Natalia","Natan","Nathalie","Nathan","Naya","Nayla","Nea","Nefeli","Nejla","Nela","Nelio","Neo","Nerea","Neva","Nevin","Nevio","Neyla","Nia","Nick","Nico","Nicola","Nicolas","Nika","Niklas","Niko","Nikola","Nila","Nils","Nina","Nino","Nio","Nisa","Noa","Noah","Noam","Noar","Noe","Noel","Noelia","Noemi","Nora","Nova","No\xE9","Nuri","Nuria","Oliver","Olivia","Omar","Orell","Oscar","Oskar","Pablo","Patrick","Paul","Paula","Paulina","Pauline","Philipp","Philippa","Pia","Pietro","Quinn","Rachel","Rafael","Rahel","Rajana","Raphael","Rayan","Rayyan","Rea","Rebecca","Reina","Rejan","Rhea","Rian","Riana","Rio","Roan","Robin","Robyn","Rodrigo","Romeo","Romina","Romy","Ron","Rona","Ronja","Rosa","Rosalie","Rose","Roy","Roza","Ruben","Ruby","Runa","Ryan","R\xFCya","Sam","Samara","Sami","Samir","Samira","Samu","Samuel","Samuele","Santiago","Santino","Sara","Sarah","Sarina","Sebastian","Selin","Selina","Selma","Sena","Seraina","Serena","Siana","Siara","Sidra","Siena","Sienna","Silas","Silvia","Simea","Simon","Sina","Siro","Sofia","Sofija","Sonja","Sophia","Sophie","Soraya","Stefan","Stella","Sven","Tabea","Talia","Tamara","Tara","Tea","Teo","Teodor","Teodora","Teresa","Tessa","Thea","Theo","Theodor","Theresa","Thiago","Thierry","Thomas","Tiago","Tian","Tiana","Tilda","Till","Tilla","Tim","Timea","Timo","Timon","Tina","Tobias","Tom","Tom\xE1s","Tristan","Tuana","Uma","Una","Valentin","Valentina","Valeria","Valerie","Vanessa","Vasco","Vera","Victor","Victoria","Viktor","Viktoria","Vincent","Viola","Vito","Vittoria","Vivienne","Vuk","William","Wilma","Xenia","Yael","Yago","Yanis","Yann","Yannick","Yannik","Yara","Yaro","Yaron","Yasin","Yasmin","Younes","Yuna","Yuri","Yusuf","Zana","Zara","Zoe","Zoey","Zo\xE9","Zo\xEB"],female:["Aaliyah","Abigail","Ada","Adea","Adele","Adina","Adriana","Agatha","Aida","Aina","Aisha","Ajana","Ajla","Ajlin","Ajna","Alana","Alara","Alba","Alea","Aleksandra","Alena","Alenia","Alessia","Alexandra","Alexia","Aleya","Aleyna","Alia","Alice","Alicia","Alina","Aline","Alisa","Alisha","Alissa","Alissia","Alix","Aliya","Aliyah","Alma","Alva","Alya","Alyssa","Alyssia","Amadea","Amalia","Amanda","Amara","Amber","Ambra","Amea","Amelia","Amelie","Amen","Amina","Amira","Amra","Amy","Am\xE1lia","Am\xE9lia","Am\xE9lie","Ana","Anahita","Anastasia","Anastasija","Ana\xEFs","Andjelina","Andrina","Angela","Angelina","Anik","Anika","Anina","Anna","Annabel","Annika","Anouk","Antonia","Aria","Ariana","Arianna","Arielle","Arisa","Arya","Asia","Asja","Astrid","Asya","Aurelia","Aurora","Ava","Ayana","Ayla","Aylin","Azra","Beatrice","Bella","Bianca","Bigna","Camilla","Carla","Carlota","Carlotta","Carolina","Cataleya","Cecilia","Celina","Celine","Charlotte","Chiara","Chloe","Chlo\xE9","Claire","Clara","Clea","Cleo","C\xE9leste","C\xE9line","Dahlia","Dalia","Daria","Dea","Deborah","Delia","Diana","Diara","Dina","Dua","Eda","Ela","Elea","Eleanor","Elena","Eleni","Eleonora","Eliana","Elif","Elin","Elina","Eline","Eliona","Elisa","Elisabeth","Eliza","Ella","Ellen","Elli","Ellie","Elodie","Elsa","Ema","Emelie","Emilia","Emilie","Emilija","Emily","Emma","Ena","Enya","Estelle","Esther","Eva","Felicia","Filippa","Finja","Fiona","Flavia","Florence","Flurina","Freya","Frida","Gelila","Giada","Gianna","Gioia","Giorgia","Giulia","Grace","Hailey","Hana","Hanna","Hannah","Hava","Helen","Helena","Helin","Hira","Ida","Ilaria","Ilenia","Iliana","Ina","Inara","Irina","Iris","Isabel","Isabella","Isabelle","Iva","Ivy","Jael","Jana","Janina","Jara","Jasmin","Jasmine","Jennifer","Jil","Joana","Johanna","Joline","Josephine","Joy","Julia","Juliana","Julie","Juliette","Juna","Jusra","Kaia","Kaja","Kalea","Kaya","Kiara","Kira","Klara","Klea","Ladina","Laia","Laila","Lana","Lara","Larina","Larissa","Laura","Lavinia","Layla","Lea","Leah","Leana","Leandra","Leia","Leila","Lena","Leni","Lenia","Lenya","Leona","Leoni","Leonie","Leonora","Letizia","Leya","Leyla","Lia","Liana","Liara","Lilia","Lilian","Liliana","Lilly","Lily","Lina","Linda","Linn","Linnea","Lisa","Liv","Liva","Livia","Liya","Liyana","Lola","Lorena","Lotta","Lou","Louisa","Louise","Lua","Luana","Lucia","Lucy","Luena","Luisa","Luise","Luna","Lya","Lynn","L\xE9a","L\xE9onie","Madlaina","Maia","Maila","Maira","Maja","Malea","Malena","Malia","Malin","Malina","Malou","Mara","Margaux","Maria","Marie","Marina","Marta","Mathilda","Matilda","Matilde","Mattea","Maya","Mayla","Ma\xEBlle","Ma\u0161a","Medina","Melanie","Melia","Melina","Melisa","Melissa","Melody","Merjem","Mia","Michelle","Mika","Mila","Milena","Milla","Mina","Mira","Miriam","Moira","Mona","Najla","Nala","Naomi","Natalia","Nathalie","Naya","Nayla","Nea","Nefeli","Nejla","Nela","Nerea","Neva","Neyla","Nia","Nika","Nila","Nina","Nisa","Noa","Noelia","Noemi","Nora","Nova","Nuria","Olivia","Paula","Paulina","Pauline","Philippa","Pia","Rachel","Rahel","Rajana","Rea","Rebecca","Reina","Rhea","Riana","Robyn","Romina","Romy","Rona","Ronja","Rosa","Rosalie","Rose","Roza","Ruby","Runa","R\xFCya","Samara","Samira","Sara","Sarah","Sarina","Selin","Selina","Selma","Sena","Seraina","Serena","Siana","Siara","Sidra","Siena","Sienna","Silvia","Simea","Sina","Sofia","Sofija","Sonja","Sophia","Sophie","Soraya","Stella","Tabea","Talia","Tamara","Tara","Tea","Teodora","Teresa","Tessa","Thea","Theresa","Tiana","Tilda","Tilla","Timea","Tina","Tuana","Uma","Una","Valentina","Valeria","Valerie","Vanessa","Vera","Victoria","Viktoria","Viola","Vittoria","Vivienne","Wilma","Xenia","Yael","Yara","Yasmin","Yuna","Zana","Zara","Zoe","Zoey","Zo\xE9","Zo\xEB"],male:["Aaron","Abdullah","Adam","Adrian","Ahmed","Aiden","Ajan","Alan","Albert","Alec","Alejandro","Aleksa","Aleksander","Alessandro","Alessio","Alex","Alexander","Alexandre","Alexandros","Alexis","Alfred","Ali","Amar","Amin","Amir","Andrea","Andrej","Andri","Andrin","Angelo","Anik","Anthony","Anton","Antonio","Anuar","Aren","Ari","Arian","Arion","Aris","Armando","Armin","Arno","Aron","Arthur","Arun","Atlas","Aurel","Aurelio","Auron","Axel","Ayan","Ayaz","Ayden","Bela","Ben","Benjamin","Bj\xF6rn","Bogdan","Bruno","Bryan","B\xE9la","Can","Carl","Carlo","Cedric","Charles","Charlie","Christian","Christopher","Clemens","Colin","Constantin","Cristian","C\xE9dric","Damian","Daniel","Danilo","Dante","Darian","Dario","Daris","Darius","David","Davide","Dean","Denis","Deniz","Devin","Diar","Diego","Dino","Dion","Dominik","Dorian","Dylan","Eddie","Eduard","Eduardo","Eli","Elia","Eliah","Elian","Eliano","Elias","Elija","Elijah","Elio","Elion","Elliot","Elyas","Emanuel","Emil","Emilian","Emilio","Emir","Enea","Enes","Enio","Ennio","Ensar","Enyo","Enzo","Eric","Erik","Eron","Esra","Ethan","Evan","Fabian","Fabio","Federico","Felix","Ferdinand","Filip","Filippo","Finn","Fionn","Flavio","Florian","Flurin","Flynn","Francesco","Francis","Francisco","Frederick","Frederik","Fynn","Gabriel","Gabriele","Georg","Gian","Gianluca","Gino","Gioele","Gion","Giuliano","Giulio","Giuseppe","Hamza","Henri","Henrik","Henry","Hugo","Ian","Ignacy","Ilai","Ilay","Ilian","Ilias","Ilja","Ilyas","Imran","Isa","Isaac","Isaiah","Ivan","Jack","Jaden","Jakob","Jakub","Jamal","James","Jamie","Jamiro","Jan","Janis","Jannik","Jannis","Janosch","Jari","Jaro","Jaron","Jasin","Jason","Jay","Jayden","Jeremy","Joah","Joel","John","Jon","Jona","Jonah","Jonas","Jonathan","Jorin","Joris","Joshua","Jovan","Jovin","Juan","Jules","Julian","Julien","Julius","Juri","Jusuf","Kai","Karl","Keano","Keanu","Kerem","Keyan","Kian","Kiano","Kilian","Kimo","Kiyan","Konstantin","Kristijan","Kuzey","Kyan","Lars","Lasse","Lauri","Laurin","Lean","Leandro","Leano","Leart","Ledion","Len","Lenn","Lenni","Lennox","Lenny","Leo","Leon","Leonard","Leonardo","Leonel","Leonidas","Leonis","Leopold","Leroy","Levi","Levin","Levio","Leyan","Liam","Lian","Lias","Lino","Linus","Lio","Lion","Lionel","Lior","Liron","Livio","Liyan","Lorenz","Lorenzo","Lorian","Lorik","Lorin","Loris","Lou","Louan","Louie","Louis","Lo\xEFc","Luan","Luc","Luca","Lucas","Lui","Luis","Luka","Lukas","Luke","Lyan","L\xE9o","Mael","Mahir","Mailo","Maksim","Malik","Manuel","Marcel","Marco","Marcus","Marino","Mario","Marius","Mark","Marlo","Marlon","Martim","Martin","Marvin","Matej","Mateo","Mateus","Matheo","Mathias","Mathis","Matias","Mats","Matteo","Matthias","Matti","Mattia","Mattis","Maurice","Mauro","Max","Maxim","Maxime","Maximilian","Ma\xEBl","Mete","Micha","Michael","Miguel","Mika","Mikail","Milan","Miles","Milo","Miran","Miro","Mischa","Mohammed","Moritz","Muhammad","Muhammed","Musa","Nael","Nando","Natan","Nathan","Nelio","Neo","Nevin","Nevio","Nick","Nico","Nicola","Nicolas","Niklas","Niko","Nikola","Nils","Nino","Nio","Noa","Noah","Noam","Noar","Noe","Noel","No\xE9","Nuri","Oliver","Omar","Orell","Oscar","Oskar","Pablo","Patrick","Paul","Philipp","Pietro","Quinn","Rafael","Raphael","Rayan","Rayyan","Rejan","Rian","Rio","Roan","Robin","Rodrigo","Romeo","Ron","Roy","Ruben","Ryan","Sam","Sami","Samir","Samu","Samuel","Samuele","Santiago","Santino","Sebastian","Silas","Simon","Siro","Stefan","Sven","Teo","Teodor","Theo","Theodor","Thiago","Thierry","Thomas","Tiago","Tian","Till","Tim","Timo","Timon","Tobias","Tom","Tom\xE1s","Tristan","Valentin","Vasco","Victor","Viktor","Vincent","Vito","Vuk","William","Yago","Yanis","Yann","Yannick","Yannik","Yaro","Yaron","Yasin","Younes","Yuri","Yusuf"]};var B=["Superviseur","Executif","Manager","Ingenieur","Specialiste","Directeur","Coordinateur","Administrateur","Architecte","Analyste","Designer","Technicien","Developpeur","Producteur","Consultant","Assistant","Agent","Stagiaire"];var P={generic:["Abegglen","Ackermann","Aebischer","Aeby","Aigroz","Aregger","Bagnoud","Ballouhey","Balmer","Barras","Bavaud","Beerli","Berney","Berset","Berthoud","Beyeler","Bidaud","Bideau","Blanc","Blatter","Blazer","Bolle","Borgeaud","Bossard","Bosson","Bossy","Bourcard","Bourgeois","Brogli","Br\xE4ndli","Br\xFCgger","Burckhardt","Burkhard","Burkhardt","Caillat","Cailler","Calame","Cali","Chappuis","Chapuis","Chapuisat","Chassot","Chollet","Chopard","Choquart","Chuit","Clerc","Cloos","Clottu","Coinchon","Corboz","Cottier","Coulon","Courten","Cuche","Dallenbach","De Pury","Denzler","Dietsche","Droz","Dubochet","Dubuis","Ducommun","Ducret","Dutoit","Egli","Emery","Emig","Fankhauser","Fauche","Favre","Felber","Fl\xFCckiger","Foretay","Frey","Freymond","Friedli","Funk","Gehrig","Geisendorf","Genoud","Gerwig","Gilli\xE9ron","Girard","Girardet","Glasson","Graber","Grandjean","Groebli","Grosjean","Guinand","G\xE4hwiler","Hediger","Hennezel","Henri","Hochstrasser","Hofer","Hollard","Hommel","Honegger","Hottinguer","Huguenin","Humbert-Droz","Huwiler","H\xE4nni","Jeanneret","Kolly","Kr\xFCgel","Kubli","Kunz","K\xE4lin","K\xFCng","Lambelet","Leuba","Leutenegger","Loup","Marcet","Matthey","Meichtry","Meier","Menu","Merian","Mermoud","Meylan","Monnard","Monod","Morax","Moraz","Moser","Mottet","Muschg","Musy","Nauer","Niggeler","Oberlin","Oppliger","Ostervald","Parlier","Perret","Perrier","Perrin","Perroy","Piaget","Pictet","Piguet","Pinon","Pittard","Plomb","Pury","Python","Quartenoud","Quinodoz","RIS","Racordon","Ravet","Reymond","Reynold","Ribaupierre","Riner","Ritz","Rochaix","Rochat","Romy","Rougemont","Roulin","Rousseau","R\xF6thlisberger","R\xFCttimann","Sandoz","Saussure","Savary","Scheurer","Schmid","Schnider","Schnyder","Schorderet","Schrepfer","Schweitz","Schweizer","Schwizgebel","Simmen","Simonet","Soutter","Spoerri","Sprunger","Suter","Sutermeister","Sutz","Tinguely","Tissot","Toma","Torriani","Tschudi","Vallet","Wasser","Wehrle","Welti","Widmer","\xC9coffey"]};var J={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var D=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:2},{value:"{{person.firstName}} {{person.lastName}}",weight:8}];var F={generic:["Dr","M","Mlle","Mme","Prof"],female:["Dr","Mlle","Mme","Prof"],male:["Dr","M","Prof"]};var x=["Femme","Homme"];var Y={first_name:R,job_type:B,last_name:P,last_name_pattern:J,name:D,prefix:F,sex:x},T=Y;var G=["0800 ### ###","0800 ## ## ##","0## ### ## ##","+41 ## ### ## ##","0900 ### ###","076 ### ## ##","079 ### ## ##","078 ### ## ##","+41 76 ### ## ##","+41 78 ### ## ##","+41 79 ### ## ##","0041 76 ### ## ##","0041 78 ### ## ##","0041 79 ### ## ##"];var k=["+41800######","+41#########","+41900######","+4176#######","+4179#######","+4178#######"];var z=["0800 ### ###","0## ### ## ##","0900 ### ###","076 ### ## ##","079 ### ## ##","078 ### ## ##"];var _={human:G,international:k,national:z},V=_;var Z={format:V},I=Z;var W={cell_phone:l,internet:t,location:b,lorem:v,metadata:N,person:T,phone_number:I},H=W;var Ce=new a({locale:[H,n,i,e]});export{H as a,Ce as b};
