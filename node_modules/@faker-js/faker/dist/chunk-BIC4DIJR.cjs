"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var n=[56,62,59];var i=["{{cell_phone.common_cell_prefix}}-###-####"];var aa={common_cell_prefix:n,formats:i},o=aa;var t=["bl\xE5","brun","gr\xE5","gr\xF6n","gul","guld","indigo","korall","lila","purpur","rosa","r\xF6d","silver","svart","vit"];var ea={human:t},l=ea;var s=["B\xF6cker","Datorer","Elektronik","Filmer","<PERSON><PERSON>","H\xE4lsa","<PERSON><PERSON>\xE4<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sk\xF6nhet","<PERSON>mycken","<PERSON><PERSON>","Sport","Tr\xE4dg\xE5rd","Verktyg"];var m={adjective:["Ergonomisk","Fantastisk","Grym","Intelligent","Liten","Otrolig","Praktisk","Robust","Slimmad","S\xF6t"],material:["Betong","Bomull","Granit","Gummi","Latex","Metall","Plast","St\xE5l","Tr\xE4"],product:["Bil","Bord","Byxor","Dator","Handskar","Hatt","Skjorta","Skor","Stol"]};var ra={department:s,product_name:m},d=ra;var u=["AB","Aktiebolag","Group","Gruppen","HB","Investment","Kommanditbolag"];var p=["{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}-{{person.last_name.generic}}"];var na={legal_entity_type:u,name_pattern:p},g=na;var f={wide:["april","augusti","december","februari","januari","juli","juni","maj","mars","november","oktober","september"],abbr:["apr","aug","dec","feb","jan","jul","jun","maj","mar","nov","okt","sep"]};var k={wide:["fredag","l\xF6rdag","m\xE5ndag","onsdag","s\xF6ndag","tisdag","torsdag"],abbr:["fre","l\xF6r","m\xE5n","ons","s\xF6n","tis","tor"]};var ia={month:f,weekday:k},b=ia;var c=["com","info","nu","org","se"];var oa={domain_suffix:c},S=oa;var v=["###","##","#"];var h=["{{location.city_prefix}}{{location.city_suffix}}"];var y=["S\xF6der","Norr","V\xE4st","\xD6ster","Aling","Ar","Av","Bo","Br","B\xE5","Ek","En","Esk","Fal","G\xE4v","G\xF6te","Ha","Helsing","Karl","Krist","Kram","Kung","K\xF6","Lyck","Ny"];var x=["stad","land","s\xE5s","\xE5s","holm","tuna","sta","berg","l\xF6v","borg","mora","hamn","fors","k\xF6ping","by","hult","torp","fred","vik"];var L=["s V\xE4g","s Gata"];var M=["Ryssland","Kanada","Kina","USA","Brasilien","Australien","Indien","Argentina","Kazakstan","Algeriet","DR Kongo","Danmark","F\xE4r\xF6arna","Gr\xF6nland","Saudiarabien","Mexiko","Indonesien","Sudan","Libyen","Iran","Mongoliet","Peru","Tchad","Niger","Angola","Mali","Sydafrika","Colombia","Etiopien","Bolivia","Mauretanien","Egypten","Tanzania","Nigeria","Venezuela","Namibia","Pakistan","Mo\xE7ambique","Turkiet","Chile","Zambia","Marocko","V\xE4stsahara","Burma","Afghanistan","Somalia","Centralafrikanska republiken","Sydsudan","Ukraina","Botswana","Madagaskar","Kenya","Frankrike","Franska Guyana","Jemen","Thailand","Spanien","Turkmenistan","Kamerun","Papua Nya Guinea","Sverige","Uzbekistan","Irak","Paraguay","Zimbabwe","Japan","Tyskland","Kongo","Finland","Malaysia","Vietnam","Norge","Svalbard","Jan Mayen","Elfenbenskusten","Polen","Italien","Filippinerna","Ecuador","Burkina Faso","Nya Zeeland","Gabon","Guinea","Storbritannien","Ghana","Rum\xE4nien","Laos","Uganda","Guyana","Oman","Vitryssland","Kirgizistan","Senegal","Syrien","Kambodja","Uruguay","Tunisien","Surinam","Nepal","Bangladesh","Tadzjikistan","Grekland","Nicaragua","Eritrea","Nordkorea","Malawi","Benin","Honduras","Liberia","Bulgarien","Kuba","Guatemala","Island","Sydkorea","Ungern","Portugal","Jordanien","Serbien","Azerbajdzjan","\xD6sterrike","F\xF6renade Arabemiraten","Tjeckien","Panama","Sierra Leone","Irland","Georgien","Sri Lanka","Litauen","Lettland","Togo","Kroatien","Bosnien och Hercegovina","Costa Rica","Slovakien","Dominikanska republiken","Bhutan","Estland","Nederl\xE4nderna","Schweiz","Guinea-Bissau","Taiwan","Moldavien","Belgien","Lesotho","Armenien","Albanien","Salomon\xF6arna","Ekvatorialguinea","Burundi","Haiti","Rwanda","Makedonien","Djibouti","Belize","Israel","El Salvador","Slovenien","Fiji","Kuwait","Swaziland","Timor-Leste","Montenegro","Bahamas","Vanuatu","Qatar","Gambia","Jamaica","Kosovo","Libanon","Cypern","Brunei","Trinidad och Tobago","Kap Verde","Samoa","Luxemburg","Komorerna","Mauritius","S\xE3o Tom\xE9 och Pr\xEDncipe","Kiribati","Dominica","Tonga","Mikronesiens federerade stater","Singapore","Bahrain","Saint Lucia","Andorra","Palau","Seychellerna","Antigua och Barbuda","Barbados","Saint Vincent och Grenadinerna","Grenada","Malta","Maldiverna","Saint Kitts och Nevis","Marshall\xF6arna","Liechtenstein","San Marino","Tuvalu","Nauru","Monaco","Vatikanstaten"];var A=["Ale","Alings\xE5s","Alvesta","Aneby","Arboga","Arjeplog","Arvidsjaur","Arvika","Askersund","Avesta","Bengtsfors","Berg","Bjurholm","Bjuv","Boden","Bollebygd","Bolln\xE4s","Borgholm","Borl\xE4nge","Bor\xE5s","Botkyrka","Boxholm","Brom\xF6lla","Br\xE4cke","Burl\xF6v","B\xE5stad","Dals-Ed","Danderyd","Degerfors","Dorotea","Eda","Eker\xF6","Eksj\xF6","Emmaboda","Enk\xF6ping","Eskilstuna","Esl\xF6v","Essunga","Fagersta","Falkenberg","Falk\xF6ping","Falun","Filipstad","Finsp\xE5ng","Flen","Forshaga","F\xE4rgelanda","Gagnef","Gislaved","Gnesta","Gnosj\xF6","Gotland","Grums","Gr\xE4storp","Gullsp\xE5ng","G\xE4llivare","G\xE4vle","G\xF6teborg","G\xF6tene","Habo","Hagfors","Hallsberg","Hallstahammar","Halmstad","Hammar\xF6","Haninge","Haparanda","Heby","Hedemora","Helsingborg","Herrljunga","Hjo","Hofors","Huddinge","Hudiksvall","Hultsfred","Hylte","H\xE5bo","H\xE4llefors","H\xE4rjedalen","H\xE4rn\xF6sand","H\xE4rryda","H\xE4ssleholm","H\xF6gan\xE4s","H\xF6gsby","H\xF6rby","H\xF6\xF6r","Jokkmokk","J\xE4rf\xE4lla","J\xF6nk\xF6ping","Kalix","Kalmar","Karlsborg","Karlshamn","Karlskoga","Karlskrona","Karlstad","Katrineholm","Kil","Kinda","Kiruna","Klippan","Knivsta","Kramfors","Kristianstad","Kristinehamn","Krokom","Kumla","Kungsbacka","Kungs\xF6r","Kung\xE4lv","K\xE4vlinge","K\xF6ping","Laholm","Landskrona","Lax\xE5","Lekeberg","Leksand","Lerum","Lessebo","Liding\xF6","Lidk\xF6ping","Lilla Edet","Lindesberg","Link\xF6ping","Ljungby","Ljusdal","Ljusnarsberg","Lomma","Ludvika","Lule\xE5","Lund","Lycksele","Lysekil","Malm\xF6","Malung-S\xE4len","Mal\xE5","Mariestad","Mark","Markaryd","Mellerud","Mj\xF6lby","Mora","Motala","Mullsj\xF6","Munkedal","Munkfors","M\xF6lndal","M\xF6nster\xE5s","M\xF6rbyl\xE5nga","Nacka","Nora","Norberg","Nordanstig","Nordmaling","Norrk\xF6ping","Norrt\xE4lje","Norsj\xF6","Nybro","Nykvarn","Nyk\xF6ping","Nyn\xE4shamn","N\xE4ssj\xF6","Ockelbo","Olofstr\xF6m","Orsa","Orust","Osby","Oskarshamn","Ovan\xE5ker","Oxel\xF6sund","Pajala","Partille","Perstorp","Pite\xE5","Ragunda","Robertsfors","Ronneby","R\xE4ttvik","Sala","Salem","Sandviken","Sigtuna","Simrishamn","Sj\xF6bo","Skara","Skellefte\xE5","Skinnskatteberg","Skurup","Sk\xF6vde","Smedjebacken","Sollefte\xE5","Sollentuna","Solna","Sorsele","Soten\xE4s","Staffanstorp","Stenungsund","Stockholm","Storfors","Storuman","Str\xE4ngn\xE4s","Str\xF6mstad","Str\xF6msund","Sundbyberg","Sundsvall","Sunne","Surahammar","Sval\xF6v","Svedala","Svenljunga","S\xE4ffle","S\xE4ter","S\xE4vsj\xF6","S\xF6derhamn","S\xF6derk\xF6ping","S\xF6dert\xE4lje","S\xF6lvesborg","Tanum","Tibro","Tidaholm","Tierp","Timr\xE5","Tingsryd","Tj\xF6rn","Tomelilla","Torsby","Tors\xE5s","Tranemo","Tran\xE5s","Trelleborg","Trollh\xE4ttan","Trosa","Tyres\xF6","T\xE4by","T\xF6reboda","Uddevalla","Ulricehamn","Ume\xE5","Upplands-Bro","Upplands V\xE4sby","Uppsala","Uppvidinge","Vadstena","Vaggeryd","Valdemarsvik","Vallentuna","Vansbro","Vara","Varberg","Vaxholm","Vellinge","Vetlanda","Vilhelmina","Vimmerby","Vindeln","Ving\xE5ker","V\xE5rg\xE5rda","V\xE4nersborg","V\xE4nn\xE4s","V\xE4rmd\xF6","V\xE4rnamo","V\xE4stervik","V\xE4ster\xE5s","V\xE4xj\xF6","Ydre","Ystad","\xC5m\xE5l","\xC5nge","\xC5re","\xC5rj\xE4ng","\xC5sele","\xC5storp","\xC5tvidaberg","\xC4lmhult","\xC4lvdalen","\xC4lvkarleby","\xC4lvsbyn","\xC4ngelholm","\xD6cker\xF6","\xD6desh\xF6g","\xD6rebro","\xD6rkelljunga","\xD6rnsk\xF6ldsvik","\xD6stersund","\xD6ster\xE5ker","\xD6sthammar","\xD6stra G\xF6inge","\xD6verkalix","\xD6vertorne\xE5"];var K=["#####"];var B=["Lgh. ###","Hus ###"];var H=["Blekinge","Dalarna","Gotland","G\xE4vleborg","G\xF6teborg","Halland","J\xE4mtland","J\xF6nk\xF6ping","Kalmar","Kronoberg","Norrbotten","Skaraborg","Sk\xE5ne","Stockholm","S\xF6dermanland","Uppsala","V\xE4rmland","V\xE4sterbotten","V\xE4sternorrland","V\xE4stmanland","\xC4lvsborg","\xD6rebro","\xD6sterg\xF6tland"];var j={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var E=["Bj\xF6rk","J\xE4rnv\xE4gs","Ring","Skol","Skogs","Ny","Gran","Idrotts","Stor","Kyrk","Industri","Park","Strand","Tr\xE4dg\xE5rd","\xC4ngs","Kyrko","Villa","Ek","Kvarn","Stations","Back","Furu","Gen","Fabriks","\xC5ker","B\xE4ck","Asp"];var _=["{{location.street_name}}{{location.street_suffix}}","{{location.street_prefix}} {{location.street_name}}{{location.street_suffix}}","{{person.first_name.generic}}{{location.common_street_suffix}}","{{person.last_name.generic}}{{location.common_street_suffix}}"];var G=["V\xE4stra","\xD6stra","Norra","S\xF6dra","\xD6vre","Undre"];var N=["v\xE4gen","gatan","gr\xE4nden","g\xE4rdet","all\xE9n"];var ta={building_number:v,city_pattern:h,city_prefix:y,city_suffix:x,common_street_suffix:L,country:M,county:A,postcode:K,secondary_address:B,state:H,street_address:j,street_name:E,street_pattern:_,street_prefix:G,street_suffix:N},D=ta;var la={title:"Swedish",code:"sv",language:"sv",endonym:"Svenska",dir:"ltr",script:"Latn"},J=la;var T={generic:["Adam","Agnes","Agneta","Albin","Alexander","Alexandra","Ali","Alice","Alva","Amanda","Anders","Andreas","Anette","Anita","Ann","Ann-Christin","Ann-Marie","Anna","Anneli","Annika","Anton","Arvid","Astrid","Axel","Barbro","Bengt","Berit","Birgitta","Bj\xF6rn","Bo","Britt","Britt-Marie","Camilla","Carina","Caroline","Cecilia","Charlotte","Christer","Christian","Christoffer","Claes","Daniel","David","Dennis","Ebba","Edvin","Elias","Elin","Elisabeth","Ella","Ellen","Elsa","Emelie","Emil","Emilia","Emma","Erik","Erika","Eva","Felicia","Felix","Filip","Fredrik","Frida","Gabriel","Gun","Gunilla","Gunnar","Gunnel","Gustav","G\xF6ran","Hanna","Hans","Helen","Helena","Henrik","Hugo","H\xE5kan","Ida","Inger","Ingrid","Isabelle","Isak","Jakob","Jan","Jenny","Jesper","Jessica","Jimmy","Joakim","Joel","Johan","Johanna","John","Johnny","Jonas","Jonathan","Josef","Josefin","Julia","J\xF6rgen","Karin","Karl","Karolina","Katarina","Kenneth","Kent","Kerstin","Kevin","Kjell","Klara","Kristina","Kurt","Lars","Leif","Lena","Lennart","Leo","Liam","Lina","Linda","Linn","Linn\xE9a","Linus","Lisa","Lisbeth","Louise","Lovisa","Lucas","Ludvig","Madeleine","Magnus","Maja","Malin","Marcus","Margareta","Maria","Marianne","Marie","Martin","Matilda","Mats","Mattias","Max","Mikael","Mikaela","Moa","Mohamed","Mona","Monica","Nathalie","Niklas","Nils","Nina","Oliver","Olivia","Olle","Olof","Oskar","Patrik","Per","Pernilla","Peter","Pia","Pontus","Rasmus","Rebecca","Rickard","Robert","Robin","Roger","Rolf","Samuel","Sandra","Sara","Sebastian","Simon","Siv","Sofia","Sofie","Sonja","Stefan","Stig","Susanne","Sven","Therese","Thomas","Tobias","Tommy","Torbj\xF6rn","Ulf","Ulla","Ulrika","Viktor","Viktoria","William","Wilma","Yvonne","\xC5ke","\xC5sa"],female:["Agnes","Agneta","Alexandra","Alice","Alva","Amanda","Anette","Anita","Ann","Ann-Christin","Ann-Marie","Anna","Anneli","Annika","Astrid","Barbro","Berit","Birgitta","Britt","Britt-Marie","Camilla","Carina","Caroline","Cecilia","Charlotte","Ebba","Elin","Elisabeth","Ella","Ellen","Elsa","Emelie","Emilia","Emma","Erika","Eva","Felicia","Frida","Gun","Gunilla","Gunnel","Hanna","Helen","Helena","Ida","Inger","Ingrid","Isabelle","Jenny","Jessica","Johanna","Josefin","Julia","Karin","Karolina","Katarina","Kerstin","Klara","Kristina","Lena","Lina","Linda","Linn","Linn\xE9a","Lisa","Lisbeth","Louise","Lovisa","Madeleine","Maja","Malin","Margareta","Maria","Marianne","Marie","Matilda","Mikaela","Moa","Mona","Monica","Nathalie","Nina","Olivia","Pernilla","Pia","Rebecca","Sandra","Sara","Siv","Sofia","Sofie","Sonja","Susanne","Therese","Ulla","Ulrika","Viktoria","Wilma","Yvonne","\xC5sa"],male:["Adam","Albin","Alexander","Ali","Anders","Andreas","Anton","Arvid","Axel","Bengt","Bj\xF6rn","Bo","Christer","Christian","Christoffer","Claes","Daniel","David","Dennis","Edvin","Elias","Emil","Erik","Felix","Filip","Fredrik","Gabriel","Gunnar","Gustav","G\xF6ran","Hans","Henrik","Hugo","H\xE5kan","Isak","Jakob","Jan","Jesper","Jimmy","Joakim","Joel","Johan","John","Johnny","Jonas","Jonathan","Josef","J\xF6rgen","Karl","Kenneth","Kent","Kevin","Kjell","Kurt","Lars","Leif","Lennart","Leo","Liam","Linus","Lucas","Ludvig","Magnus","Marcus","Martin","Mats","Mattias","Max","Mikael","Mohamed","Niklas","Nils","Oliver","Olle","Olof","Oskar","Patrik","Per","Peter","Pontus","Rasmus","Rickard","Robert","Robin","Roger","Rolf","Samuel","Sebastian","Simon","Stefan","Stig","Sven","Thomas","Tobias","Tommy","Torbj\xF6rn","Ulf","Viktor","William","\xC5ke"]};var F=["Verkst\xE4llande","Program","Varum\xE4rke","S\xE4kerhet","Forskning","Marknadsf\xF6ring","Direktiv","Implementation","Integration","Funktionalitet","Taktik","Marknader","Division","Grupp","Optimering","Infrastruktur","Operativ","Finansiell","Kommunikation"];var P=["Ansvarig","Senior","Junior","F\xF6retags","Organisatorisk","Intern","Tilltr\xE4dande","Nationell","Internationell","Regional","Global"];var V=["Direkt\xF6r","Ingenj\xF6r","Handledare","Koordinator","Specialist","Administrat\xF6r","Arkitekt","Analytiker","Designer","Planerare","Tekniker","Utvecklare","Konsult","Representant","Chef","Producent","Assistent"];var I={generic:["Abrahamsson","Ahmed","Ali","Andersson","Andreasson","Arvidsson","Axelsson","Bengtsson","Berg","Berggren","Berglund","Bergman","Bergqvist","Bergstr\xF6m","Bj\xF6rk","Bj\xF6rklund","Blom","Blomqvist","Claesson","Dahl","Dahlberg","Danielsson","Ek","Eklund","Ekstr\xF6m","Eliasson","Engstr\xF6m","Eriksson","Falk","Forsberg","Fransson","Fredriksson","Gunnarsson","Gustafsson","Hansen","Hansson","Hassan","Hedlund","Hellstr\xF6m","Henriksson","Hermansson","Holm","Holmberg","Holmgren","Holmqvist","H\xE5kansson","Isaksson","Jakobsson","Jansson","Johansson","Jonsson","J\xF6nsson","Karlsson","Larsson","Lind","Lindberg","Lindgren","Lindholm","Lindqvist","Lindstr\xF6m","Lund","Lundberg","Lundgren","Lundin","Lundqvist","Lundstr\xF6m","L\xF6fgren","Magnusson","Martinsson","Mattsson","Mohamed","M\xE5nsson","M\xE5rtensson","Nilsson","Norberg","Nordin","Nordstr\xF6m","Nyberg","Nystr\xF6m","Olofsson","Olsson","Persson","Pettersson","P\xE5lsson","Samuelsson","Sandberg","Sandstr\xF6m","Sj\xF6berg","Sj\xF6gren","Str\xF6m","Str\xF6mberg","Sundberg","Svensson","S\xF6derberg","Viklund","Wallin","Wikstr\xF6m","\xC5berg","\xC5kesson","\xD6berg"]};var C={generic:[{value:"{{person.last_name.generic}}",weight:8},{value:"{{person.last_name.generic}} {{person.last_name.generic}}",weight:2}]};var R=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:9}];var O={generic:["Dr.","PhD.","Prof."],female:["Dr.","PhD.","Prof."],male:["Dr.","PhD.","Prof."]};var sa={first_name:T,job_area:F,job_descriptor:P,job_type:V,last_name:I,last_name_pattern:C,name:R,prefix:O},U=sa;var w=["070#######","+4670#######","072#######","+4672#######","073#######","+4673#######","076#######","+4676#######","079#######","+4679#######","01#####","+461#####","02######","+462######","03#######","+463#######","04########","+464########","05#####","+465#####","06######","+466######","08#######","+468#######","09########","+469########"];var z=["+4670#######","+4672#######","+4673#######","+4676#######","+4679#######","+461#####","+462######","+463#######","+464########","+465#####","+466######","+468#######","+469########"];var q=["070-### ## ##","072-### ## ##","073-### ## ##","076-### ## ##","079-### ## ##","1#####","02#-### ##","03##-### ##","04#-### ## ##","5#####","06##-## ##","08-### ## ##","09##-## ## ##"];var ma={human:w,international:z,national:q},W=ma;var da={format:W},Y=da;var Z=["{{location.city}} {{team.suffix}}"];var Q=["IF","FF","BK","HK","AIF","SK","FC","BoIS","FK","BIS","FIF","IK"];var ua={name:Z,suffix:Q},X=ua;var pa={cell_phone:o,color:l,commerce:d,company:g,date:b,internet:S,location:D,metadata:J,person:U,phone_number:Y,team:X},$= exports.a =pa;var dr=new (0, _chunkZKNYQOPPcjs.n)({locale:[$,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = $; exports.b = dr;
