"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkVT2XJBFXcjs = require('./chunk-VT2XJBFX.cjs');var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var o=["BVBA","CVBA","NV","VZW"];var R={legal_entity_type:o},i=R;var t=["be","brussels","com","net","org","vlaanderen"];var l=["gmail.com","hotmail.com","skynet.be","yahoo.com"];var G={domain_suffix:t,free_email:l},s=G;var m=["#","##","###","###a","###b","###c"];var u=["{{location.city_prefix}}","{{location.city_prefix}}{{location.city_suffix}}"];var k=["'s Herenelderen","'s-Gravenvoeren","'s-Gravenwezel","Aaigem","Aalbeke","Aalst","Aalter","Aarschot","Aarsele","Aartrijke","Aartselaar","Achel","Adegem","Adinkerke","Afsnee","Alken","Alsemberg","Alveringem","Antwerpen","Anzegem","Appels","Appelterre-Eichem","Ardooie","Arendonk","As","Aspelare","Asper","Asse","Assebroek","Assenede","Assent","Astene","Attenhoven","Attenrode-Wever","Avekapelle","Avelgem","Averbode","Baaigem","Baal","Baardegem","Baarle-Hertog","Baasrode","Bachte-Maria-Leerne","Balegem","Balen","Bambrugge","Bassevelde","Batsheers","Bavegem","Bavikhove","Bazel","Beek","Beerlegem","Beernem","Beerse","Beersel","Beerst","Beert","Beervelde","Beerzel","Begijnendijk","Beigem","Bekegem","Bekkerzeel","Bekkevoort","Bellegem","Bellem","Bellingen","Belsele","Berbroek","Berchem","Berendrecht-Zandvliet-Lillo","Berg","Beringen","Berlaar","Berlare","Berlingen","Bertem","Beselare","Betekom","Bevel","Bever","Bevere","Beveren","Beverlo","Beverst","Bierbeek","Bikschote","Bilzen","Binderveld","Binkom","Bissegem","Blaasveld","Blanden","Blankenberge","Bocholt","Boechout","Boekhout","Boekhoute","Boezinge","Bogaarden","Bommershoven","Bonheiden","Booischot","Booitshoeke","Boom","Boorsem","Boortmeerbeek","Borchtlombeek","Borgerhout","Borgloon","Borlo","Bornem","Borsbeek","Borsbeke","Bossuit","Bost","Bottelare","Boutersem","Bouwel","Bovekerke","Brasschaat","Brecht","Bredene","Bree","Breendonk","Brielen","Broechem","Broekom","Brugge","Brussegem","Brustem","Budingen","Buggenhout","Buizingen","Buken","Bulskamp","Bunsbeek","Burcht","Burst","Buvingen","Dadizele","Daknam","Damme","De Klinge","De Moeren","De Panne","De Pinte","Deerlijk","Deftinge","Deinze","Denderbelle","Denderhoutem","Denderleeuw","Dendermonde","Denderwindeke","Dentergem","Dessel","Desselgem","Destelbergen","Desteldonk","Deurle","Deurne","Diegem","Diepenbeek","Diest","Diets-Heur","Dikkebus","Dikkele","Dikkelvenne","Diksmuide","Dilbeek","Dilsen","Doel","Donk","Dormaal","Dranouter","Drieslinter","Drogenbos","Drongen","Dudzele","Duffel","Duisburg","Duras","Dworp","Edegem","Edelare","Eeklo","Eernegem","Egem","Eggewaartskapelle","Eigenbilzen","Eindhout","Eine","Eisden","Eke","Ekeren","Eksaarde","Eksel","Elen","Elene","Elewijt","Eliksem","Elingen","Ellikom","Elsegem","Elst","Elverdinge","Elversele","Emblem","Emelgem","Ename","Engelmanshoven","Eppegem","Erembodegem","Erondegem","Erpe","Erps-Kwerps","Ertvelde","Erwetegem","Esen","Essen","Essene","Etikhove","Ettelgem","Everbeek","Everberg","Evergem","Ezemaal","Gaasbeek","Galmaarden","Gavere","Geel","Geetbets","Gelinden","Gellik","Gelrode","Geluveld","Geluwe","Genk","Genoelselderen","Gent","Gentbrugge","Geraardsbergen","Gerdingen","Gestel","Gierle","Gijverinkhove","Gijzegem","Gijzelbrechtegem","Gijzenzele","Gingelom","Gistel","Gits","Glabbeek-Zuurbemde","Godveerdegem","Goeferdinge","Goetsenhoven","Gontrode","Gooik","Gors-Opleeuw","Gorsem","Gotem","Gottem","Grammene","Grazen","Grembergen","Grimbergen","Grimminge","Grobbendonk","Groot-Bijgaarden","Groot-Gelmen","Groot-Loon","Grote-Brogel","Grote-Spouwen","Grotenberge","Gruitrode","Guigoven","Gullegem","Gutschoven","Haacht","Haaltert","Haasdonk","Haasrode","Hakendover","Halen","Hallaar","Halle","Halle-Booienhoven","Halmaal","Hamme","Hamont","Handzame","Hansbeke","Harelbeke","Hasselt","Hechtel","Heers","Hees","Heestert","Heffen","Heikruis","Heindonk","Heist","Heist-op-den-Berg","Hekelgem","Heks","Helchteren","Heldergem","Helen-Bos","Helkijn","Hemelveerdegem","Hemiksem","Hendrieken","Henis","Heppen","Herderen","Herdersem","Herent","Herentals","Herenthout","Herfelingen","Herk-de-Stad","Herne","Herselt","Herstappe","Herten","Hertsberge","Herzele","Heule","Heurne","Heusden","Hever","Heverlee","Hillegem","Hingene","Hoboken","Hoegaarden","Hoeilaart","Hoeke","Hoelbeek","Hoeleden","Hoepertingen","Hoeselt","Hoevenen","Hofstade","Hollebeke","Holsbeek","Hombeek","Hooglede","Hoogstade","Hoogstraten","Horpmaal","Houtave","Houtem","Houthalen","Houthulst","Houtvenne","Houwaart","Hove","Huise","Huizingen","Huldenberg","Hulshout","Hulste","Humbeek","Hundelgem","Ichtegem","Iddergem","Idegem","Ieper","Impe","Ingelmunster","Ingooigem","Itegem","Itterbeek","Izegem","Izenberge","Jabbeke","Jesseren","Jeuk","Kaaskerke","Kachtem","Kaggevinne","Kalken","Kallo","Kalmthout","Kampenhout","Kanegem","Kanne","Kapelle-op-den-Bos","Kapellen","Kaprijke","Kaster","Kasterlee","Kaulille","Keerbergen","Keiem","Kemmel","Kemzeke","Kerkhove","Kerkom","Kerkom-bij-Sint-Truiden","Kerksken","Kermt","Kerniel","Kersbeek-Miskom","Kessel","Kessel-Lo","Kessenich","Kester","Kieldrecht","Kinrooi","Klein-Gelmen","Kleine-Brogel","Kleine-Spouwen","Klemskerke","Klerken","Kluizen","Knesselare","Knokke","Kobbegem","Koekelare","Koersel","Koksijde","Koningshooikt","Koninksem","Kontich","Kooigem","Koolkerke","Koolskamp","Korbeek-Dijle","Korbeek-Lo","Kortemark","Kortenaken","Kortenberg","Kortessem","Kortijs","Kortrijk","Kortrijk-Dutsel","Kozen","Kraainem","Krombeke","Kruibeke","Kruishoutem","Kumtich","Kuringen","Kuttekoven","Kuurne","Kwaadmechelen","Kwaremont","Laar","Laarne","Lampernisse","Lanaken","Landegem","Landen","Landskouter","Langdorp","Langemark","Lanklaar","Lapscheure","Lauw","Lauwe","Lebbeke","Lede","Ledeberg","Ledegem","Leefdaal","Leerbeek","Leest","Leeuwergem","Leffinge","Leisele","Leke","Lembeek","Lembeke","Lemberge","Lendelede","Leopoldsburg","Letterhoutem","Leupegem","Leut","Leuven","Lichtaart","Lichtervelde","Liedekerke","Lieferinge","Lier","Liezele","Lille","Linden","Linkebeek","Linkhout","Lint","Lippelo","Lissewege","Lo","Lochristi","Loenhout","Loker","Lokeren","Loksbergen","Lombardsijde","Lommel","Londerzeel","Loonbeek","Loppem","Lot","Lotenhulle","Lovendegem","Lovenjoel","Lubbeek","Lummen","Maarke-Kerkem","Maaseik","Machelen","Mal","Maldegem","Malderen","Mannekensvere","Mariakerke","Mariekerke","Marke","Markegem","Martenslinde","Massemen","Massenhoven","Mater","Mazenzele","Mechelen","Mechelen-Bovelingen","Mechelen-aan-de-Maas","Meensel-Kiezegem","Meer","Meerbeek","Meerbeke","Meerdonk","Meerhout","Meerle","Meeswijk","Meetkerke","Meeuwen","Meigem","Meilegem","Meise","Melden","Meldert","Melkwezer","Melle","Melsbroek","Melsele","Melsen","Membruggen","Mendonk","Menen","Merchtem","Mere","Merelbeke","Merendree","Merkem","Merksem","Merksplas","Mesen","Mespelare","Messelbroek","Mettekoven","Meulebeke","Michelbeke","Middelburg","Middelkerke","Mielen-boven-Aalst","Millen","Minderhout","Moelingen","Moen","Moerbeke","Moere","Moerkerke","Moerzeke","Mol","Molenbeek-Wersbeek","Molenbeersel","Molenstede","Mollem","Montenaken","Moorsel","Moorsele","Moorslede","Moortsele","Mopertingen","Moregem","Morkhoven","Mortsel","Muizen","Mullem","Munkzwalm","Munsterbilzen","Munte","Nazareth","Nederboelare","Nederbrakel","Nederename","Nederhasselt","Nederokkerzeel","Nederzwalm-Hermelgem","Neerglabbeek","Neerharen","Neerhespen","Neerijse","Neerlanden","Neerlinter","Neeroeteren","Neerpelt","Neerrepen","Neervelp","Neerwinden","Neigem","Nerem","Nevele","Niel","Niel-bij-As","Niel-bij-Sint-Truiden","Nieuwenhove","Nieuwenrode","Nieuwerkerken","Nieuwkapelle","Nieuwkerke","Nieuwkerken-Waas","Nieuwmunster","Nieuwpoort","Nieuwrode","Nijlen","Ninove","Nokere","Noorderwijk","Noordschote","Nossegem","Nukerke","Oedelem","Oekene","Oelegem","Oeren","Oeselgem","Oetingen","Oevel","Okegem","Olen","Olmen","Olsene","Onkerzele","Onze-Lieve-Vrouw-Lombeek","Onze-Lieve-Vrouw-Waver","Ooigem","Ooike","Oombergen","Oorbeek","Oordegem","Oostakker","Oostduinkerke","Oosteeklo","Oostende","Oosterzele","Oostham","Oostkamp","Oostkerke","Oostmalle","Oostnieuwkerke","Oostrozebeke","Oostvleteren","Oostwinkel","Opbrakel","Opdorp","Opglabbeek","Opgrimbie","Ophasselt","Opheers","Ophoven","Opitter","Oplinter","Opoeteren","Oppuurs","Opvelp","Opwijk","Ordingen","Orsmaal-Gussenhoven","Otegem","Ottenburg","Ottergem","Oud-Heverlee","Oud-Turnhout","Oudegem","Oudekapelle","Oudenaarde","Oudenaken","Oudenburg","Outer","Outgaarden","Outrijve","Ouwegem","Overboelare","Overhespen","Overijse","Overmere","Overpelt","Overrepen","Overwinden","Paal","Pamel","Parike","Passendale","Paulatem","Peer","Pellenberg","Pepingen","Perk","Pervijze","Petegem-aan-de-Leie","Petegem-aan-de-Schelde","Peutie","Piringen","Pittem","Poederlee","Poeke","Poelkapelle","Poesele","Pollare","Pollinkhove","Poperinge","Poppel","Proven","Pulderbos","Pulle","Putte","Puurs","Ramsdonk","Ramsel","Ramskapelle","Ransberg","Ranst","Ravels","Reet","Rekem","Rekkem","Relegem","Remersdaal","Reninge","Reningelst","Reppel","Ressegem","Retie","Riemst","Rijkel","Rijkevorsel","Rijkhoven","Rijmenam","Riksingen","Rillaar","Roborst","Roesbrugge-Haringe","Roeselare","Roksem","Rollegem","Rollegem-Kapelle","Romershoven","Ronse","Ronsele","Roosbeek","Rosmeer","Rotem","Rotselaar","Rozebeke","Ruddervoorde","Ruien","Ruisbroek","Ruiselede","Rukkelingen-Loon","Rumbeke","Rummen","Rumsdorp","Rumst","Runkelen","Rupelmonde","Rutten","Schaffen","Schalkhoven","Schelderode","Scheldewindeke","Schelle","Schellebelle","Schendelbeke","Schepdaal","Scherpenheuvel","Schilde","Schoonaarde","Schore","Schorisse","Schoten","Schriek","Schuiferskapelle","Schulen","Semmerzake","Serskamp","Sijsele","Sinaai","Sint-Agatha-Rode","Sint-Amands","Sint-Amandsberg","Sint-Andries","Sint-Antelinks","Sint-Baafs-Vijve","Sint-Blasius-Boekel","Sint-Denijs","Sint-Denijs-Boekel","Sint-Denijs-Westrem","Sint-Eloois-Vijve","Sint-Eloois-Winkel","Sint-Genesius-Rode","Sint-Gillis-Waas","Sint-Gillis-bij-Dendermonde","Sint-Goriks-Oudenhove","Sint-Huibrechts-Hern","Sint-Huibrechts-Lille","Sint-Jacobskapelle","Sint-Jan","Sint-Jan-in-Eremo","Sint-Job-in-'t-Goor","Sint-Joris","Sint-Joris-Weert","Sint-Joris-Winge","Sint-Katelijne-Waver","Sint-Katherina-Lombeek","Sint-Kornelis-Horebeke","Sint-Kruis","Sint-Kruis-Winkel","Sint-Kwintens-Lennik","Sint-Lambrechts-Herk","Sint-Laureins","Sint-Laureins-Berchem","Sint-Lenaarts","Sint-Lievens-Esse","Sint-Lievens-Houtem","Sint-Margriete","Sint-Margriete-Houtem","Sint-Maria-Horebeke","Sint-Maria-Latem","Sint-Maria-Lierde","Sint-Maria-Oudenhove","Sint-Martens-Bodegem","Sint-Martens-Latem","Sint-Martens-Leerne","Sint-Martens-Lennik","Sint-Martens-Lierde","Sint-Martens-Voeren","Sint-Michiels","Sint-Niklaas","Sint-Pauwels","Sint-Pieters-Kapelle","Sint-Pieters-Leeuw","Sint-Pieters-Rode","Sint-Pieters-Voeren","Sint-Rijkers","Sint-Stevens-Woluwe","Sint-Truiden","Sint-Ulriks-Kapelle","Sleidinge","Slijpe","Sluizen","Smeerebbe-Vloerzegem","Smetlede","Snaaskerke","Snellegem","Spalbeek","Spiere","Stabroek","Staden","Stalhille","Stavele","Steendorp","Steenhuffel","Steenhuize-Wijnhuize","Steenkerke","Steenokkerzeel","Stekene","Stene","Sterrebeek","Stevoort","Stokkem","Stokrooie","Strijpen","Strijtem","Strombeek-Bever","Stuivekenskerke","Temse","Teralfene","Terhagen","Ternat","Tervuren","Tessenderlo","Testelt","Teuven","Tiegem","Tielen","Tielrode","Tielt","Tienen","Tildonk","Tisselt","Tollembeek","Tongeren","Tongerlo","Torhout","Tremelo","Turnhout","Uikhoven","Uitbergen","Uitkerke","Ulbeek","Ursel","Vaalbeek","Val-Meer","Varendonk","Varsenare","Vechmaal","Veerle","Veldegem","Veldwezelt","Velm","Veltem-Beisem","Velzeke-Ruddershove","Verrebroek","Vertrijk","Veulen","Veurne","Viane","Vichte","Viersel","Vilvoorde","Vinderhoute","Vinkem","Vinkt","Vissenaken","Vladslo","Vlamertinge","Vlekkem","Vlezenbeek","Vliermaal","Vliermaalroot","Vlierzele","Vlijtingen","Vlimmeren","Vlissegem","Volkegem","Vollezele","Voorde","Voormezele","Voort","Vorselaar","Vorsen","Vorst","Vosselaar","Vosselare","Vossem","Vrasene","Vremde","Vreren","Vroenhoven","Vucht","Vurste","Waanrode","Waarbeke","Waardamme","Waarloos","Waarmaarde","Waarschoot","Waasmont","Waasmunster","Wachtebeke","Wakken","Walem","Walsbets","Walshoutem","Waltwilder","Wambeek","Wange","Wannegem-Lede","Wanzele","Waregem","Waterland-Oudeman","Watervliet","Watou","Webbekom","Wechelderzande","Weelde","Weerde","Weert","Welden","Welle","Wellen","Wemmel","Wenduine","Werchter","Werken","Werm","Wervik","Wespelaar","Westende","Westerlo","Westkapelle","Westkerke","Westmalle","Westmeerbeek","Westouter","Westrem","Westrozebeke","Westvleteren","Wetteren","Wevelgem","Wezemaal","Wezembeek-Oppem","Wezeren","Wichelen","Widooie","Wiekevorst","Wielsbeke","Wieze","Wijchmaal","Wijer","Wijgmaal","Wijnegem","Wijshagen","Wijtschate","Wilderen","Willebringen","Willebroek","Wilrijk","Wilsele","Wilskerke","Wimmertingen","Wingene","Winksele","Wintershoven","Woesten","Wolvertem","Wommelgem","Wommersom","Wondelgem","Wontergem","Wortegem","Wortel","Woubrechtegem","Woumen","Wulpen","Wulvergem","Wulveringem","Wuustwezel","Zaffelare","Zandbergen","Zande","Zandhoven","Zandvoorde","Zarlardinge","Zarren","Zaventem","Zedelgem","Zegelsem","Zele","Zelem","Zellik","Zelzate","Zemst","Zepperen","Zerkegem","Zevekote","Zeveneken","Zeveren","Zevergem","Zichem","Zichen-Zussen-Bolder","Zillebeke","Zingem","Zoerle-Parwijs","Zoersel","Zolder","Zomergem","Zonhoven","Zonnebeke","Zonnegem","Zottegem","Zoutenaaie","Zoutleeuw","Zuidschote","Zuienkerke","Zulte","Zulzeke","Zutendaal","Zwevegem","Zwevezele","Zwijnaarde","Zwijndrecht"];var d=["gem","tem","vijve","zele"];var g=["####"];var p=["1e verdieping","2e verdieping","3e verdieping"];var b=["West-Vlaanderen","Oost-Vlaanderen","Vlaams-Brabant","Antwerpen","Limburg","Brussel"];var h=["WVL","OVL","VBR","ANT","LIM","BRU"];var c={normal:"{{location.street}} {{location.buildingNumber}}",full:"{{location.street}} {{location.buildingNumber}} {{location.secondaryAddress}}"};var L=["{{person.first_name.generic}}{{location.street_suffix}}","{{person.last_name.generic}}{{location.street_suffix}}"];var M=["straat","laan","weg","dreef","plein","park"];var j={building_number:m,city_pattern:u,city_prefix:k,city_suffix:d,postcode:g,secondary_address:p,state:b,state_abbr:h,street_address:c,street_pattern:L,street_suffix:M},f=j;var x={title:"Dutch (Belgium)",code:"nl_BE",country:"BE",language:"nl",endonym:"Nederlands (Belgi\xEB)",dir:"ltr",script:"Latn"},v=x;var B={generic:["Aaron","Adam","Alex","Alexander","Alice","Alicia","Aline","Amber","Amelie","Amina","Amira","Amy","Am\xE9lie","Anna","Arne","Arthur","Axel","Axelle","Aya","Bas","Bent","Bo","Bram","Brent","Camille","Cas","Charlotte","Chloe","Daan","David","Dries","Elena","Elias","Eline","Elisa","Elise","Ella","Emiel","Emile","Emily","Emma","Eva","Febe","Felix","Femke","Ferre","Fien","Finn","Fleur","Floor","Flore","Fran","Gabriel","Gilles","Gust","Hailey","Hamza","Hannah","Hanne","Helena","Ilias","Ilyas","Imran","Inaya","Ines","Jack","Jade","Jana","Janne","Jarne","Jasper","Jayden","Jef","Jelle","Jens","Jesse","Jolien","Jonas","Jules","Julia","Julie","Juliette","Juul","Kaat","Kasper","Kato","Kobe","Lana","Lander","Lara","Lars","Laura","Laure","Lena","Lenn","Lennert","Leon","Leonie","Lewis","Liam","Lien","Lieze","Lily","Lina","Linde","Lisa","Lise","Liv","Lize","Lore","Lotte","Lou","Louis","Louise","Lowie","Luca","Lucas","Lucie","Lukas","Luna","Manon","Margaux","Margot","Marie","Marthe","Mathias","Mathis","Mats","Matteo","Matthias","Maud","Maurice","Mauro","Maxim","Maxime","Maya","Merel","Miel","Mila","Milan","Milo","Mohamed","Mona","Nand","Nathan","Nicolas","Niels","Nina","Noa","Noah","Noor","Nora","Norah","Nore","Olivia","Oona","Oscar","Paulien","Pauline","Quinten","Rayan","Ren\xE9e","Robbe","Robin","Roos","Ruben","Rune","Sam","Sander","Sara","Sarah","Sem","Senne","Seppe","Siebe","Sien","Simon","Sofia","Stan","Sterre","Tess","Thibo","Thomas","Tibo","Tristan","Tuur","Vic","Victor","Victoria","Viktor","Vince","Wannes","Warre","Wout","Xander","Yana","Yasmine","Zoe","Zo\xEB"],female:["Alice","Alicia","Aline","Amber","Amelie","Amina","Amira","Amy","Am\xE9lie","Anna","Axelle","Aya","Bo","Camille","Charlotte","Chloe","Elena","Eline","Elisa","Elise","Ella","Emily","Emma","Eva","Febe","Femke","Fien","Fleur","Floor","Flore","Fran","Hailey","Hannah","Hanne","Helena","Inaya","Ines","Jade","Jana","Janne","Jolien","Julia","Julie","Juliette","Kaat","Kato","Lana","Lara","Laura","Laure","Lena","Leonie","Lien","Lieze","Lily","Lina","Linde","Lisa","Lise","Liv","Lize","Lore","Lotte","Louise","Lucie","Luna","Manon","Margaux","Margot","Marie","Marthe","Maud","Maya","Merel","Mila","Mona","Nina","Noa","Noor","Nora","Norah","Nore","Olivia","Oona","Paulien","Pauline","Ren\xE9e","Roos","Sara","Sarah","Sien","Sofia","Sterre","Tess","Victoria","Yana","Yasmine","Zoe","Zo\xEB"],male:["Aaron","Adam","Alex","Alexander","Arne","Arthur","Axel","Bas","Bent","Bram","Brent","Cas","Daan","David","Dries","Elias","Emiel","Emile","Felix","Ferre","Finn","Gabriel","Gilles","Gust","Hamza","Ilias","Ilyas","Imran","Jack","Jarne","Jasper","Jayden","Jef","Jelle","Jens","Jesse","Jonas","Jules","Juul","Kasper","Kobe","Lander","Lars","Lenn","Lennert","Leon","Lewis","Liam","Lou","Louis","Lowie","Luca","Lucas","Lukas","Mathias","Mathis","Mats","Matteo","Matthias","Maurice","Mauro","Maxim","Maxime","Miel","Milan","Milo","Mohamed","Nand","Nathan","Nicolas","Niels","Noah","Oscar","Quinten","Rayan","Robbe","Robin","Ruben","Rune","Sam","Sander","Sem","Senne","Seppe","Siebe","Simon","Stan","Thibo","Thomas","Tibo","Tristan","Tuur","Vic","Victor","Viktor","Vince","Wannes","Warre","Wout","Xander"]};var S={generic:["Claes","Claeys","Declerck","Declercq","Decock","Decoster","Desmet","Devos","Dewilde","Gielen","Goossens","Hermans","Jacobs","Janssen","Janssens","Lemmens","Maes","Martens","Mertens","Michiels","Peeters","Smet","Smets","Thijs","Vandamme","Vandenberghe","Vandenbroeck","Vandevelde","Verhaeghe","Verstraete","Willems","Wouters"]};var H={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var W=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:8}];var K={generic:["Dr.","Ing.","Ir.","Prof."],female:["Dr.","Ing.","Ir.","Prof."],male:["Dr.","Ing.","Ir.","Prof."]};var A=["MBA","Phd."];var P={first_name:B,last_name:S,last_name_pattern:H,name:W,prefix:K,suffix:A},D=P;var V=["###/######","###/## ## ##","### ## ## ##","###/### ###","##########","04##/### ###","04## ## ## ##","00324 ## ## ##","+324 ## ## ## ##"];var O=["+32#########","+32##########","+324########","+324######"];var N=["0### ## ## ##","##########","04## ## ## ##","4######"];var Z={human:V,international:O,national:N},z=Z;var y={format:z},E=y;var J={company:i,internet:s,location:f,metadata:v,person:D,phone_number:E},w= exports.a =J;var rr=new (0, _chunkZKNYQOPPcjs.n)({locale:[w,_chunkVT2XJBFXcjs.a,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = w; exports.b = rr;
