"use strict";Object.defineProperty(exports, "__esModule", {value: true});var _chunkCK6HCXEPcjs = require('./chunk-CK6HCXEP.cjs');var _chunkZKNYQOPPcjs = require('./chunk-ZKNYQOPP.cjs');var r=["082 ### ####","084 ### ####","083 ### ####","065 ### ####","082#######","082 #######"];var D={formats:r},i=D;var t=["CC","Ltd","Pty Ltd"];var C={legal_entity_type:t},o=C;var l=["co.za","com","info","net.za","org.za"];var E={domain_suffix:l},s=E;var m=["Polokwane","Johannesburg","Pretoria","Tshwane","Durban","Pietermaritzburg","Nelspruit","Kaapstad","Stellenbosch","Port Elizabeth","Oos-Londen","Kimberley","Rustenburg","Bloemfontein"];var d=["{{location.city_name}}"];var u=["#####","####"];var p=["Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Noord-Kaap","Noordwes","Oos-Kaap","Vrystaat","Wes-Kaap"];var f=["{{person.firstName}} {{location.street_suffix}}","{{person.lastName}} {{location.street_suffix}}"];var J={city_name:m,city_pattern:d,postcode:u,state:p,street_pattern:f},h=J;var L={title:"Afrikaans (South Africa)",code:"af_ZA",country:"ZA",language:"af",endonym:"Afrikaans (Suid-Afrika)",dir:"ltr",script:"Latn"},c=L;var y={generic:["Adam","Adriaan","Alan","Albert","Alexander","Alicia","Allen","Alma","Amanda","Amelia","Andre","Angelique","Angie","Anita","Anna","Annelie","Annette","Anthony","Antoinette","Audrey","Belinda","Bennie","Bernadette","Bernard","Bernice","Billy","Bobbie","Brenda","Brian","Bridgette","Cameron","Carla","Carmen","Cecil","Cecile","Charl","Charlene","Charlotte","Chris","Christopher","Cindy","Clara","Conrad","Craig","Dale","Daniel","Dante","David","Debbie","Dennis","Derek","Derrick","Duanne","Earl","Eddie","Edith","Edna","Edward","Elaine","Eleanor","Elisa","Ellen","Eloise","Elsa","Erik","Erika","Ernest","Estelle","Ethel","Eugene","Francois","Frankie","George","Gerald","Geraldine","Gerhard","Hannes","Harry","Harvey","Hazel","Heidi","Helen","Henrie","Herman","Hugo","Ian","Ida","Iwan","Jaco","Jacobus","Jacques","Jan","Jana","Janet","Jenna","Jenny","Jimmy","Joe","Johan","Johannes","Jolanda","Joshua","Juanita","Julian","Karen","Karl","Karla","Katrina","Katryn","Kayla","Kenneth","Kerry","Kevin","Kimberly","Krista","Kristie","Kristin","Kristina","Krystal","Leona","Leonard","Leticia","Linda","Lisa","Louis","Lucille","Lukas","Lydia","Lynda","Lynette","Margaret","Marie","Mark","Markus","Marlene","Martin","Mathuys","Maureen","Melanie","Melinda","Melissa","Michael","Micheal","Michele","Mike","Monica","Monique","Morne","Nadine","Natalie","Natasha","Neil","Nick","Nicoleen","Nina","Olivia","Oscar","Paul","Paula","Pauline","Phillip","Pieter","Raymond","Renette","Rhonda","Riaan","Richard","Rick","Rigard","Rita","Robert","Robin","Robyn","Roland","Ronald","Rosalie","Rosemarie","Roy","Ruben","Rudolph","Rudy","Rufus","Russell","Sandra","Sara","Shaun","Shawn","Simon","Sonia","Sonja","Stefanie","Stephaans","Stephen","Steve","Steven","Sue-Marie","Susan","Tanya","Tasha","Theo","Theunis","Theuns","Thomas","Tommie","Vanessa","Vernon","Vickie","Victor","Vincent","Wayne","Wesley","Wessel","Wilbur","Willem","William","Willie","Wilma","Yvette","Yvonne"],female:["Alicia","Alma","Amanda","Amelia","Angelique","Angie","Anita","Anna","Annelie","Annette","Antoinette","Audrey","Belinda","Bernadette","Bernice","Brenda","Bridgette","Carla","Carmen","Cecile","Charlene","Charlotte","Cindy","Clara","Debbie","Edith","Edna","Elaine","Eleanor","Elisa","Ellen","Eloise","Elsa","Erika","Estelle","Ethel","Geraldine","Hazel","Heidi","Helen","Ida","Jana","Janet","Jenna","Jenny","Jolanda","Juanita","Karen","Karla","Katrina","Katryn","Kayla","Kerry","Kimberly","Krista","Kristie","Kristin","Kristina","Krystal","Leona","Leticia","Linda","Lisa","Lucille","Lydia","Lynda","Lynette","Margaret","Marie","Marlene","Maureen","Melanie","Melinda","Melissa","Michele","Monica","Monique","Nadine","Natalie","Natasha","Nicoleen","Nina","Olivia","Paula","Pauline","Renette","Rhonda","Rita","Robin","Robyn","Rosalie","Rosemarie","Rosemary","Sandra","Sara","Sonia","Sonja","Stefanie","Sue-Marie","Susan","Tanya","Tasha","Vanessa","Vickie","Wilma","Yvette","Yvonne"],male:["Adam","Adriaan","Alan","Albert","Alexander","Allen","Andre","Anthony","Bennie","Bernard","Billy","Bobbie","Brian","Cameron","Cecil","Charl","Chris","Christopher","Conrad","Craig","Dale","Daniel","Dante","David","Dennis","Derek","Derrick","Duanne","Earl","Eddie","Edward","Erik","Ernest","Eugene","Francois","Frankie","George","Gerald","Gerhard","Hannes","Harry","Harvey","Henrie","Herman","Hugo","Ian","Iwan","Jaco","Jacobus","Jacques","Jan","Jimmy","Joe","Johan","Johannes","Joshua","Julian","Karl","Kenneth","Kevin","Leonard","Louis","Lukas","Mark","Markus","Martin","Mathuys","Michael","Micheal","Mike","Morne","Neil","Nick","Oscar","Paul","Phillip","Pieter","Raymond","Riaan","Richard","Rick","Rigard","Robert","Roland","Ronald","Roy","Ruben","Rudolph","Rudy","Rufus","Russell","Shaun","Shawn","Simon","Stephaans","Stephen","Steve","Steven","Theo","Theunis","Theuns","Thomas","Tommie","Vernon","Victor","Vincent","Wayne","Wesley","Wessel","Wilbur","Willem","William","Willie"]};var R={generic:["Ackerman","Albertyn","Aucamp","Badenhorst","Barnard","Basson","Bekker","Bester","Bezuidenhout","Bisset","Boje","Bosch","Boshoff","Bosman","Botha","Boyes","Bredenkamp","Brink","Brits","Burger","Carstens","Castelyn","Castens","Cilliers","Claasen","Cloete","Cronnje","Daniels","Delaney","Dippenaar","Donald","Duminy","Durand","Dyer","Eksteen","Els","Erasmus","Etzebeth","Ferreira","Fichardt","Fourie","Fuller","Gerber","Goosen","Grobelaar","Hanekom","Hattingh","Hertzog","Heunis","Hoffman","Hougaard","Human","Immelman","Jansen van Rensburg","Jantjies","Jordaan","Joubert","Kallis","Kirsten","Knoetze","Koen","Kriel","Krige","Kruger","Langeveldt","Laubscher","Lochner","Lombard","Lotter","Loubser","Louw","Luyt","Malan","Marais","Meintjies","Melker","Mellett","Meyer","Morkel","Mostert","Myburg","Myburgh","Naude","Nel","Olivier","Oosthuizen","Oppenheimer","Paulse","Philander","Pienaar","Pieterse","Potgieter","Powell","Pretorius","Pringle","Prinsloo","Rens","Richter","Roberts","Roos","Rossouw","Schmidt","Schoeman","Scholtz","Serfontein","Smit","Snedden","Snell","Snyman","Spies","Steenkamp","Stegmann","Steyn","Strauss","Strydon","Styger","Swart","Theron","Theunissen","Tromp","Truter","Uys","Veldsman","Venter","Versfeld","Viljoen","Visagie","Visser","Viviers","Vogel","Vosloo","Wagenaar","Wentzel","Wessels","Wolmerans","Zimmerman","de Bruyn","de Jager","de Klerk","de Kock","de Villiers","de Vos","de Waal","de Wet","du Plessis","du Preez","le Roux","van Buuren","van Deventer","van Heerden","van Jaarsveld","van Niekerk","van Renen","van Rooyen","van Staden","van Zyl","van de Heefer","van de Merwe","van den Berg","van der Bijl","van der Hoff","van der Westhuyzen"]};var M={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var v={first_name:y,last_name:R,last_name_pattern:M},g=v;var A=["01# ### #####","02# ### #####","03# ### #####","04# ### #####","05# ### #####","0800 ### ###","0860 ### ###","01#########","01# ########"];var b=["+271#########","+272#########","+273#########","+274#########","+275#########","+27800######","+27860######"];var S=["1#########","2#########","3#########","4#########","5#########","080 0## ####","0860 ### ###"];var P={human:A,international:b,national:S},K=P;var x={format:K},k=x;var H={cell_phone:i,company:o,internet:s,location:h,metadata:c,person:g,phone_number:k},B= exports.a =H;var Le=new (0, _chunkZKNYQOPPcjs.n)({locale:[B,_chunkCK6HCXEPcjs.a,_chunkZKNYQOPPcjs.o]});exports.a = B; exports.b = Le;
