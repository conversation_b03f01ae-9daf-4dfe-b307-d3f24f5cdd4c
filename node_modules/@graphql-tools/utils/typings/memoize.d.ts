export declare function memoize1<F extends (a1: any) => any>(fn: F): F;
export declare function memoize2<F extends (a1: any, a2: any) => any>(fn: F): F;
export declare function memoize3<F extends (a1: any, a2: any, a3: any) => any>(fn: F): F;
export declare function memoize4<F extends (a1: any, a2: any, a3: any, a4: any) => any>(fn: F): F;
export declare function memoize5<F extends (a1: any, a2: any, a3: any, a4: any, a5: any) => any>(fn: F): F;
export declare function memoize2of4<F extends (a1: any, a2: any, a3: any, a4: any) => any>(fn: F): F;
export declare function memoize2of5<F extends (a1: any, a2: any, a3: any, a4: any, a5: any) => any>(fn: F): F;
