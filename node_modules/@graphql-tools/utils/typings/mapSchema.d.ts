import { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLObjectType, GraphQLSchema } from 'graphql';
import { SchemaMapper } from './Interfaces.js';
export declare function mapSchema(schema: GraphQLSchema, schemaMapper?: SchemaMapper): GraphQLSchema;
export declare function correctASTNodes(type: GraphQLObjectType): GraphQLObjectType;
export declare function correctASTNodes(type: GraphQLInterfaceType): GraphQLInterfaceType;
export declare function correctASTNodes(type: GraphQLInputObjectType): GraphQLInputObjectType;
export declare function correctASTNodes(type: GraphQLEnumType): GraphQLEnumType;
