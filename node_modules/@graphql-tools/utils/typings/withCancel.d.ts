export declare function getAsyncIteratorWithCancel<T, TReturn = any>(asyncIterator: AsyncIterator<T>, onCancel: (value?: TReturn) => void | Promise<void>): AsyncIterator<T>;
export declare function getAsyncIterableWithCancel<T, TAsyncIterable extends AsyncIterable<T>, TReturn = any>(asyncIterable: TAsyncIterable, onCancel: (value?: TReturn) => void | Promise<void>): TAsyncIterable;
export { getAsyncIterableWithCancel as withCancel };
