import { DirectiveDefinitionNode, UnionTypeDefinitionNode, UnionTypeExtensionNode } from 'graphql';
import { Config } from './merge-typedefs.cjs';
export declare function mergeUnion(first: UnionTypeDefinitionNode | UnionTypeExtensionNode, second: UnionTypeDefinitionNode | UnionTypeExtensionNode, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): UnionTypeDefinitionNode | UnionTypeExtensionNode;
