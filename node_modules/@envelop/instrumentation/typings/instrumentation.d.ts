import { MaybePromise } from '@whatwg-node/promise-helpers';
export type GenericInstrumentation = Record<string, (payload: any, wrapped: () => MaybePromise<void>) => MaybePromise<void>>;
/**
 * Composes 2 instrumentations together into one instrumentation.
 * The first one will be the outer call, the second one the inner call.
 */
export declare function chain<First extends GenericInstrumentation, Next extends GenericInstrumentation>(first: First, next: Next): First & Next;
/**
 * Composes a list of instrumentation together into one instrumentation object.
 * The order of execution will respect the order of the array,
 * the first one being the outter most call, the last one the inner most call.
 */
export declare function composeInstrumentation<T extends GenericInstrumentation>(instrumentation: T[]): T | undefined;
/**
 * Extract instrumentation from a list of plugins.
 * It returns instrumentation found, and the list of plugins without their instrumentation.
 *
 * You can use this to easily customize the composition of the instrumentation if the default one
 * doesn't suits your needs.
 */
export declare function getInstrumentationAndPlugin<T, P extends {
    instrumentation?: T;
}>(plugins: P[]): {
    pluginInstrumentation: T[];
    plugins: Omit<P, 'instrumentation'>[];
};
/**
 * A helper to instrument a function.
 *
 * @param payload: The first argument that will be passed to the instrumentation on each function call
 * @returns Function and Async Functions factories allowing to wrap a function with a given instrument.
 */
export declare const getInstrumented: <TPayload>(payload: TPayload) => {
    /**
     * Wraps the `wrapped` function with the given `instrument` wrapper.
     * @returns The wrapped function, or `undefined` if the instrument is `undefined`.
     */
    fn<TResult, TArgs extends any[]>(instrument: ((payload: TPayload, wrapped: () => void) => void) | undefined, wrapped: (...args: TArgs) => TResult): (...args: TArgs) => TResult;
    /**
     * Wraps the `wrapped` function with the given `instrument` wrapper.
     * @returns The wrapped function, or `undefined` if the instrument is `undefined`.
     */
    asyncFn<TResult, TArgs_1 extends any[]>(instrument: ((payload: TPayload, wrapped: () => MaybePromise<void>) => MaybePromise<void>) | undefined, wrapped: (...args: TArgs_1) => MaybePromise<TResult>): (...args: TArgs_1) => MaybePromise<TResult>;
};
