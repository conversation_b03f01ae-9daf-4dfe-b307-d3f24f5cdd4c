import { PromiseOrValue } from '@envelop/core';
import type { GraphQLSchemaWithContext, YogaInitialContext } from '../types.js';
import type { Plugin } from './types.js';
export type YogaSchemaDefinition<TServerContext, TUserContext> = PromiseOrValue<GraphQLSchemaWithContext<TServerContext & YogaInitialContext & TUserContext>> | ((context: TServerContext & {
    request: YogaInitialContext['request'];
}) => PromiseOrValue<GraphQLSchemaWithContext<TServerContext & YogaInitialContext & TUserContext>>);
export declare const useSchema: <TServerContext = {}, TUserContext = {}>(schemaDef?: YogaSchemaDefinition<TServerContext, TUserContext>) => Plugin<YogaInitialContext & TServerContext>;
