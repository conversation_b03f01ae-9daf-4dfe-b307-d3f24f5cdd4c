import type { DocumentNode, validate } from 'graphql';
import type { Plugin } from './types.js';
interface Cache<T> {
    get(key: string): T | undefined;
    set(key: string, value: T): void;
}
export interface ParserAndValidationCacheOptions {
    documentCache?: Cache<DocumentNode>;
    errorCache?: Cache<unknown>;
    validationCache?: boolean | Cache<typeof validate>;
}
export declare function useParserAndValidationCache({ documentCache, errorCache, validationCache, }: ParserAndValidationCacheOptions): Plugin<{}>;
export {};
