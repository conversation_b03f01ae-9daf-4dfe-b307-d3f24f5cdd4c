import { LRUCache as LRU } from 'lru-cache';
/**
 * @deprecated In the next major, `createLRUCache` will be renamed to `_createLRUCache`,
 * and the current `createLRUCache` will be removed.
 */
export declare const createLRUCache: typeof _createLRUCache;
/**
 * @deprecated In the next major, `LRUCacheOptions` will be renamed to `_LRUCacheOptions`,
 * and the current `LRUCacheOptions` will be removed.
 */
export type LRUCacheOptions = _LRUCacheOptions;
/**
 * @deprecated In the next major, `LRUCache` will be renamed to `_LRUCache`,
 * and the current `LRUCache` will be removed.
 */
export type LRUCache<T extends {}> = _LRUCache<T>;
/**
 * @internal This is an internal utility, and you should use it with your own risk.
 * This utility can have breaking changes in the future.
 */
export type _LRUCache<T extends {}> = LRU<string, T>;
/**
 * @internal This is an internal utility, and you should use it with your own risk.
 * This utility can have breaking changes in the future.
 */
export interface _LRUCacheOptions {
    max?: number;
    ttl?: number;
}
/**
 * @internal This is an internal utility, and you should use it with your own risk.
 * This utility can have breaking changes in the future.
 */
export declare function _createLRUCache<T extends {}>({ max, ttl, }?: _LRUCacheOptions): LRU<string, T, unknown>;
