{"name": "graphql-yoga", "version": "5.13.5", "description": "", "sideEffects": false, "peerDependencies": {"graphql": "^15.2.0 || ^16.0.0"}, "dependencies": {"@envelop/core": "^5.2.3", "@envelop/instrumentation": "^1.0.0", "@graphql-tools/executor": "^1.4.0", "@graphql-tools/schema": "^10.0.11", "@graphql-tools/utils": "^10.6.2", "@whatwg-node/fetch": "^0.10.6", "@whatwg-node/promise-helpers": "^1.2.4", "@whatwg-node/server": "^0.10.5", "dset": "^3.1.4", "lru-cache": "^10.0.0", "tslib": "^2.8.1", "@graphql-yoga/logger": "^2.0.1", "@graphql-yoga/subscription": "^5.0.5"}, "repository": {"type": "git", "url": "https://github.com/graphql-hive/graphql-yoga.git", "directory": "packages/graphql-yoga"}, "keywords": ["graphql", "server", "api", "graphql-server"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <saihaj<PERSON><PERSON>.<EMAIL>> (https://saihaj.dev/)", "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}