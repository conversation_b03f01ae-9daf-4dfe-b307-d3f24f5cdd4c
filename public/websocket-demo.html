<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mirage WebSocket Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .control-group h3 {
            margin-top: 0;
            color: #495057;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        textarea { height: 100px; resize: vertical; }
        
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent { background: #e3f2fd; }
        .message.received { background: #f3e5f5; }
        .message.error { background: #ffebee; color: #c62828; }
        .message.system { background: #e8f5e8; color: #2e7d32; }
        
        .timestamp {
            color: #666;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Mirage WebSocket Demo</h1>
            <p>Real-time Mock Data Generation via WebSocket</p>
        </div>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>Connection</h3>
                <button id="connectBtn" onclick="connect()">Connect</button>
                <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
                <button onclick="ping()">Ping Server</button>
                <button onclick="getInfo()">Get Info</button>
            </div>
            
            <div class="control-group">
                <h3>Mock Data Generation</h3>
                <textarea id="mockStructure" placeholder="Enter JSON structure">
{
  "id": "{{guid}}",
  "name": "{{person.fullName}}",
  "email": "{{internet.email}}",
  "age": "{{integer(18, 65)}}"
}</textarea>
                <input type="number" id="recordCount" value="1" min="1" max="100" placeholder="Number of records">
                <button onclick="generateMockData()">Generate Mock Data</button>
                <button onclick="generateStream()">Start Stream (10s)</button>
            </div>
            
            <div class="control-group">
                <h3>Rooms & Subscriptions</h3>
                <input type="text" id="roomName" placeholder="Room name" value="demo-room">
                <button onclick="joinRoom()">Join Room</button>
                <button onclick="leaveRoom()">Leave Room</button>
                <br>
                <input type="text" id="subscriptionTopic" placeholder="Subscription topic" value="user-updates">
                <button onclick="subscribe()">Subscribe</button>
                <button onclick="unsubscribe()">Unsubscribe</button>
            </div>
            
            <div class="control-group">
                <h3>Broadcasting</h3>
                <textarea id="broadcastMessage" placeholder="Message to broadcast">
{
  "type": "custom",
  "event": "announcement",
  "data": {
    "message": "Hello from WebSocket demo!"
  }
}</textarea>
                <button onclick="broadcast()">Broadcast to All</button>
                <button onclick="broadcastToRoom()">Broadcast to Room</button>
            </div>
        </div>
        
        <div class="container">
            <h3>Messages</h3>
            <button onclick="clearMessages()">Clear Messages</button>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = status;
            statusEl.className = `status ${className}`;
        }
        
        function addMessage(content, type = 'received') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `
                <div class="timestamp">${timestamp}</div>
                <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
            `;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) return;
            
            updateStatus('Connecting...', 'connecting');
            
            try {
                ws = new WebSocket('ws://localhost:3000/ws');
                
                ws.onopen = function(event) {
                    updateStatus('Connected', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    reconnectAttempts = 0;
                    addMessage('WebSocket connected successfully', 'system');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage(message, 'received');
                    } catch (e) {
                        addMessage(event.data, 'received');
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('Disconnected', 'disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    addMessage(`WebSocket closed: ${event.code} - ${event.reason}`, 'system');
                    
                    // Auto-reconnect
                    if (reconnectAttempts < maxReconnectAttempts) {
                        setTimeout(() => {
                            reconnectAttempts++;
                            addMessage(`Reconnection attempt ${reconnectAttempts}/${maxReconnectAttempts}`, 'system');
                            connect();
                        }, 2000 * reconnectAttempts);
                    }
                };
                
                ws.onerror = function(error) {
                    addMessage(`WebSocket error: ${error}`, 'error');
                };
                
            } catch (error) {
                updateStatus('Connection failed', 'disconnected');
                addMessage(`Connection failed: ${error}`, 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close(1000, 'Manual disconnect');
            }
        }
        
        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const messageStr = JSON.stringify(message);
                ws.send(messageStr);
                addMessage(message, 'sent');
                return true;
            } else {
                addMessage('WebSocket not connected', 'error');
                return false;
            }
        }
        
        function ping() {
            sendMessage({
                type: 'system',
                event: 'ping',
                data: {}
            });
        }
        
        function getInfo() {
            sendMessage({
                type: 'system',
                event: 'getInfo',
                data: {}
            });
        }
        
        function generateMockData() {
            try {
                const structure = JSON.parse(document.getElementById('mockStructure').value);
                const records = parseInt(document.getElementById('recordCount').value) || 1;
                
                sendMessage({
                    type: 'mock',
                    event: 'generate',
                    data: {
                        app: 'websocket-demo',
                        endpoint: 'generate',
                        structure: structure,
                        records: records
                    }
                });
            } catch (error) {
                addMessage(`Invalid JSON structure: ${error}`, 'error');
            }
        }
        
        function generateStream() {
            try {
                const structure = JSON.parse(document.getElementById('mockStructure').value);
                
                sendMessage({
                    type: 'mock',
                    event: 'generateStream',
                    data: {
                        app: 'websocket-demo',
                        endpoint: 'stream',
                        structure: structure,
                        interval: 1000,
                        duration: 10000
                    }
                });
            } catch (error) {
                addMessage(`Invalid JSON structure: ${error}`, 'error');
            }
        }
        
        function joinRoom() {
            const roomName = document.getElementById('roomName').value;
            if (roomName) {
                sendMessage({
                    type: 'room',
                    event: 'join',
                    data: { roomName: roomName }
                });
            }
        }
        
        function leaveRoom() {
            const roomName = document.getElementById('roomName').value;
            if (roomName) {
                sendMessage({
                    type: 'room',
                    event: 'leave',
                    data: { roomName: roomName }
                });
            }
        }
        
        function subscribe() {
            try {
                const topic = document.getElementById('subscriptionTopic').value;
                const structure = JSON.parse(document.getElementById('mockStructure').value);
                
                if (topic) {
                    sendMessage({
                        type: 'mock',
                        event: 'subscribe',
                        data: {
                            topic: topic,
                            structure: structure,
                            interval: 5000
                        }
                    });
                }
            } catch (error) {
                addMessage(`Invalid JSON structure: ${error}`, 'error');
            }
        }
        
        function unsubscribe() {
            const topic = document.getElementById('subscriptionTopic').value;
            if (topic) {
                sendMessage({
                    type: 'mock',
                    event: 'unsubscribe',
                    data: { topic: topic }
                });
            }
        }
        
        function broadcast() {
            try {
                const message = JSON.parse(document.getElementById('broadcastMessage').value);
                sendMessage({
                    type: 'system',
                    event: 'broadcast',
                    data: {
                        broadcastMessage: message,
                        excludeSelf: true
                    }
                });
            } catch (error) {
                addMessage(`Invalid JSON message: ${error}`, 'error');
            }
        }
        
        function broadcastToRoom() {
            try {
                const roomName = document.getElementById('roomName').value;
                const message = JSON.parse(document.getElementById('broadcastMessage').value);
                
                if (roomName) {
                    sendMessage({
                        type: 'system',
                        event: 'broadcastToRoom',
                        data: {
                            roomName: roomName,
                            broadcastMessage: message,
                            excludeSelf: true
                        }
                    });
                }
            } catch (error) {
                addMessage(`Invalid JSON message: ${error}`, 'error');
            }
        }
        
        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            connect();
        };
    </script>
</body>
</html>
