<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mirage - Dynamic Mock API Server Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        code {
            background-color: #f8f8f8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .content {
            flex: 1;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .example {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .note {
            background-color: #fff8dc;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #f1c40f;
        }
        .nav {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .nav ul {
            list-style-type: none;
            padding: 0;
        }
        .nav ul li {
            display: inline-block;
            margin-right: 15px;
        }
        .nav a {
            text-decoration: none;
            color: #3498db;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <h1>Mirage - Dynamic Mock API Server Documentation</h1>

            <div class="nav">
                <ul>
                    <li><a href="#overview">Overview</a></li>
                    <li><a href="#getting-started">Getting Started</a></li>
                    <li><a href="#rest-api">REST API</a></li>
                    <li><a href="#graphql-api">GraphQL API</a></li>
                    <li><a href="#websocket-api">WebSocket API</a></li>
                    <li><a href="#template-syntax">Template Syntax</a></li>
                    <li><a href="#available-helpers">Available Helpers</a></li>
                    <li><a href="#examples">Examples</a></li>
                    <li><a href="#advanced-usage">Advanced Usage</a></li>
                </ul>
            </div>

            <section id="overview">
                <h2>Overview</h2>
                <p>
                    Mirage is a dynamic mock API server that allows you to generate realistic mock data based on your own defined structure.
                    It uses Faker.js to create realistic data for testing, development, and demonstration purposes.
                </p>
                <p>
                    Unlike traditional mock servers with predefined endpoints and data structures, Mirage allows you to define your own
                    data structure on-the-fly and generates mock data based on that structure. This makes it extremely flexible and
                    suitable for a wide range of use cases.
                </p>
                <p>
                    Mirage supports <strong>REST API</strong>, <strong>GraphQL API</strong>, and <strong>WebSocket API</strong> interfaces,
                    giving you the flexibility to choose the API style that best fits your application's needs, including real-time
                    data generation and streaming.
                </p>
            </section>

            <section id="getting-started">
                <h2>Getting Started</h2>
                <h3>Installation</h3>
                <pre><code>
# Clone the repository
git clone &lt;repository-url&gt;
cd mirage

# Install dependencies
npm install

# Start the server
npm start
                </code></pre>
                <p>
                    The server will start on port 3000 by default. You can change this by setting the <code>PORT</code> environment variable.
                </p>
            </section>

            <section id="rest-api">
                <h2>REST API</h2>
                <p>
                    Mirage provides a dynamic REST endpoint that can be used to generate mock data:
                </p>
                <pre><code>POST /api/mock/:app/:endpoint</code></pre>
                <p>
                    Where:
                </p>
                <ul>
                    <li><code>:app</code> - Any application name (e.g., users, products, orders)</li>
                    <li><code>:endpoint</code> - Any endpoint name (e.g., list, details, search)</li>
                </ul>

                <h3>Request Body</h3>
                <p>
                    The request body should contain a <code>structure</code> object that defines the structure of the mock data to be generated:
                </p>
                <pre><code>{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}",
    "createdAt": "{{date.past}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    }
  }
}</code></pre>

                <h3>Query Parameters</h3>
                <p>
                    <code>records</code> - Number of records to generate (default: 1)
                </p>
                <p>
                    Example: <code>/api/mock/users/list?records=5</code>
                </p>

                <h3>Response</h3>
                <p>
                    The response will contain the generated mock data based on the provided structure:
                </p>
                <pre><code>{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 32,
    "isActive": true,
    "createdAt": "2023-01-15T08:30:00.000Z",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA"
    }
  }
}</code></pre>
            </section>

            <section id="graphql-api">
                <h2>GraphQL API</h2>
                <p>
                    Mirage also provides a GraphQL endpoint for more flexible data querying and manipulation:
                </p>
                <pre><code>POST /graphql</code></pre>

                <h3>GraphQL Playground</h3>
                <p>
                    You can access the interactive GraphQL playground at:
                </p>
                <pre><code>GET /graphql</code></pre>

                <h3>Available Operations</h3>

                <h4>Query: getMock</h4>
                <p>
                    Use the <code>getMock</code> query for read-only mock data generation:
                </p>
                <pre><code>query {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
      age: "{{integer(18, 65)}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}</code></pre>

                <h4>Mutation: postMock</h4>
                <p>
                    Use the <code>postMock</code> mutation for creating new mock data:
                </p>
                <pre><code>mutation {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      name: "{{person.fullName}}"
      email: "{{internet.email()}}"
      company: {
        name: "{{company.name().toUpperCase()}}"
      }
    }
    records: 1
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}</code></pre>

                <h3>Input Parameters</h3>
                <table>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Required</th>
                        <th>Description</th>
                    </tr>
                    <tr>
                        <td><code>app</code></td>
                        <td>String</td>
                        <td>Yes</td>
                        <td>Application name (e.g., users, products, orders)</td>
                    </tr>
                    <tr>
                        <td><code>endpoint</code></td>
                        <td>String</td>
                        <td>Yes</td>
                        <td>Endpoint name (e.g., list, details, search)</td>
                    </tr>
                    <tr>
                        <td><code>structure</code></td>
                        <td>JSON</td>
                        <td>Yes</td>
                        <td>Object or Array defining the data structure with faker templates</td>
                    </tr>
                    <tr>
                        <td><code>records</code></td>
                        <td>Integer</td>
                        <td>No</td>
                        <td>Number of records to generate (default: 1)</td>
                    </tr>
                </table>

                <h3>Response Types</h3>
                <p>
                    GraphQL responses use union types to handle both success and error cases:
                </p>
                <ul>
                    <li><strong>MockDataResponse</strong>: Successful response with generated data</li>
                    <li><strong>Error</strong>: Error response with error message and details</li>
                </ul>
            </section>

            <section id="websocket-api">
                <h2>WebSocket API</h2>
                <p>
                    Mirage provides a real-time WebSocket API for bidirectional communication and live data streaming:
                </p>
                <pre><code>WebSocket Connection: ws://localhost:3000/ws</code></pre>

                <h3>WebSocket Demo</h3>
                <p>
                    You can access the interactive WebSocket demo at:
                </p>
                <pre><code><a href="/ws-demo" target="_blank">GET /ws-demo</a></code></pre>

                <h3>Message Format</h3>
                <p>
                    All WebSocket messages use a standardized JSON format:
                </p>
                <pre><code>{
  "type": "mock|system|room|namespace",
  "event": "specific_event_name",
  "data": {
    // Event-specific data
  }
}</code></pre>

                <h3>Available Event Types</h3>

                <h4>Mock Events</h4>
                <table>
                    <tr>
                        <th>Event</th>
                        <th>Description</th>
                        <th>Data Parameters</th>
                    </tr>
                    <tr>
                        <td><code>generate</code></td>
                        <td>Generate single mock data</td>
                        <td>structure, records, app, endpoint</td>
                    </tr>
                    <tr>
                        <td><code>generateBatch</code></td>
                        <td>Generate multiple mock data sets</td>
                        <td>requests (array of generation requests)</td>
                    </tr>
                    <tr>
                        <td><code>generateStream</code></td>
                        <td>Start real-time data streaming</td>
                        <td>structure, interval, duration</td>
                    </tr>
                    <tr>
                        <td><code>subscribe</code></td>
                        <td>Subscribe to automatic updates</td>
                        <td>topic, structure, interval</td>
                    </tr>
                    <tr>
                        <td><code>unsubscribe</code></td>
                        <td>Unsubscribe from updates</td>
                        <td>topic</td>
                    </tr>
                    <tr>
                        <td><code>validateTemplate</code></td>
                        <td>Validate data structure template</td>
                        <td>structure</td>
                    </tr>
                    <tr>
                        <td><code>getTemplates</code></td>
                        <td>Get available Faker.js templates</td>
                        <td>None</td>
                    </tr>
                </table>

                <h4>System Events</h4>
                <table>
                    <tr>
                        <th>Event</th>
                        <th>Description</th>
                        <th>Data Parameters</th>
                    </tr>
                    <tr>
                        <td><code>ping</code></td>
                        <td>Health check ping</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td><code>getInfo</code></td>
                        <td>Get connection information</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td><code>getMetrics</code></td>
                        <td>Get server metrics</td>
                        <td>None</td>
                    </tr>
                    <tr>
                        <td><code>broadcast</code></td>
                        <td>Broadcast to all connections</td>
                        <td>broadcastMessage, excludeSelf</td>
                    </tr>
                    <tr>
                        <td><code>broadcastToRoom</code></td>
                        <td>Broadcast to specific room</td>
                        <td>roomName, broadcastMessage, excludeSelf</td>
                    </tr>
                </table>

                <h4>Room Events</h4>
                <table>
                    <tr>
                        <th>Event</th>
                        <th>Description</th>
                        <th>Data Parameters</th>
                    </tr>
                    <tr>
                        <td><code>join</code></td>
                        <td>Join a communication room</td>
                        <td>roomName</td>
                    </tr>
                    <tr>
                        <td><code>leave</code></td>
                        <td>Leave a communication room</td>
                        <td>roomName</td>
                    </tr>
                </table>

                <h3>WebSocket Examples</h3>

                <h4>Generate Mock Data</h4>
                <pre><code>{
  "type": "mock",
  "event": "generate",
  "data": {
    "structure": {
      "id": "{{guid}}",
      "name": "{{person.fullName}}",
      "email": "{{internet.email}}"
    },
    "records": 3
  }
}</code></pre>

                <h4>Start Data Streaming</h4>
                <pre><code>{
  "type": "mock",
  "event": "generateStream",
  "data": {
    "structure": {
      "timestamp": "{{date.recent}}",
      "value": "{{integer(1, 100)}}"
    },
    "interval": 1000,
    "duration": 10000
  }
}</code></pre>

                <h4>Join Room</h4>
                <pre><code>{
  "type": "room",
  "event": "join",
  "data": {
    "roomName": "analytics-room"
  }
}</code></pre>

                <h4>Subscribe to Updates</h4>
                <pre><code>{
  "type": "mock",
  "event": "subscribe",
  "data": {
    "topic": "user-updates",
    "structure": {
      "id": "{{guid}}",
      "status": "{{random(\"online\", \"offline\")}}"
    }
  }
}</code></pre>

                <h3>WebSocket Features</h3>
                <ul>
                    <li><strong>Real-time Data Generation</strong>: Generate mock data instantly</li>
                    <li><strong>Data Streaming</strong>: Continuous data streams with configurable intervals</li>
                    <li><strong>Room-based Communication</strong>: Join/leave rooms for group messaging</li>
                    <li><strong>Subscription System</strong>: Subscribe to automatic data updates</li>
                    <li><strong>Heartbeat Monitoring</strong>: Automatic connection health checks</li>
                    <li><strong>Binary Data Support</strong>: Support for binary message formats</li>
                    <li><strong>Message Compression</strong>: Automatic message compression for performance</li>
                    <li><strong>Automatic Reconnection</strong>: Client-side reconnection with exponential backoff</li>
                </ul>

                <h3>JavaScript Client Example</h3>
                <pre><code>// Connect to WebSocket
const ws = new WebSocket('ws://localhost:3000/ws');

ws.onopen = function(event) {
  console.log('Connected to WebSocket');

  // Generate mock data
  ws.send(JSON.stringify({
    type: 'mock',
    event: 'generate',
    data: {
      structure: {
        id: '{{guid}}',
        name: '{{person.fullName}}'
      },
      records: 5
    }
  }));
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
};

ws.onclose = function(event) {
  console.log('WebSocket closed');
};

ws.onerror = function(error) {
  console.error('WebSocket error:', error);
};</code></pre>
            </section>

            <section id="template-syntax">
                <h2>Template Syntax</h2>
                <p>
                    The structure object supports the following template syntax:
                </p>

                <h3>Basic Syntax</h3>
                <p>
                    Use double curly braces to indicate a template:
                </p>
                <pre><code>{{helperName}}</code></pre>

                <h3>With Parameters</h3>
                <pre><code>{{helperName(param1, param2, ...)}}</code></pre>

                <h3>Nested Properties</h3>
                <pre><code>{{category.subcategory}}</code></pre>

                <div class="note">
                    <strong>Note:</strong> All templates must be enclosed in quotes when used in JSON, e.g., <code>"{{guid}}"</code>
                </div>
            </section>

            <section id="available-helpers">
                <h2>Available Helpers</h2>

                <h3>ID Generators</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>objectId</code></td>
                        <td>Generate a MongoDB ObjectId</td>
                        <td><code>"{{objectId}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>guid</code></td>
                        <td>Generate a UUID</td>
                        <td><code>"{{guid}}"</code></td>
                    </tr>
                </table>

                <h3>Boolean Generator</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>boolean</code></td>
                        <td>Generate a random boolean value</td>
                        <td><code>"{{boolean}}"</code></td>
                    </tr>
                </table>

                <h3>Person Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>person.firstName</code></td>
                        <td>Generate a random first name</td>
                        <td><code>"{{person.firstName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.lastName</code></td>
                        <td>Generate a random last name</td>
                        <td><code>"{{person.lastName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.fullName</code></td>
                        <td>Generate a random full name</td>
                        <td><code>"{{person.fullName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.gender</code></td>
                        <td>Generate a random gender</td>
                        <td><code>"{{person.gender}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.jobTitle</code></td>
                        <td>Generate a random job title</td>
                        <td><code>"{{person.jobTitle}}"</code></td>
                    </tr>
                </table>

                <h3>Internet Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>internet.email</code></td>
                        <td>Generate a random email address</td>
                        <td><code>"{{internet.email}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.userName</code></td>
                        <td>Generate a random username</td>
                        <td><code>"{{internet.userName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.url</code></td>
                        <td>Generate a random URL</td>
                        <td><code>"{{internet.url}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.ip</code></td>
                        <td>Generate a random IP address</td>
                        <td><code>"{{internet.ip}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.password</code></td>
                        <td>Generate a random password</td>
                        <td><code>"{{internet.password}}"</code></td>
                    </tr>
                </table>

                <h3>Location Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>location.street</code></td>
                        <td>Generate a random street address</td>
                        <td><code>"{{location.street}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.city</code></td>
                        <td>Generate a random city</td>
                        <td><code>"{{location.city}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.state</code></td>
                        <td>Generate a random state</td>
                        <td><code>"{{location.state}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.country</code></td>
                        <td>Generate a random country</td>
                        <td><code>"{{location.country}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.zipCode</code></td>
                        <td>Generate a random zip code</td>
                        <td><code>"{{location.zipCode}}"</code></td>
                    </tr>
                </table>

                <h3>Date</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>date.past</code></td>
                        <td>Generate a date in the past</td>
                        <td><code>"{{date.past}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>date.future</code></td>
                        <td>Generate a date in the future</td>
                        <td><code>"{{date.future}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>date.recent</code></td>
                        <td>Generate a recent date</td>
                        <td><code>"{{date.recent}}"</code></td>
                    </tr>
                </table>

                <h3>Numbers</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>integer(min, max)</code></td>
                        <td>Generate a random integer between min and max</td>
                        <td><code>"{{integer(1, 100)}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>floating(min, max)</code></td>
                        <td>Generate a random float between min and max</td>
                        <td><code>"{{floating(1, 100)}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>amount(min, max, decimals)</code></td>
                        <td>Generate a random amount with specified decimals</td>
                        <td><code>"{{amount(10, 1000, 2)}}"</code></td>
                    </tr>
                </table>

                <h3>Commerce</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>commerce.productName</code></td>
                        <td>Generate a random product name</td>
                        <td><code>"{{commerce.productName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.productDescription</code></td>
                        <td>Generate a random product description</td>
                        <td><code>"{{commerce.productDescription}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.price</code></td>
                        <td>Generate a random price</td>
                        <td><code>"{{commerce.price}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.department</code></td>
                        <td>Generate a random department name</td>
                        <td><code>"{{commerce.department}}"</code></td>
                    </tr>
                </table>

                <h3>Arrays and Objects</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>random(value1, value2, ...)</code></td>
                        <td>Pick a random value from the provided options</td>
                        <td><code>"{{random('red', 'green', 'blue')}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>repeat(count, operation)</code></td>
                        <td>Repeat an operation count times</td>
                        <td>See Advanced Usage section</td>
                    </tr>
                </table>
            </section>

            <section id="examples">
                <h2>Examples</h2>

                <h3>Basic User Object</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/users/list
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}"
  }
}</code></pre>
                </div>

                <h3>Product with Nested Objects</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/products/details
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{commerce.productName}}",
    "description": "{{commerce.productDescription}}",
    "price": "{{floating(10, 1000)}}",
    "category": "{{commerce.department}}",
    "manufacturer": {
      "name": "{{company.name}}",
      "location": "{{location.country}}",
      "established": "{{date.past}}"
    },
    "specifications": {
      "weight": "{{floating(0.1, 10)}}",
      "dimensions": {
        "length": "{{floating(1, 100)}}",
        "width": "{{floating(1, 100)}}",
        "height": "{{floating(1, 100)}}"
      }
    }
  }
}</code></pre>
                </div>

                <h3>Array of Items</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/orders/list?records=3
{
  "structure": [{
    "id": "{{guid}}",
    "customer": {
      "name": "{{person.fullName}}",
      "email": "{{internet.email}}"
    },
    "orderDate": "{{date.past}}",
    "status": "{{random('pending', 'processing', 'shipped', 'delivered')}}",
    "total": "{{amount(10, 500, 2)}}"
  }]
}</code></pre>
                </div>
            </section>

            <section id="advanced-usage">
                <h2>Advanced Usage</h2>

                <h3>Generating Arrays with Repeat</h3>
                <p>
                    You can use the <code>repeat</code> helper to generate arrays of items:
                </p>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/products/details
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{commerce.productName}}",
    "price": "{{floating(10, 1000)}}",
    "tags": ["repeat(3)", "{{lorem.word}}"],
    "reviews": ["repeat(2)", {
      "user": "{{person.fullName}}",
      "rating": "{{integer(1, 5)}}",
      "comment": "{{lorem.sentence}}"
    }]
  }
}</code></pre>
                </div>

                <h3>Combining Multiple Helpers</h3>
                <p>
                    You can combine multiple helpers to create complex data structures:
                </p>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/users/profile
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    },
    "phoneNumbers": ["repeat(2)", {
      "type": "{{random('home', 'work', 'mobile')}}",
      "number": "{{phone.number}}"
    }],
    "preferences": {
      "theme": "{{random('light', 'dark', 'system')}}",
      "notifications": "{{boolean}}",
      "language": "{{random('en', 'es', 'fr', 'de')}}"
    },
    "subscription": {
      "plan": "{{random('free', 'basic', 'premium')}}",
      "startDate": "{{date.past}}",
      "endDate": "{{date.future}}",
      "autoRenew": "{{boolean}}"
    }
  }
}</code></pre>
                </div>

                <h3>Complex Data Structures for Contact Management</h3>
                <p>
                    Here are examples of complex data structures for contact management systems:
                </p>

                <h4>Contact Table Row</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/table
{
  "structure": [{
    "_id": "{{objectId()}}",
    "guid": "{{guid()}}",
    "type": "{{random('person', 'company')}}",
    "label": {
      "_id": "{{objectId()}}",
      "name": "{{random('client', 'reference', 'open')}}"
    },
    "avatar": "https://i.pravatar.cc/500",
    "name": "{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}",
    "email": "{{internet.email()}}",
    "phone": "+{{integer(1, 99)}} {{phone.number()}}",
    "dob": "{{date.birthdate()}}",
    "company": {
      "_id": "{{objectId()}}",
      "companyName": "{{company.name().toUpperCase()}}"
    },
    "jobTitle": "{{person.jobTitle()}}",
    "department": "{{person.jobArea()}}",
    "reportingTo": "{{person.fullName()}}",
    "contactOwner": "{{person.fullName()}}",
    "createdBy": "{{person.fullName()}}",
    "assistant": "{{person.fullName()}}",
    "assistantPhone": "+{{integer(1, 99)}} {{phone.number()}}",
    "gender": "{{person.gender()}}",
    "description": "{{person.bio()}}",
    "leadSource": "{{lorem.word(5)}}",
    "address": "{{integer(100, 999)}} {{location.street()}}, {{location.city()}}, {{location.state()}}, {{integer(100, 10000)}}",
    "latitude": "{{floating(-90.000001, 90)}}",
    "longitude": "{{floating(-180.000001, 180)}}",
    "local": {
      "latitude": "{{floating(-90.000001, 90)}}",
      "longitude": "{{floating(-180.000001, 180)}}"
    },
    "tags": ["{{repeat(7)}}", "{{lorem.words(1)}}"],
    "friends": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "name": "{{person.firstName()}} {{person.lastName()}}"
      }
    ],
    "greeting": "Hello, {{person.firstName()}} {{person.lastName()}}! You have {{integer(1, 10)}} unread messages.",
    "favoriteFruit": "{{random('apple', 'banana', 'strawberry')}}",
    "createdAt": "{{date.past()}}",
    "updatedAt": "{{date.recent()}}"
  }]
}</code></pre>
                </div>

                <h4>Contact Form Data</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/form
{
  "structure": {
    "_id": "{{objectId(66ca2151b2038dfb2582f258)}}",
    "guid": "{{guid()}}",
    "type": "{{random('person', 'company')}}",
    "status": "{{random('active', 'inactive')}}",
    "category": {
      "_id": "{{objectId()}}",
      "name": "{{random('client', 'reference', 'open')}}"
    },
    "domain": "{{internet.domainName()}}",
    "avatar": "https://i.pravatar.cc/500",
    "salutation": "{{person.prefix()}}",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "company": {
      "_id": "{{objectId()}}",
      "companyName": "{{company.name().toUpperCase()}}"
    },
    "companyName": "{{company.name()}}",
    "owner": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}",
      "avatar": "https://i.pravatar.cc/200"
    },
    "email": "{{internet.email()}}",
    "emails": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "email": "{{internet.email()}}"
      }
    ],
    "countryPhoneCode": "+{{integer(1, 99)}}",
    "phone": "{{phone.number('##########')}}",
    "phones": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "countryPhoneCode": "+{{integer(1, 99)}}",
        "phone": "{{phone.number('##########')}}"
      }
    ],
    "group": {
      "_id": "{{objectId()}}",
      "name": "{{lorem.words(1)}}"
    },
    "address": [
      {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      },
      {
        "_id": "{{objectId()}}",
        "type": "billing",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      },
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "type": "addon",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      }
    ],
    "billingOnPrimaryAddress": "{{boolean()}}",
    "jobTitle": "{{person.jobTitle()}}",
    "department": "{{person.jobArea()}}",
    "reportingTo": "{{person.fullName()}}",
    "createdBy": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}"
    },
    "assistant": "{{person.fullName()}}",
    "assistantPhone": "+{{integer(1, 99)}} {{phone.number()}}",
    "gender": "{{person.gender()}}",
    "description": "{{person.bio()}}",
    "leadSource": "{{random('apple', 'banana', 'strawberry')}}",
    "notes": [
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      },
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      }
    ],
    "dates": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "date": "{{date.past()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "customFields": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "field": "{{lorem.words(2)}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "relatedPersons": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "person": "{{person.fullName()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "websites": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "url": "{{internet.url()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "tags": [
      "{{repeat(7)}}",
      {
        "_id": "{{objectId()}}",
        "tag": "{{lorem.words(1)}}",
        "icon": "https://i.pravatar.cc/200"
      }
    ],
    "createdAt": "{{date.past()}}",
    "updatedAt": "{{date.recent()}}"
  }
}</code></pre>
                </div>

                <h4>Contact Profile Details</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/profile
{
  "structure": {
    "_id": "{{objectId()}}",
    "type": "{{random('person')}}",
    "avatar": "https://i.pravatar.cc/500",
    "salutation": "{{person.prefix()}}",
    "jobTitle": "{{person.jobTitle()}}",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "companyName": "{{company.name().toUpperCase()}}",
    "domain": "{{internet.domainName()}}",
    "email": "{{internet.email()}}",
    "countryPhoneCode": "+{{integer(1, 235)}}",
    "phone": "{{phone.number('##########')}}",
    "company": {
      "_id": "{{objectId()}}",
      "avatar": "https://i.pravatar.cc/500",
      "domain": "{{internet.domainName()}}",
      "companyName": "{{company.name().toUpperCase()}}",
      "email": "{{internet.email()}}",
      "countryPhoneCode": "+{{integer(1, 99)}}",
      "phone": "{{phone.number('##########')}}",
      "address": {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      }
    },
    "address": {
      "_id": "{{objectId()}}",
      "type": "primary",
      "country": "{{location.state()}}",
      "street": "{{location.street()}}",
      "city": "{{location.city(5)}}",
      "state": "{{location.state()}}",
      "postalCode": "{{integer(100000, 999999)}}",
      "label": "home"
    }
  }
}</code></pre>
                </div>

                <h4>Contact Lead Information</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/leads
{
  "structure": {
    "_id": "{{objectId()}}",
    "email": "{{internet.email()}}",
    "emails": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "email": "{{internet.email()}}"
      }
    ],
    "countryPhoneCode": "+{{integer(1, 99)}}",
    "phone": "{{phone.number('##########')}}",
    "phones": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "countryPhoneCode": "+{{integer(1, 99)}}",
        "phone": "{{phone.number('##########')}}"
      }
    ],
    "group": {
      "_id": "{{objectId()}}",
      "name": "{{lorem.words(1)}}"
    },
    "createdBy": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}"
    },
    "owner": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}",
      "avatar": "https://i.pravatar.cc/200"
    },
    "notes": [
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      },
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      }
    ],
    "dates": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "date": "{{date.past()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "customFields": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "field": "{{lorem.words(2)}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "relatedPersons": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "person": "{{person.fullName()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "websites": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "url": "{{internet.url()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "tags": [
      "{{repeat(7)}}",
      {
        "_id": "{{objectId()}}",
        "tag": "{{lorem.words(1)}}",
        "icon": "https://i.pravatar.cc/200"
      }
    ],
    "createdAt": "{{date.past()}}"
  }
}</code></pre>
                </div>

                <h4>Contact Address Information</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/address
{
  "structure": {
    "address": [
      {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      },
      {
        "_id": "{{objectId()}}",
        "type": "billing",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      },
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "type": "addon",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      }
    ],
    "billingOnPrimaryAddress": "{{boolean()}}"
  }
}</code></pre>
                </div>

                <h4>Associated Persons</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/associated
{
  "structure": [{
    "_id": "{{objectId()}}",
    "avatar": "https://i.pravatar.cc/500",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "email": "{{internet.email()}}",
    "address": {
      "_id": "{{objectId()}}",
      "type": "primary",
      "country": "{{location.state()}}",
      "street": "{{location.street()}}",
      "city": "{{location.city(5)}}",
      "state": "{{location.state()}}",
      "postalCode": "{{integer(100000, 999999)}}",
      "label": "home"
    }
  }]
}</code></pre>
                </div>

                <h4>Company and Owner Data</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/company
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}"
  }]
}

POST /api/mock/contacts/owner
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}",
    "avatar": "https://i.pravatar.cc/200"
  }]
}

POST /api/mock/contacts/group
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}"
  }]
}</code></pre>
                </div>
            </section>
        </div>

        <div class="footer">
            <p>Mirage - Dynamic Mock API Server By Innvoq &copy; 2025</p>
        </div>
    </div>
</body>
</html>
