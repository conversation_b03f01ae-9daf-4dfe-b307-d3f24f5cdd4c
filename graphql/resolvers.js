/**
 * GraphQL Resolvers for Mirage Dynamic Mock API Server
 * 
 * This file contains the resolver functions for GraphQL queries and mutations.
 * It uses the same mock data generation logic as the REST API.
 */

const { GraphQLScalarType } = require('graphql');
const { Kind } = require('graphql/language');
const { generateMockData } = require('../controllers/mock/helpers');

/**
 * Custom JSON scalar type for handling arbitrary JSON data
 */
const JSONType = new GraphQLScalarType({
  name: 'JSON',
  description: 'JSON scalar type',
  serialize(value) {
    return value;
  },
  parseValue(value) {
    return value;
  },
  parseLiteral(ast) {
    switch (ast.kind) {
      case Kind.STRING:
      case Kind.BOOLEAN:
        return ast.value;
      case Kind.INT:
      case Kind.FLOAT:
        return parseFloat(ast.value);
      case Kind.OBJECT: {
        const value = Object.create(null);
        ast.fields.forEach((field) => {
          value[field.name.value] = parseLiteral(field.value);
        });
        return value;
      }
      case Kind.LIST:
        return ast.values.map(parseLiteral);
      default:
        return null;
    }
  },
});

/**
 * Helper function to parse literal values recursively
 */
function parseLiteral(ast) {
  switch (ast.kind) {
    case Kind.STRING:
    case Kind.BOOLEAN:
      return ast.value;
    case Kind.INT:
    case Kind.FLOAT:
      return parseFloat(ast.value);
    case Kind.OBJECT: {
      const value = Object.create(null);
      ast.fields.forEach((field) => {
        value[field.name.value] = parseLiteral(field.value);
      });
      return value;
    }
    case Kind.LIST:
      return ast.values.map(parseLiteral);
    default:
      return null;
  }
}

/**
 * Generate mock data using the same logic as the REST API
 * @param {Object} input - Input parameters
 * @returns {Object} Mock data response or error
 */
const generateMockDataResolver = async (input) => {
  try {
    const { app, endpoint, structure, records = 1 } = input;

    // Validate input
    if (!structure) {
      return {
        __typename: 'Error',
        success: false,
        message: 'Invalid structure format',
        errors: ['Structure is required']
      };
    }

    // Generate mock data using the same helper function as REST API
    const mockData = generateMockData(structure, Number(records));

    // Log request info (for debugging)
    console.log(`GraphQL: Generated mock data for ${app}/${endpoint} with ${records} records`);

    // Return successful response
    return {
      __typename: 'MockDataResponse',
      success: true,
      app,
      endpoint,
      records: Array.isArray(mockData) ? mockData.length : 1,
      data: mockData
    };
  } catch (error) {
    console.error('GraphQL Error generating mock data:', error);
    return {
      __typename: 'Error',
      success: false,
      message: 'Server error occurred while generating mock data',
      errors: [error.message]
    };
  }
};

/**
 * GraphQL resolvers
 */
const resolvers = {
  // Custom scalar types
  JSON: JSONType,

  // Union type resolver
  MockDataResult: {
    __resolveType(obj) {
      if (obj.success && obj.data) {
        return 'MockDataResponse';
      }
      return 'Error';
    }
  },

  // Query resolvers
  Query: {
    /**
     * Get mock data (Query operation)
     * @param {Object} parent - Parent object
     * @param {Object} args - Arguments
     * @returns {Object} Mock data response
     */
    getMock: async (parent, args) => {
      return generateMockDataResolver(args.input);
    }
  },

  // Mutation resolvers
  Mutation: {
    /**
     * Post mock data (Mutation operation)
     * @param {Object} parent - Parent object
     * @param {Object} args - Arguments
     * @returns {Object} Mock data response
     */
    postMock: async (parent, args) => {
      return generateMockDataResolver(args.input);
    }
  }
};

module.exports = resolvers;
