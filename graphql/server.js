/**
 * GraphQL Server Configuration for Mirage Dynamic Mock API Server
 *
 * This file sets up the GraphQL server using GraphQL Yoga
 * and integrates it with the Express application.
 */

const { createYoga } = require('graphql-yoga');
const { makeExecutableSchema } = require('@graphql-tools/schema');
const typeDefs = require('./schema');
const resolvers = require('./resolvers');

/**
 * Create GraphQL Yoga server
 */
const createGraphQLServer = () => {
  // Create executable schema
  const schema = makeExecutableSchema({
    typeDefs,
    resolvers,
  });

  return createYoga({
    schema,
    graphiql: {
      title: 'Mirage GraphQL API',
      defaultQuery: `# Welcome to Mirage GraphQL API
# 
# This is a dynamic mock API server that generates realistic mock data
# based on provided structures using Faker.js.
#
# Try the examples below:

# Example 1: Simple user data
query GetUsers {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
      age: "{{integer(18, 65)}}"
      isActive: "{{boolean}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}

# Example 2: Complex contact data with nested objects
mutation CreateContact {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      guid: "{{guid()}}"
      type: "{{random(\\"person\\", \\"company\\")}}"
      name: "{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}"
      email: "{{internet.email()}}"
      phone: "+{{integer(1, 99)}} {{phone.number()}}"
      company: {
        _id: "{{objectId()}}"
        companyName: "{{company.name().toUpperCase()}}"
      }
      address: {
        street: "{{location.street()}}"
        city: "{{location.city()}}"
        state: "{{location.state()}}"
        country: "{{location.country()}}"
      }
      tags: ["{{repeat(3)}}", "{{lorem.words(1)}}"]
    }
    records: 1
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}`,
    },
    cors: {
      origin: '*',
      credentials: true,
    },
    context: (req) => ({
      req,
    }),
  });
};

module.exports = { createGraphQLServer };
