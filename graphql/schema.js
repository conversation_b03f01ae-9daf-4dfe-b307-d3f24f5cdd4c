/**
 * GraphQL Schema for Mirage Dynamic Mock API Server
 *
 * This file defines the GraphQL schema with queries and mutations
 * for generating mock data.
 */

const typeDefs = `
  """
  JSON scalar type for handling arbitrary JSON data
  """
  scalar JSON

  """
  Input type for mock data generation request
  """
  input MockDataInput {
    """
    The application name (e.g., users, products, orders)
    """
    app: String!
    
    """
    The endpoint name (e.g., list, details, search)
    """
    endpoint: String!
    
    """
    The structure of the mock data to be generated.
    Can be an object or array with Faker.js templates.
    """
    structure: JSON!
    
    """
    Number of records to generate (default: 1)
    """
    records: Int = 1
  }

  """
  Response type for mock data generation
  """
  type MockDataResponse {
    """
    Indicates if the request was successful
    """
    success: Boolean!
    
    """
    The application name from the request
    """
    app: String!
    
    """
    The endpoint name from the request
    """
    endpoint: String!
    
    """
    Number of records generated
    """
    records: Int!
    
    """
    The generated mock data
    """
    data: JSON!
  }

  """
  Error type for handling errors
  """
  type Error {
    """
    Indicates if the request was successful (always false for errors)
    """
    success: Boolean!
    
    """
    Error message
    """
    message: String!
    
    """
    Additional error details
    """
    errors: [String]
  }

  """
  Union type for responses that can be either success or error
  """
  union MockDataResult = MockDataResponse | Error

  """
  Root Query type
  """
  type Query {
    """
    Generate mock data based on the provided structure.
    This is a query operation for read-only mock data generation.
    
    Example:
    query {
      getMock(input: {
        app: "users"
        endpoint: "list"
        structure: {
          id: "{{guid}}"
          name: "{{person.fullName}}"
          email: "{{internet.email}}"
        }
        records: 5
      }) {
        ... on MockDataResponse {
          success
          app
          endpoint
          records
          data
        }
        ... on Error {
          success
          message
          errors
        }
      }
    }
    """
    getMock(input: MockDataInput!): MockDataResult!
  }

  """
  Root Mutation type
  """
  type Mutation {
    """
    Generate mock data based on the provided structure.
    This is a mutation operation for creating new mock data.
    
    Example:
    mutation {
      postMock(input: {
        app: "contacts"
        endpoint: "form"
        structure: {
          _id: "{{objectId()}}"
          name: "{{person.fullName}}"
          email: "{{internet.email}}"
          company: {
            name: "{{company.name().toUpperCase()}}"
          }
        }
        records: 1
      }) {
        ... on MockDataResponse {
          success
          app
          endpoint
          records
          data
        }
        ... on Error {
          success
          message
          errors
        }
      }
    }
    """
    postMock(input: MockDataInput!): MockDataResult!
  }
`);

module.exports = schema;
