# GitLab CI/CD Pipeline for Mirage API Server
# This pipeline builds, tests, and deploys the Mirage API server to a VPS

# Define stages
stages:
  - validate
  - test
  - build
  - security
  - deploy
  - verify

# Global variables
variables:
  NODE_VERSION: "20"
  APP_NAME: "mirage-api"
  DEPLOY_PATH: "/home/<USER>/htdocs/mirage.innvoq.com"
  PM2_APP_NAME: "mirage-api"

# Cache configuration for faster builds
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/
    - .npm/

# Before script - runs before each job
before_script:
  - echo "🚀 Starting CI/CD Pipeline for Mirage API"
  - echo "Branch - $CI_COMMIT_REF_NAME"
  - echo "Commit - $CI_COMMIT_SHA"

# ==========================================
# VALIDATE STAGE
# ==========================================

validate:syntax:
  stage: validate
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🔍 Validating code syntax and structure..."
    - npm ci --cache .npm --prefer-offline
    - echo "✅ Package.json validation passed"
    - node -c app.js
    - echo "✅ Main application syntax validation passed"
    - find . -name "*.js" -not -path "./node_modules/*" -exec node -c {} \;
    - echo "✅ All JavaScript files syntax validation passed"
  only:
    - merge_requests
    - main
    - develop

validate:dependencies:
  stage: validate
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🔍 Checking for security vulnerabilities in dependencies..."
    - npm ci --cache .npm --prefer-offline
    - npm audit --audit-level=moderate
    - echo "✅ Dependency security check passed"
  allow_failure: true
  only:
    - merge_requests
    - main
    - develop

# ==========================================
# TEST STAGE
# ==========================================

test:unit:
  stage: test
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🧪 Running unit tests..."
    - npm ci --cache .npm --prefer-offline
    - npm start &
    - sleep 10
    - echo "🔄 Testing API endpoints..."
    - node test-all-apis.js
    - node test-documentation-endpoints.js
    - echo "✅ All tests passed"
  artifacts:
    reports:
      junit: test-results.xml
    paths:
      - test-results/
    expire_in: 1 week
  coverage: '/Coverage: \d+\.\d+%/'
  only:
    - merge_requests
    - main
    - develop

test:integration:
  stage: test
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🔗 Running integration tests..."
    - npm ci --cache .npm --prefer-offline
    - npm start &
    - sleep 15
    - echo "🧪 Testing WebSocket functionality..."
    - node test-websocket-comprehensive.js
    - echo "✅ Integration tests passed"
  only:
    - main
    - develop

# ==========================================
# BUILD STAGE
# ==========================================

build:application:
  stage: build
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🏗️ Building application..."
    - npm ci --cache .npm --prefer-offline --production
    - echo "📦 Creating deployment package..."
    - mkdir -p dist
    - cp -r . dist/
    - cd dist && rm -rf .git .gitlab-ci.yml test-*.js node_modules
    - cd dist && npm ci --production --silent
    - echo "✅ Build completed successfully"
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  only:
    - main
    - develop

# ==========================================
# SECURITY STAGE
# ==========================================

security:scan:
  stage: security
  image: node:${NODE_VERSION}-alpine
  script:
    - echo "🔒 Running security scans..."
    - npm ci --cache .npm --prefer-offline
    - npx audit-ci --moderate
    - echo "✅ Security scan completed"
  allow_failure: true
  only:
    - main

# ==========================================
# DEPLOY STAGE
# ==========================================

deploy:staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client rsync
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $STAGING_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "🚀 Deploying to staging server..."
    - echo "Server - $STAGING_SERVER_IP"
    - echo "User - $DEPLOY_USER"
    - echo "Creating deployment directory..."
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "sudo mkdir -p $DEPLOY_PATH"
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "sudo chown $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH"
    
    # Sync files to server
    - rsync -avz --delete dist/ $DEPLOY_USER@$STAGING_SERVER_IP:$DEPLOY_PATH/
    
    # Install dependencies and restart application
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "cd $DEPLOY_PATH && npm ci --production"
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "cd $DEPLOY_PATH && pm2 stop $PM2_APP_NAME || true"
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "cd $DEPLOY_PATH && pm2 start app.js --name $PM2_APP_NAME"
    - ssh $DEPLOY_USER@$STAGING_SERVER_IP "pm2 save"
    
    - echo "✅ Staging deployment completed"
  environment:
    name: staging
    url: http://$STAGING_SERVER_IP:3000
  dependencies:
    - build:application
  only:
    - develop

deploy:production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client rsync
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $PRODUCTION_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - echo "🚀 Deploying to production server..."
    - echo "Server - $PRODUCTION_SERVER_IP"
    - echo "User - $DEPLOY_USER"
    - echo "Creating backup of current deployment..."
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "sudo cp -r $DEPLOY_PATH $DEPLOY_PATH.backup.$(date +%Y%m%d_%H%M%S) || true"
    
    # Create deployment directory
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "sudo mkdir -p $DEPLOY_PATH"
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "sudo chown $DEPLOY_USER:$DEPLOY_USER $DEPLOY_PATH"
    
    # Sync files to server
    - rsync -avz --delete dist/ $DEPLOY_USER@$PRODUCTION_SERVER_IP:$DEPLOY_PATH/
    
    # Install dependencies and restart application
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "cd $DEPLOY_PATH && npm ci --production"
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "cd $DEPLOY_PATH && pm2 stop $PM2_APP_NAME || true"
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "cd $DEPLOY_PATH && pm2 start app.js --name $PM2_APP_NAME"
    - ssh $DEPLOY_USER@$PRODUCTION_SERVER_IP "pm2 save"
    
    - echo "✅ Production deployment completed"
  environment:
    name: production
    url: http://$PRODUCTION_SERVER_IP:3000
  dependencies:
    - build:application
  when: manual
  only:
    - main

# ==========================================
# VERIFY STAGE
# ==========================================

verify:staging:
  stage: verify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "🔍 Verifying staging deployment..."
    - sleep 30
    - curl -f http://$STAGING_SERVER_IP:3000/ || exit 1
    - curl -f http://$STAGING_SERVER_IP:3000/api-docs || exit 1
    - curl -f http://$STAGING_SERVER_IP:3000/client-integration || exit 1
    - echo "✅ Staging verification completed"
  dependencies:
    - deploy:staging
  only:
    - main

.verify:production:
  stage: verify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "🔍 Verifying production deployment..."
    - sleep 30
    - curl -f http://$PRODUCTION_SERVER_IP:3000/ || exit 1
    - curl -f http://$PRODUCTION_SERVER_IP:3000/api-docs || exit 1
    - curl -f http://$PRODUCTION_SERVER_IP:3000/client-integration || exit 1
    - echo "✅ Production verification completed"
  dependencies:
    - deploy:production
  only:
    - main

# ==========================================
# NOTIFICATION STAGE (Optional)
# ==========================================

notify:success:
  stage: .post
  image: alpine:latest
  script:
    - echo "🎉 Deployment completed successfully!"
    - echo "Environment - $CI_ENVIRONMENT_NAME"
    - echo "Commit - $CI_COMMIT_SHA"
    - echo "Branch - $CI_COMMIT_REF_NAME"
  when: on_success
  only:
    - main
    - develop

notify:failure:
  stage: .post
  image: alpine:latest
  script:
    - echo "❌ Pipeline failed!"
    - echo "Stage - $CI_JOB_STAGE"
    - echo "Job - $CI_JOB_NAME"
    - echo "Commit - $CI_COMMIT_SHA"
  when: on_failure
  only:
    - main
    - develop
