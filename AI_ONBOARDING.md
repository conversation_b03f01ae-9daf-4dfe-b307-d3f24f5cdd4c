# AI Onboarding Guide for Mirage Dynamic Mock API Server

## Project Overview

Mirage is a Node.js Express backend application that provides dynamic mock API endpoints with realistic dummy data generated using Faker.js. The application allows clients to define their own data structure and generates mock data based on that structure.

## Core Requirements

1. **Dynamic Data Generation**:
   - Generate mock data based on client-defined structure
   - Support for nested objects, arrays, and complex data structures
   - Utilize Faker.js for realistic data generation

2. **API Functionality**:
   - Dynamic endpoint that accepts any app and endpoint combination
   - Support for POST operations with structure definition in request body
   - Query parameter support for controlling number of records

3. **CORS Configuration**:
   - Accept requests from all domains (both HTTP and HTTPS)
   - Support for various HTTP methods (GET, POST, PUT, DELETE, OPTIONS)
   - Allow standard headers (Content-Type, Authorization)

4. **Error Handling**:
   - Consistent error response format
   - Validation of request parameters
   - Proper error logging

## Project Structure

```
mirage/
├── app.js                  # Main application entry point
├── package.json            # Project metadata and dependencies
├── common/
│   └── errors.js           # Common error handling functions
├── routes/
│   └── mock.js             # API routes definition
└── controllers/
    └── mock/
        ├── controller.js   # Controller function for the dynamic endpoint
        └── helpers.js      # Helper functions for generating mock data
```

## Key Components

### 1. Main Application (app.js)

The entry point configures Express with necessary middleware, sets up CORS, defines routes, and starts the server.

Key aspects:
- Express initialization
- Middleware configuration (JSON parsing, CORS)
- Route registration
- Server startup

### 2. Routes (routes/mock.js)

Defines the dynamic route for the mock API endpoint.

Key aspects:
- Dynamic route definition with app and endpoint parameters
- Request validation using express-validator
- Routing to the controller function

### 3. Controller (controllers/mock/controller.js)

Contains the logic for handling requests and generating responses.

Key aspects:
- Request validation
- Parameter extraction
- Integration with helper functions for data generation
- Response formatting
- Error handling

### 4. Helpers (controllers/mock/helpers.js)

Contains utility functions for generating mock data using Faker.js.

Key aspects:
- Template parsing and processing
- Helper function definitions for various data types
- Dynamic data generation based on structure
- Support for nested objects and arrays

### 5. Error Handling (common/errors.js)

Contains functions for handling common error scenarios.

Key aspects:
- Consistent error response format
- Functions for different error types (server error, not found, bad request, etc.)

## API Endpoints

### Mock Data Generation

The server provides a dynamic endpoint that can be used to generate mock data:

```
POST /api/mock/:app/:endpoint
```

Where:
- `:app` - Any application name (e.g., users, products, orders)
- `:endpoint` - Any endpoint name (e.g., list, details, search)

### Documentation

The server also provides a documentation endpoint that returns a comprehensive HTML page with detailed information about the API:

```
GET /docs
```

This documentation includes:
- Detailed explanation of the template syntax
- Complete list of available helpers with examples
- Sample requests and responses
- Advanced usage examples

## Template Syntax

The application uses a template syntax for defining data structures:

1. **Basic Syntax**: `{{helperName}}`
2. **With Parameters**: `{{helperName(param1, param2, ...)}}`
3. **Nested Properties**: `{{category.subcategory}}`

## Data Generation Process

1. Client sends a POST request to `/api/mock/:app/:endpoint` with a structure object in the request body
2. The controller validates the request and extracts the structure
3. The structure is passed to the `generateMockData` function
4. The function processes the structure, replacing templates with generated data
5. The generated data is returned to the client

## Example Usage

### Request:

```json
POST /api/mock/users/list?records=2
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "country": "{{location.country}}"
    }
  }
}
```

### Response:

```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 2,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "email": "<EMAIL>",
      "age": 32,
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "country": "USA"
      }
    },
    {
      "id": "223e4567-e89b-12d3-a456-************",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "age": 28,
      "address": {
        "street": "456 Oak Ave",
        "city": "Los Angeles",
        "country": "USA"
      }
    }
  ]
}
```

## Development Guidelines

1. **Code Organization**:
   - Follow the established directory structure
   - Keep controllers thin, move business logic to helpers
   - Use descriptive function and variable names

2. **Error Handling**:
   - Use try/catch blocks for async operations
   - Return consistent error responses
   - Log errors appropriately

3. **Template Processing**:
   - Maintain consistent template syntax
   - Add new helper functions as needed
   - Document new helpers in the README

4. **Testing**:
   - Test with various structure definitions
   - Verify error handling
   - Check response formats

## Running the Application

```bash
# Install dependencies
npm install

# Start in production mode
npm start

# Start in development mode (with auto-reload)
npm run dev
```

The server runs on port 3000 by default, configurable via the PORT environment variable.

## Future Enhancements

Potential areas for improvement:
- Add caching for frequently used structures
- Implement schema validation for structure objects
- Add support for custom helper functions
- Add OpenAPI/Swagger documentation
- Add authentication for protected endpoints
- Implement rate limiting
