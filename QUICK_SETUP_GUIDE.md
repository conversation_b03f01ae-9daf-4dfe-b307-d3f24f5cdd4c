# 🚀 Quick Setup Guide - GitLab CI/CD for Mirage API

## 📋 What You Need to Do

### 1. 🔑 GitLab CI/CD Variables Setup

Go to your GitLab project → **Settings** → **CI/CD** → **Variables** and add these:

| Variable Name | Value | Protected | Masked |
|---------------|-------|-----------|---------|
| `SSH_PRIVATE_KEY` | Your SSH private key | ✅ | ❌ |
| `DEPLOY_USER` | `deploy` | ❌ | ❌ |
| `STAGING_SERVER_IP` | Your staging server IP | ❌ | ❌ |
| `PRODUCTION_SERVER_IP` | Your production server IP | ✅ | ❌ |

### 2. 🖥️ Server Setup

**Option A: Automated Setup (Recommended)**
```bash
# On your VPS as root:
wget https://raw.githubusercontent.com/your-repo/mirage-api/main/scripts/setup-server.sh
chmod +x setup-server.sh
sudo ./setup-server.sh
```

**Option B: Manual Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Create deploy user
sudo useradd -m -s /bin/bash deploy
sudo usermod -aG sudo deploy

# Create directories
sudo mkdir -p /opt/mirage-api /var/log/mirage-api
sudo chown deploy:deploy /opt/mirage-api /var/log/mirage-api
```

### 3. 🔐 SSH Key Setup

**Generate SSH key:**
```bash
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
```

**Copy public key to server:**
```bash
ssh-copy-id deploy@your-server-ip
```

**Copy private key for GitLab:**
```bash
cat ~/.ssh/id_rsa
# Copy this entire content to GitLab SSH_PRIVATE_KEY variable
```

### 4. 🔥 Firewall Configuration

```bash
# On your VPS:
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000
sudo ufw enable
```

### 5. 🚀 Deploy!

**For Staging:**
```bash
git push origin develop
# Automatically deploys to staging
```

**For Production:**
```bash
git push origin main
# Go to GitLab → CI/CD → Pipelines
# Click "Manual" button for production deployment
```

## 🔍 Verification

After deployment, check these URLs:
- **API**: `http://your-server-ip:3000/`
- **Documentation**: `http://your-server-ip:3000/docs`
- **Client Guide**: `http://your-server-ip:3000/client-integration`
- **WebSocket Demo**: `http://your-server-ip:3000/ws-demo`

## 🛠️ Useful Commands

**Check application status:**
```bash
ssh deploy@your-server-ip
pm2 status
pm2 logs mirage-api
```

**Restart application:**
```bash
pm2 restart mirage-api
```

**View server logs:**
```bash
tail -f /var/log/mirage-api/combined.log
```

## 🆘 Troubleshooting

**Pipeline fails with SSH error:**
- Check SSH_PRIVATE_KEY format in GitLab variables
- Ensure no extra spaces or newlines
- Test SSH connection manually

**Application won't start:**
```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs mirage-api

# Check Node.js version
node --version
```

**Port 3000 already in use:**
```bash
# Find what's using the port
sudo lsof -i :3000

# Kill the process
sudo kill -9 <PID>
```

## 📚 Full Documentation

For detailed instructions, see [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)

## 🐳 Docker Alternative

If you prefer Docker deployment:

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t mirage-api .
docker run -d -p 3000:3000 --name mirage-api mirage-api
```

## ✅ Success Checklist

- [ ] GitLab CI/CD variables configured
- [ ] Server setup completed
- [ ] SSH keys configured
- [ ] Firewall configured
- [ ] First deployment successful
- [ ] API endpoints accessible
- [ ] PM2 process running
- [ ] Logs are being generated

## 🎉 You're Done!

Your Mirage API server is now deployed with automated CI/CD! 

Every push to `develop` deploys to staging, and every push to `main` can be manually deployed to production.
