PW: 
GENERAL: INNVoq@has427
GIT: INNV@oq_has427
Deployment process


// Deployment

ssh root@193.203.162.52

cd /home/<USER>/htdocs/mirage.innvoq.com

// Delete all files from path

rm -r *

// do in project Folder Create .tar with all required files

tar --exclude='node_modules' --exclude='.git*' --exclude='.ebextensions' --exclude='.next' --exclude='backup' --exclude='study' -cvf project.tar .

scp project.tar root@193.203.162.52:/home/<USER>/htdocs/mirage.innvoq.com

// Do in VPS shell

tar -xvf project.tar
rm -r project.tar
hash -r
npm install
npm run build

// server startup
== optional - npm install -g pm2
pm2 kill
pm2 start npm --name "sggangajhari.in" -- start
pm2 status
pm2 startup
exit


// PM2 operations
To stop startup: pm2 unstartup systemd
pm2 kill


