/**
 * WebSocket Integration for Mirage Dynamic Mock API Server
 * 
 * This file integrates the WebSocket server with the main application
 * and sets up all handlers and middleware
 */

const MirageWebSocketServer = require('./server');
const MockHandlers = require('./handlers/mockHandlers');
const SystemHandlers = require('./handlers/systemHandlers');
const { WebSocketAuth, WebSocketRateLimit, WebSocketMessageValidator } = require('./middleware/auth');

class WebSocketIntegration {
  constructor(httpServer, options = {}) {
    this.httpServer = httpServer;
    this.options = {
      // WebSocket server options
      heartbeatInterval: options.heartbeatInterval || 30000,
      heartbeatTimeout: options.heartbeatTimeout || 5000,
      compression: options.compression !== false,
      binaryProtocol: options.binaryProtocol || false,
      
      // Authentication options
      requireAuth: options.requireAuth || false,
      validApiKeys: options.validApiKeys || [],
      allowAnonymous: options.allowAnonymous !== false,
      
      // Rate limiting options
      rateLimitWindowMs: options.rateLimitWindowMs || 60000,
      rateLimitMaxRequests: options.rateLimitMaxRequests || 100,
      
      // Message validation options
      maxMessageSize: options.maxMessageSize || 1024 * 1024,
      allowedTypes: options.allowedTypes || ['mock', 'system', 'room', 'namespace'],
      
      ...options
    };

    this.wsServer = null;
    this.auth = null;
    this.rateLimit = null;
    this.messageValidator = null;
    this.mockHandlers = null;
    this.systemHandlers = null;

    this.initialize();
  }

  /**
   * Initialize WebSocket integration
   */
  initialize() {
    // Create WebSocket server
    this.wsServer = new MirageWebSocketServer(this.httpServer, this.options);

    // Initialize middleware
    this.auth = new WebSocketAuth({
      requireAuth: this.options.requireAuth,
      validApiKeys: this.options.validApiKeys,
      allowAnonymous: this.options.allowAnonymous
    });

    this.rateLimit = new WebSocketRateLimit({
      windowMs: this.options.rateLimitWindowMs,
      maxRequests: this.options.rateLimitMaxRequests
    });

    this.messageValidator = new WebSocketMessageValidator({
      maxMessageSize: this.options.maxMessageSize,
      allowedTypes: this.options.allowedTypes
    });

    // Initialize handlers
    this.mockHandlers = new MockHandlers(this.wsServer);
    this.systemHandlers = new SystemHandlers(this.wsServer);

    // Setup middleware integration
    this.setupMiddleware();

    // Setup periodic cleanup
    this.setupCleanup();

    // Setup subscription broadcasting
    this.setupSubscriptionBroadcasting();

    console.log('WebSocket integration initialized successfully');
  }

  /**
   * Setup middleware integration
   */
  setupMiddleware() {
    // Override the original handleConnection to add middleware
    const originalHandleConnection = this.wsServer.handleConnection.bind(this.wsServer);
    
    this.wsServer.handleConnection = async (ws, req) => {
      try {
        // Authenticate connection
        const authResult = await this.auth.authenticate(req, ws);
        
        if (!authResult.authenticated) {
          ws.close(1008, 'Authentication failed');
          return;
        }

        // Call original handler
        originalHandleConnection(ws, req);

        // Store auth info in connection
        // Wait a bit for the connection to be stored
        setTimeout(() => {
          const connectionId = Array.from(this.wsServer.connections.keys()).pop();
          const connection = this.wsServer.connections.get(connectionId);
          if (connection) {
            connection.user = authResult.user;
            connection.authenticated = true;
          }
        }, 100);

      } catch (error) {
        console.error('WebSocket authentication error:', error);
        ws.close(1008, 'Authentication failed');
      }
    };

    // Override message handling to add validation and rate limiting
    const originalHandleMessage = this.wsServer.handleMessage.bind(this.wsServer);
    
    this.wsServer.handleMessage = (connectionId, data) => {
      try {
        const connection = this.wsServer.connections.get(connectionId);
        if (!connection) return;

        // Rate limiting
        const rateLimitResult = this.rateLimit.checkRateLimit(connectionId, connection);
        if (!rateLimitResult.allowed) {
          this.wsServer.sendToConnection(connectionId, {
            type: 'error',
            event: 'rate_limit_exceeded',
            data: {
              code: 'RATE_LIMIT_EXCEEDED',
              message: 'Too many requests',
              resetTime: rateLimitResult.resetTime,
              timestamp: new Date().toISOString()
            }
          });
          return;
        }

        // Message validation
        const validationResult = this.messageValidator.validateMessage(data, connectionId);
        if (!validationResult.valid) {
          this.wsServer.sendToConnection(connectionId, {
            type: 'error',
            event: 'validation_error',
            data: {
              code: validationResult.code,
              message: validationResult.error,
              timestamp: new Date().toISOString()
            }
          });
          return;
        }

        // Authorization check
        const message = validationResult.message;
        const action = `${message.type}:${message.event}`;

        // For development, allow all actions (skip authorization for now)
        // TODO: Implement proper authorization after core functionality is working
        /*
        // Ensure user object exists, default to anonymous
        const user = connection.user || { type: 'anonymous', id: 'anonymous' };

        if (!this.auth.authorize(user, action)) {
          this.wsServer.sendToConnection(connectionId, {
            type: 'error',
            event: 'authorization_error',
            data: {
              code: 'UNAUTHORIZED',
              message: 'Insufficient permissions for this action',
              action: action,
              timestamp: new Date().toISOString()
            }
          });
          return;
        }
        */

        // Call original handler
        originalHandleMessage(connectionId, data);

      } catch (error) {
        console.error('WebSocket message handling error:', error);
        this.wsServer.sendError(connectionId, 'INTERNAL_ERROR', 'Internal server error');
      }
    };

    // Override disconnection handling to clean up middleware
    const originalHandleDisconnection = this.wsServer.handleDisconnection.bind(this.wsServer);
    
    this.wsServer.handleDisconnection = (connectionId) => {
      // Clean up rate limiting
      this.rateLimit.removeClient(connectionId);
      
      // Call original handler
      originalHandleDisconnection(connectionId);
    };
  }

  /**
   * Setup periodic cleanup
   */
  setupCleanup() {
    // Clean up rate limiting records every 5 minutes
    setInterval(() => {
      this.rateLimit.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Setup subscription broadcasting
   */
  setupSubscriptionBroadcasting() {
    // Broadcast to subscriptions every 5 seconds
    setInterval(() => {
      this.broadcastToSubscriptions();
    }, 5000);
  }

  /**
   * Broadcast to active subscriptions
   */
  broadcastToSubscriptions() {
    this.wsServer.connections.forEach((connection, connectionId) => {
      if (connection.subscriptions && connection.subscriptions.size > 0) {
        connection.subscriptions.forEach((subscriptionInfo, topic) => {
          try {
            const { structure } = subscriptionInfo;
            const { generateMockData } = require('../controllers/mock/helpers');
            const mockData = generateMockData(structure, 1);
            
            this.wsServer.sendToConnection(connectionId, {
              type: 'mock',
              event: 'subscriptionUpdate',
              data: {
                success: true,
                topic,
                data: mockData,
                timestamp: new Date().toISOString()
              }
            });
          } catch (error) {
            console.error(`Error broadcasting to subscription ${topic}:`, error);
          }
        });
      }
    });
  }

  /**
   * Get WebSocket server instance
   */
  getServer() {
    return this.wsServer;
  }

  /**
   * Get server metrics
   */
  getMetrics() {
    return this.wsServer.getMetrics();
  }

  /**
   * Get all connections
   */
  getConnections() {
    return this.wsServer.getAllConnections();
  }

  /**
   * Broadcast message to all connections
   */
  broadcast(message, excludeConnectionId = null) {
    return this.wsServer.broadcast(message, excludeConnectionId);
  }

  /**
   * Broadcast message to room
   */
  broadcastToRoom(roomName, message, excludeConnectionId = null) {
    return this.wsServer.broadcastToRoom(roomName, message, excludeConnectionId);
  }

  /**
   * Broadcast message to namespace
   */
  broadcastToNamespace(namespaceName, message, excludeConnectionId = null) {
    return this.wsServer.broadcastToNamespace(namespaceName, message, excludeConnectionId);
  }

  /**
   * Close WebSocket server
   */
  close() {
    if (this.wsServer) {
      this.wsServer.close();
    }
  }
}

module.exports = WebSocketIntegration;
