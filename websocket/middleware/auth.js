/**
 * WebSocket Authentication Middleware
 * 
 * This middleware provides authentication and authorization for WebSocket connections
 */

class WebSocketAuth {
  constructor(options = {}) {
    this.options = {
      requireAuth: options.requireAuth || false,
      apiKeyHeader: options.apiKeyHeader || 'x-api-key',
      validApiKeys: options.validApiKeys || [],
      allowAnonymous: options.allowAnonymous !== false,
      ...options
    };
  }

  /**
   * Authenticate WebSocket connection
   */
  authenticate(req, ws) {
    return new Promise((resolve, reject) => {
      try {
        // If authentication is not required, allow connection
        if (!this.options.requireAuth) {
          resolve({
            authenticated: true,
            user: { id: 'anonymous', type: 'anonymous' }
          });
          return;
        }

        // Extract authentication information from request
        const apiKey = this.extractApiKey(req);
        const token = this.extractToken(req);

        // Validate API key if provided
        if (apiKey) {
          if (this.validateApiKey(apiKey)) {
            resolve({
              authenticated: true,
              user: { id: apiKey, type: 'api_key' }
            });
            return;
          } else {
            reject(new Error('Invalid API key'));
            return;
          }
        }

        // Validate token if provided
        if (token) {
          this.validateToken(token)
            .then(user => {
              resolve({
                authenticated: true,
                user: { ...user, type: 'token' }
              });
            })
            .catch(error => {
              reject(new Error('Invalid token'));
            });
          return;
        }

        // Allow anonymous connections if enabled
        if (this.options.allowAnonymous) {
          resolve({
            authenticated: true,
            user: { id: 'anonymous', type: 'anonymous' }
          });
        } else {
          reject(new Error('Authentication required'));
        }

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Extract API key from request
   */
  extractApiKey(req) {
    // Check headers
    const headerKey = req.headers[this.options.apiKeyHeader.toLowerCase()];
    if (headerKey) return headerKey;

    // Check query parameters
    const url = new URL(req.url, `http://${req.headers.host}`);
    const queryKey = url.searchParams.get('apiKey') || url.searchParams.get('api_key');
    if (queryKey) return queryKey;

    return null;
  }

  /**
   * Extract token from request
   */
  extractToken(req) {
    // Check Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check query parameters
    const url = new URL(req.url, `http://${req.headers.host}`);
    const queryToken = url.searchParams.get('token') || url.searchParams.get('access_token');
    if (queryToken) return queryToken;

    return null;
  }

  /**
   * Validate API key
   */
  validateApiKey(apiKey) {
    if (this.options.validApiKeys.length === 0) {
      return true; // Allow any API key if none are configured
    }
    return this.options.validApiKeys.includes(apiKey);
  }

  /**
   * Validate token (placeholder for JWT or other token validation)
   */
  async validateToken(token) {
    // This is a placeholder implementation
    // In a real application, you would validate JWT tokens or other authentication tokens
    
    try {
      // Example: Simple token validation
      if (token === 'valid-token') {
        return { id: 'user123', name: 'Test User' };
      }
      
      // Example: JWT validation (you would use a proper JWT library)
      // const decoded = jwt.verify(token, process.env.JWT_SECRET);
      // return decoded;
      
      throw new Error('Invalid token');
    } catch (error) {
      throw new Error('Token validation failed');
    }
  }

  /**
   * Check if user has permission for specific action
   */
  authorize(user, action, resource = null) {
    // Basic authorization logic
    // In a real application, you would implement proper role-based access control

    // If user is not defined, treat as anonymous
    if (!user || !user.type) {
      user = { type: 'anonymous' };
    }

    if (user.type === 'anonymous') {
      // Anonymous users have broad permissions for development
      const allowedActions = [
        'mock:generate', 'mock:generateBatch', 'mock:generateStream',
        'mock:subscribe', 'mock:unsubscribe', 'mock:validateTemplate', 'mock:getTemplates',
        'system:ping', 'system:getInfo', 'system:getMetrics',
        'room:join', 'room:leave',
        'namespace:join'
      ];
      return allowedActions.includes(action);
    }

    if (user.type === 'api_key' || user.type === 'token') {
      // Authenticated users have more permissions
      const restrictedActions = ['system:broadcast', 'system:listConnections'];
      return !restrictedActions.includes(action);
    }

    return false;
  }
}

/**
 * Rate Limiting Middleware
 */
class WebSocketRateLimit {
  constructor(options = {}) {
    this.options = {
      windowMs: options.windowMs || 60000, // 1 minute
      maxRequests: options.maxRequests || 100,
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      skipFailedRequests: options.skipFailedRequests || false,
      ...options
    };

    this.clients = new Map();
  }

  /**
   * Check if request is within rate limit
   */
  checkRateLimit(connectionId, clientInfo) {
    const now = Date.now();
    const windowStart = now - this.options.windowMs;

    // Get or create client record
    let clientRecord = this.clients.get(connectionId);
    if (!clientRecord) {
      clientRecord = {
        requests: [],
        firstRequest: now
      };
      this.clients.set(connectionId, clientRecord);
    }

    // Remove old requests outside the window
    clientRecord.requests = clientRecord.requests.filter(timestamp => timestamp > windowStart);

    // Check if limit exceeded
    if (clientRecord.requests.length >= this.options.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: clientRecord.requests[0] + this.options.windowMs
      };
    }

    // Add current request
    clientRecord.requests.push(now);

    return {
      allowed: true,
      remaining: this.options.maxRequests - clientRecord.requests.length,
      resetTime: windowStart + this.options.windowMs
    };
  }

  /**
   * Clean up old client records
   */
  cleanup() {
    const now = Date.now();
    const cutoff = now - this.options.windowMs * 2; // Keep records for 2 windows

    this.clients.forEach((record, connectionId) => {
      if (record.firstRequest < cutoff && record.requests.length === 0) {
        this.clients.delete(connectionId);
      }
    });
  }

  /**
   * Remove client record
   */
  removeClient(connectionId) {
    this.clients.delete(connectionId);
  }
}

/**
 * Message Validation Middleware
 */
class WebSocketMessageValidator {
  constructor(options = {}) {
    this.options = {
      maxMessageSize: options.maxMessageSize || 1024 * 1024, // 1MB
      allowedTypes: options.allowedTypes || ['mock', 'system', 'room', 'namespace'],
      requireRequestId: options.requireRequestId || false,
      ...options
    };
  }

  /**
   * Validate incoming message
   */
  validateMessage(data, connectionId) {
    try {
      // Check message size
      if (data.length > this.options.maxMessageSize) {
        return {
          valid: false,
          error: 'Message too large',
          code: 'MESSAGE_TOO_LARGE'
        };
      }

      // Parse message
      let message;
      try {
        message = JSON.parse(data);
      } catch (parseError) {
        return {
          valid: false,
          error: 'Invalid JSON format',
          code: 'INVALID_JSON'
        };
      }

      // Validate message structure
      if (!message.type || !message.event) {
        return {
          valid: false,
          error: 'Message must have type and event fields',
          code: 'INVALID_STRUCTURE'
        };
      }

      // Validate message type
      if (!this.options.allowedTypes.includes(message.type)) {
        return {
          valid: false,
          error: `Message type '${message.type}' not allowed`,
          code: 'INVALID_TYPE'
        };
      }

      // Validate request ID if required
      if (this.options.requireRequestId && (!message.data || !message.data.requestId)) {
        return {
          valid: false,
          error: 'Request ID is required',
          code: 'MISSING_REQUEST_ID'
        };
      }

      return {
        valid: true,
        message
      };

    } catch (error) {
      return {
        valid: false,
        error: 'Message validation failed',
        code: 'VALIDATION_ERROR'
      };
    }
  }
}

module.exports = {
  WebSocketAuth,
  WebSocketRateLimit,
  WebSocketMessageValidator
};
