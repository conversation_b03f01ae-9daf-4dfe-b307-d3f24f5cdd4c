/**
 * WebSocket Server for Mirage Dynamic Mock API Server
 * 
 * This file implements a custom WebSocket server with advanced features:
 * - Bi-directional real-time communication
 * - Custom event-based messaging
 * - Automatic reconnection support
 * - Heartbeat/Keep-alive (Ping/Pong)
 * - Room/Channel system (Pub/Sub)
 * - Binary data support
 * - Custom protocol with compression
 * - Performance optimization
 */

const WebSocket = require('ws');
const msgpack = require('msgpack5')();
const EventEmitter = require('events');
const { generateMockData } = require('../controllers/mock/helpers');

class MirageWebSocketServer extends EventEmitter {
  constructor(server, options = {}) {
    super();
    
    this.server = server;
    this.options = {
      heartbeatInterval: options.heartbeatInterval || 30000, // 30 seconds
      heartbeatTimeout: options.heartbeatTimeout || 5000,    // 5 seconds
      maxReconnectAttempts: options.maxReconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 1000,        // 1 second
      compression: options.compression || true,
      binaryProtocol: options.binaryProtocol || false,
      ...options
    };
    
    // WebSocket server instance
    this.wss = null;
    
    // Connection management
    this.connections = new Map();
    this.rooms = new Map();
    this.namespaces = new Map();
    
    // Event handlers map
    this.handlers = new Map();
    
    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      messagesSent: 0,
      messagesReceived: 0,
      bytesTransferred: 0
    };
    
    this.initialize();
  }

  /**
   * Initialize the WebSocket server
   */
  initialize() {
    this.wss = new WebSocket.Server({
      server: this.server,
      path: '/ws',
      perMessageDeflate: this.options.compression
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.setupDefaultHandlers();
    this.startHeartbeat();
    
    console.log('WebSocket server initialized on /ws');
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, req) {
    const connectionId = this.generateConnectionId();
    const clientInfo = {
      id: connectionId,
      ws: ws,
      ip: req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      connectedAt: new Date(),
      lastPing: Date.now(),
      isAlive: true,
      rooms: new Set(),
      namespace: 'default'
    };

    // Store connection
    this.connections.set(connectionId, clientInfo);
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;

    // Setup connection event handlers
    ws.on('message', (data) => this.handleMessage(connectionId, data));
    ws.on('close', () => this.handleDisconnection(connectionId));
    ws.on('error', (error) => this.handleError(connectionId, error));
    ws.on('pong', () => this.handlePong(connectionId));

    // Send welcome message
    this.sendToConnection(connectionId, {
      type: 'connection',
      event: 'connected',
      data: {
        connectionId: connectionId,
        serverTime: new Date().toISOString(),
        features: {
          heartbeat: true,
          rooms: true,
          namespaces: true,
          binaryData: true,
          compression: this.options.compression,
          mockDataGeneration: true
        }
      }
    });

    this.emit('connection', connectionId, clientInfo);
    console.log(`WebSocket client connected: ${connectionId} from ${clientInfo.ip}`);
  }

  /**
   * Handle incoming messages
   */
  handleMessage(connectionId, data) {
    try {
      let message;
      
      // Handle binary data
      if (Buffer.isBuffer(data)) {
        if (this.options.binaryProtocol) {
          message = msgpack.decode(data);
        } else {
          message = JSON.parse(data.toString());
        }
      } else {
        message = JSON.parse(data);
      }

      this.metrics.messagesReceived++;
      this.metrics.bytesTransferred += data.length;

      // Update last activity
      const connection = this.connections.get(connectionId);
      if (connection) {
        connection.lastPing = Date.now();
      }

      // Route message to appropriate handler
      this.routeMessage(connectionId, message);

    } catch (error) {
      console.error(`Error handling message from ${connectionId}:`, error);
      this.sendError(connectionId, 'INVALID_MESSAGE', 'Failed to parse message');
    }
  }

  /**
   * Route message to appropriate handler
   */
  routeMessage(connectionId, message) {
    const { type, event, namespace = 'default' } = message;
    
    if (!type || !event) {
      this.sendError(connectionId, 'INVALID_FORMAT', 'Message must have type and event fields');
      return;
    }

    const handlerKey = `${namespace}:${type}:${event}`;
    const handler = this.handlers.get(handlerKey) || this.handlers.get(`*:${type}:${event}`);

    if (handler) {
      try {
        handler(connectionId, message);
      } catch (error) {
        console.error(`Error in handler ${handlerKey}:`, error);
        this.sendError(connectionId, 'HANDLER_ERROR', 'Internal server error');
      }
    } else {
      this.sendError(connectionId, 'UNKNOWN_EVENT', `No handler found for ${type}:${event}`);
    }
  }

  /**
   * Setup default event handlers
   */
  setupDefaultHandlers() {
    // Ping/Pong handlers
    this.registerHandler('*', 'system', 'ping', (connectionId, message) => {
      this.sendToConnection(connectionId, {
        type: 'system',
        event: 'pong',
        data: { timestamp: Date.now() }
      });
    });

    // Room management handlers
    this.registerHandler('*', 'room', 'join', (connectionId, message) => {
      const { roomName } = message.data || {};
      if (roomName) {
        this.joinRoom(connectionId, roomName);
        this.sendToConnection(connectionId, {
          type: 'room',
          event: 'joined',
          data: { roomName, connectionId }
        });
      }
    });

    this.registerHandler('*', 'room', 'leave', (connectionId, message) => {
      const { roomName } = message.data || {};
      if (roomName) {
        this.leaveRoom(connectionId, roomName);
        this.sendToConnection(connectionId, {
          type: 'room',
          event: 'left',
          data: { roomName, connectionId }
        });
      }
    });

    // Mock data generation handler
    this.registerHandler('*', 'mock', 'generate', (connectionId, message) => {
      this.handleMockDataGeneration(connectionId, message);
    });

    // Namespace handlers
    this.registerHandler('*', 'namespace', 'join', (connectionId, message) => {
      const { namespaceName } = message.data || {};
      if (namespaceName) {
        this.joinNamespace(connectionId, namespaceName);
      }
    });
  }

  /**
   * Handle mock data generation via WebSocket
   */
  handleMockDataGeneration(connectionId, message) {
    try {
      const { app, endpoint, structure, records = 1 } = message.data || {};
      
      if (!structure) {
        this.sendError(connectionId, 'INVALID_STRUCTURE', 'Structure is required for mock data generation');
        return;
      }

      // Generate mock data using existing helper
      const mockData = generateMockData(structure, Number(records));
      
      // Send response
      this.sendToConnection(connectionId, {
        type: 'mock',
        event: 'generated',
        data: {
          success: true,
          app: app || 'websocket',
          endpoint: endpoint || 'generate',
          records: Array.isArray(mockData) ? mockData.length : 1,
          data: mockData,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket: Generated mock data for ${connectionId} - ${app}/${endpoint} with ${records} records`);

    } catch (error) {
      console.error('Error generating mock data via WebSocket:', error);
      this.sendError(connectionId, 'GENERATION_ERROR', 'Failed to generate mock data');
    }
  }

  /**
   * Register event handler
   */
  registerHandler(namespace, type, event, handler) {
    const key = `${namespace}:${type}:${event}`;
    this.handlers.set(key, handler);
  }

  /**
   * Send message to specific connection
   */
  sendToConnection(connectionId, message) {
    const connection = this.connections.get(connectionId);
    if (!connection || connection.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      let data;
      
      if (this.options.binaryProtocol) {
        data = msgpack.encode(message);
      } else {
        data = JSON.stringify(message);
      }

      connection.ws.send(data);
      this.metrics.messagesSent++;
      this.metrics.bytesTransferred += data.length;
      return true;
    } catch (error) {
      console.error(`Error sending message to ${connectionId}:`, error);
      return false;
    }
  }

  /**
   * Send error message
   */
  sendError(connectionId, code, message) {
    this.sendToConnection(connectionId, {
      type: 'error',
      event: 'error',
      data: {
        code,
        message,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Generate unique connection ID
   */
  generateConnectionId() {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle client disconnection
   */
  handleDisconnection(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      // Remove from all rooms
      connection.rooms.forEach(roomName => {
        this.leaveRoom(connectionId, roomName);
      });
      
      // Remove connection
      this.connections.delete(connectionId);
      this.metrics.activeConnections--;
      
      this.emit('disconnection', connectionId);
      console.log(`WebSocket client disconnected: ${connectionId}`);
    }
  }

  /**
   * Handle connection error
   */
  handleError(connectionId, error) {
    console.error(`WebSocket error for ${connectionId}:`, error);
    this.emit('error', connectionId, error);
  }

  /**
   * Handle pong response
   */
  handlePong(connectionId) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.isAlive = true;
      connection.lastPing = Date.now();
    }
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    setInterval(() => {
      this.connections.forEach((connection, connectionId) => {
        if (!connection.isAlive) {
          console.log(`Terminating dead connection: ${connectionId}`);
          connection.ws.terminate();
          this.handleDisconnection(connectionId);
          return;
        }
        
        connection.isAlive = false;
        connection.ws.ping();
      });
    }, this.options.heartbeatInterval);
  }

  /**
   * Join a room
   */
  joinRoom(connectionId, roomName) {
    const connection = this.connections.get(connectionId);
    if (!connection) return false;

    // Add connection to room
    if (!this.rooms.has(roomName)) {
      this.rooms.set(roomName, new Set());
    }
    this.rooms.get(roomName).add(connectionId);
    connection.rooms.add(roomName);

    // Broadcast to room
    this.broadcastToRoom(roomName, {
      type: 'room',
      event: 'user_joined',
      data: { connectionId, roomName }
    }, connectionId);

    return true;
  }

  /**
   * Leave a room
   */
  leaveRoom(connectionId, roomName) {
    const connection = this.connections.get(connectionId);
    if (!connection) return false;

    // Remove connection from room
    if (this.rooms.has(roomName)) {
      this.rooms.get(roomName).delete(connectionId);
      if (this.rooms.get(roomName).size === 0) {
        this.rooms.delete(roomName);
      }
    }
    connection.rooms.delete(roomName);

    // Broadcast to room
    this.broadcastToRoom(roomName, {
      type: 'room',
      event: 'user_left',
      data: { connectionId, roomName }
    }, connectionId);

    return true;
  }

  /**
   * Broadcast message to all connections in a room
   */
  broadcastToRoom(roomName, message, excludeConnectionId = null) {
    const room = this.rooms.get(roomName);
    if (!room) return 0;

    let sentCount = 0;
    room.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        if (this.sendToConnection(connectionId, message)) {
          sentCount++;
        }
      }
    });

    return sentCount;
  }

  /**
   * Join a namespace
   */
  joinNamespace(connectionId, namespaceName) {
    const connection = this.connections.get(connectionId);
    if (!connection) return false;

    connection.namespace = namespaceName;

    if (!this.namespaces.has(namespaceName)) {
      this.namespaces.set(namespaceName, new Set());
    }
    this.namespaces.get(namespaceName).add(connectionId);

    this.sendToConnection(connectionId, {
      type: 'namespace',
      event: 'joined',
      data: { namespaceName, connectionId }
    });

    return true;
  }

  /**
   * Broadcast to all connections
   */
  broadcast(message, excludeConnectionId = null) {
    let sentCount = 0;
    this.connections.forEach((connection, connectionId) => {
      if (connectionId !== excludeConnectionId) {
        if (this.sendToConnection(connectionId, message)) {
          sentCount++;
        }
      }
    });
    return sentCount;
  }

  /**
   * Broadcast to namespace
   */
  broadcastToNamespace(namespaceName, message, excludeConnectionId = null) {
    const namespace = this.namespaces.get(namespaceName);
    if (!namespace) return 0;

    let sentCount = 0;
    namespace.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        if (this.sendToConnection(connectionId, message)) {
          sentCount++;
        }
      }
    });

    return sentCount;
  }

  /**
   * Get server metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      rooms: this.rooms.size,
      namespaces: this.namespaces.size,
      uptime: process.uptime()
    };
  }

  /**
   * Get connection info
   */
  getConnectionInfo(connectionId) {
    const connection = this.connections.get(connectionId);
    if (!connection) return null;

    return {
      id: connection.id,
      ip: connection.ip,
      userAgent: connection.userAgent,
      connectedAt: connection.connectedAt,
      namespace: connection.namespace,
      rooms: Array.from(connection.rooms),
      isAlive: connection.isAlive
    };
  }

  /**
   * Get all connections
   */
  getAllConnections() {
    const connections = [];
    this.connections.forEach((connection, connectionId) => {
      connections.push(this.getConnectionInfo(connectionId));
    });
    return connections;
  }

  /**
   * Close server
   */
  close() {
    if (this.wss) {
      this.wss.close();
      console.log('WebSocket server closed');
    }
  }
}

module.exports = MirageWebSocketServer;
