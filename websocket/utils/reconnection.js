/**
 * WebSocket Reconnection Utility
 * 
 * This utility provides automatic reconnection functionality for WebSocket clients
 * with exponential backoff strategy
 */

class WebSocketReconnection {
  constructor(url, options = {}) {
    this.url = url;
    this.options = {
      maxReconnectAttempts: options.maxReconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 1000,
      maxReconnectDelay: options.maxReconnectDelay || 30000,
      backoffFactor: options.backoffFactor || 2,
      jitter: options.jitter || true,
      ...options
    };
    
    this.ws = null;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.isConnecting = false;
    this.isManualClose = false;
    
    // Event handlers
    this.onOpen = null;
    this.onMessage = null;
    this.onClose = null;
    this.onError = null;
    this.onReconnecting = null;
    this.onReconnected = null;
    this.onMaxReconnectAttemptsReached = null;
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    this.isManualClose = false;

    try {
      this.ws = new WebSocket(this.url);
      this.setupEventHandlers();
    } catch (error) {
      this.isConnecting = false;
      this.handleError(error);
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    this.ws.onopen = (event) => {
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.onOpen) {
        this.onOpen(event);
      }

      if (this.reconnectAttempts > 0 && this.onReconnected) {
        this.onReconnected(event);
      }
    };

    this.ws.onmessage = (event) => {
      if (this.onMessage) {
        this.onMessage(event);
      }
    };

    this.ws.onclose = (event) => {
      this.isConnecting = false;
      
      if (this.onClose) {
        this.onClose(event);
      }

      if (!this.isManualClose) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (event) => {
      this.isConnecting = false;
      this.handleError(event);
    };
  }

  /**
   * Handle WebSocket errors
   */
  handleError(error) {
    if (this.onError) {
      this.onError(error);
    }

    if (!this.isManualClose) {
      this.scheduleReconnect();
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      if (this.onMaxReconnectAttemptsReached) {
        this.onMaxReconnectAttemptsReached();
      }
      return;
    }

    this.reconnectAttempts++;
    
    let delay = this.options.reconnectDelay * Math.pow(this.options.backoffFactor, this.reconnectAttempts - 1);
    delay = Math.min(delay, this.options.maxReconnectDelay);
    
    // Add jitter to prevent thundering herd
    if (this.options.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }

    if (this.onReconnecting) {
      this.onReconnecting(this.reconnectAttempts, delay);
    }

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Send message
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(data);
      return true;
    }
    return false;
  }

  /**
   * Close connection manually
   */
  close(code = 1000, reason = '') {
    this.isManualClose = true;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.ws) {
      this.ws.close(code, reason);
    }
  }

  /**
   * Get current connection state
   */
  getState() {
    if (!this.ws) return 'DISCONNECTED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'CONNECTED';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'DISCONNECTED';
      default:
        return 'UNKNOWN';
    }
  }

  /**
   * Check if connected
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Reset reconnection attempts
   */
  resetReconnectAttempts() {
    this.reconnectAttempts = 0;
  }
}

/**
 * Client-side WebSocket wrapper with automatic reconnection
 */
class MirageWebSocketClient extends WebSocketReconnection {
  constructor(url, options = {}) {
    super(url, options);
    
    this.messageQueue = [];
    this.requestCallbacks = new Map();
    this.subscriptions = new Map();
    
    // Setup event handlers
    this.onOpen = this.handleOpen.bind(this);
    this.onMessage = this.handleMessage.bind(this);
    this.onClose = this.handleClose.bind(this);
    this.onError = this.handleError.bind(this);
  }

  /**
   * Handle connection open
   */
  handleOpen(event) {
    console.log('WebSocket connected');
    
    // Send queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.ws.send(message);
    }
  }

  /**
   * Handle incoming messages
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      // Handle request callbacks
      if (message.data && message.data.requestId) {
        const callback = this.requestCallbacks.get(message.data.requestId);
        if (callback) {
          callback(message);
          this.requestCallbacks.delete(message.data.requestId);
          return;
        }
      }

      // Handle subscriptions
      if (message.type && message.event) {
        const subscriptionKey = `${message.type}:${message.event}`;
        const subscription = this.subscriptions.get(subscriptionKey);
        if (subscription) {
          subscription(message);
        }
      }

    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Handle connection close
   */
  handleClose(event) {
    console.log('WebSocket disconnected');
  }

  /**
   * Handle connection error
   */
  handleError(error) {
    console.error('WebSocket error:', error);
  }

  /**
   * Send message with optional callback
   */
  sendMessage(message, callback = null) {
    const requestId = this.generateRequestId();
    message.data = message.data || {};
    message.data.requestId = requestId;

    const messageStr = JSON.stringify(message);

    if (callback) {
      this.requestCallbacks.set(requestId, callback);
    }

    if (this.isConnected()) {
      this.ws.send(messageStr);
    } else {
      this.messageQueue.push(messageStr);
    }

    return requestId;
  }

  /**
   * Subscribe to events
   */
  subscribe(type, event, callback) {
    const subscriptionKey = `${type}:${event}`;
    this.subscriptions.set(subscriptionKey, callback);
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(type, event) {
    const subscriptionKey = `${type}:${event}`;
    this.subscriptions.delete(subscriptionKey);
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate mock data
   */
  generateMockData(structure, records = 1, callback = null) {
    return this.sendMessage({
      type: 'mock',
      event: 'generate',
      data: {
        structure,
        records
      }
    }, callback);
  }

  /**
   * Join room
   */
  joinRoom(roomName, callback = null) {
    return this.sendMessage({
      type: 'room',
      event: 'join',
      data: {
        roomName
      }
    }, callback);
  }

  /**
   * Leave room
   */
  leaveRoom(roomName, callback = null) {
    return this.sendMessage({
      type: 'room',
      event: 'leave',
      data: {
        roomName
      }
    }, callback);
  }

  /**
   * Ping server
   */
  ping(callback = null) {
    return this.sendMessage({
      type: 'system',
      event: 'ping',
      data: {}
    }, callback);
  }
}

// Export for both Node.js and browser environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { WebSocketReconnection, MirageWebSocketClient };
} else if (typeof window !== 'undefined') {
  window.WebSocketReconnection = WebSocketReconnection;
  window.MirageWebSocketClient = MirageWebSocketClient;
}
