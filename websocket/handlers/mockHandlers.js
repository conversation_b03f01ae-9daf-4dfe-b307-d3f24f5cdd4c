/**
 * WebSocket Mock Data Handlers
 * 
 * This file contains handlers for mock data generation via WebSocket
 */

const { generateMockData } = require('../../controllers/mock/helpers');

class MockHandlers {
  constructor(wsServer) {
    this.wsServer = wsServer;
    this.registerHandlers();
  }

  /**
   * Register all mock data handlers
   */
  registerHandlers() {
    // Single mock data generation
    this.wsServer.registerHandler('*', 'mock', 'generate', this.handleGenerate.bind(this));
    
    // Batch mock data generation
    this.wsServer.registerHandler('*', 'mock', 'generateBatch', this.handleGenerateBatch.bind(this));
    
    // Stream mock data generation
    this.wsServer.registerHandler('*', 'mock', 'generateStream', this.handleGenerateStream.bind(this));
    
    // Real-time mock data updates
    this.wsServer.registerHandler('*', 'mock', 'subscribe', this.handleSubscribe.bind(this));
    this.wsServer.registerHandler('*', 'mock', 'unsubscribe', this.handleUnsubscribe.bind(this));
    
    // Template validation
    this.wsServer.registerHandler('*', 'mock', 'validateTemplate', this.handleValidateTemplate.bind(this));
    
    // Get available templates
    this.wsServer.registerHandler('*', 'mock', 'getTemplates', this.handleGetTemplates.bind(this));
  }

  /**
   * Handle single mock data generation
   */
  handleGenerate(connectionId, message) {
    try {
      const { app, endpoint, structure, records = 1, requestId } = message.data || {};
      
      if (!structure) {
        this.sendError(connectionId, 'INVALID_STRUCTURE', 'Structure is required for mock data generation', requestId);
        return;
      }

      // Generate mock data
      const mockData = generateMockData(structure, Number(records));
      
      // Send response
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'generated',
        data: {
          success: true,
          app: app || 'websocket',
          endpoint: endpoint || 'generate',
          records: Array.isArray(mockData) ? mockData.length : 1,
          data: mockData,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket Mock: Generated data for ${connectionId} - ${app}/${endpoint} with ${records} records`);

    } catch (error) {
      console.error('Error generating mock data via WebSocket:', error);
      this.sendError(connectionId, 'GENERATION_ERROR', 'Failed to generate mock data', message.data?.requestId);
    }
  }

  /**
   * Handle batch mock data generation
   */
  handleGenerateBatch(connectionId, message) {
    try {
      const { requests, requestId } = message.data || {};
      
      if (!Array.isArray(requests) || requests.length === 0) {
        this.sendError(connectionId, 'INVALID_BATCH', 'Requests array is required for batch generation', requestId);
        return;
      }

      const results = [];
      
      for (const request of requests) {
        try {
          const { app, endpoint, structure, records = 1 } = request;
          if (structure) {
            const mockData = generateMockData(structure, Number(records));
            results.push({
              success: true,
              app: app || 'websocket',
              endpoint: endpoint || 'generate',
              records: Array.isArray(mockData) ? mockData.length : 1,
              data: mockData
            });
          } else {
            results.push({
              success: false,
              error: 'Structure is required'
            });
          }
        } catch (error) {
          results.push({
            success: false,
            error: error.message
          });
        }
      }
      
      // Send batch response
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'batchGenerated',
        data: {
          success: true,
          results,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket Mock: Generated batch data for ${connectionId} - ${requests.length} requests`);

    } catch (error) {
      console.error('Error generating batch mock data via WebSocket:', error);
      this.sendError(connectionId, 'BATCH_ERROR', 'Failed to generate batch mock data', message.data?.requestId);
    }
  }

  /**
   * Handle stream mock data generation
   */
  handleGenerateStream(connectionId, message) {
    try {
      const { app, endpoint, structure, interval = 1000, duration = 10000, requestId } = message.data || {};
      
      if (!structure) {
        this.sendError(connectionId, 'INVALID_STRUCTURE', 'Structure is required for stream generation', requestId);
        return;
      }

      let streamCount = 0;
      const maxStreams = Math.floor(duration / interval);
      
      const streamInterval = setInterval(() => {
        try {
          const mockData = generateMockData(structure, 1);
          
          this.wsServer.sendToConnection(connectionId, {
            type: 'mock',
            event: 'streamData',
            data: {
              success: true,
              app: app || 'websocket',
              endpoint: endpoint || 'stream',
              data: mockData,
              streamIndex: streamCount,
              requestId,
              timestamp: new Date().toISOString()
            }
          });

          streamCount++;
          
          if (streamCount >= maxStreams) {
            clearInterval(streamInterval);
            
            // Send stream complete message
            this.wsServer.sendToConnection(connectionId, {
              type: 'mock',
              event: 'streamComplete',
              data: {
                success: true,
                totalStreams: streamCount,
                requestId,
                timestamp: new Date().toISOString()
              }
            });
          }
        } catch (error) {
          clearInterval(streamInterval);
          this.sendError(connectionId, 'STREAM_ERROR', 'Error in stream generation', requestId);
        }
      }, interval);

      // Send stream started message
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'streamStarted',
        data: {
          success: true,
          interval,
          duration,
          expectedStreams: maxStreams,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket Mock: Started stream for ${connectionId} - ${app}/${endpoint}`);

    } catch (error) {
      console.error('Error starting mock data stream via WebSocket:', error);
      this.sendError(connectionId, 'STREAM_START_ERROR', 'Failed to start stream generation', message.data?.requestId);
    }
  }

  /**
   * Handle subscription to real-time mock data updates
   */
  handleSubscribe(connectionId, message) {
    try {
      const { topic, structure, interval = 5000, requestId } = message.data || {};
      
      if (!topic || !structure) {
        this.sendError(connectionId, 'INVALID_SUBSCRIPTION', 'Topic and structure are required for subscription', requestId);
        return;
      }

      // Join subscription room
      const roomName = `subscription:${topic}`;
      this.wsServer.joinRoom(connectionId, roomName);
      
      // Store subscription info
      const connection = this.wsServer.connections.get(connectionId);
      if (connection) {
        if (!connection.subscriptions) {
          connection.subscriptions = new Map();
        }
        connection.subscriptions.set(topic, { structure, interval });
      }

      // Send subscription confirmation
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'subscribed',
        data: {
          success: true,
          topic,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket Mock: Subscribed ${connectionId} to topic ${topic}`);

    } catch (error) {
      console.error('Error subscribing to mock data updates:', error);
      this.sendError(connectionId, 'SUBSCRIPTION_ERROR', 'Failed to subscribe to updates', message.data?.requestId);
    }
  }

  /**
   * Handle unsubscription from real-time mock data updates
   */
  handleUnsubscribe(connectionId, message) {
    try {
      const { topic, requestId } = message.data || {};
      
      if (!topic) {
        this.sendError(connectionId, 'INVALID_UNSUBSCRIPTION', 'Topic is required for unsubscription', requestId);
        return;
      }

      // Leave subscription room
      const roomName = `subscription:${topic}`;
      this.wsServer.leaveRoom(connectionId, roomName);
      
      // Remove subscription info
      const connection = this.wsServer.connections.get(connectionId);
      if (connection && connection.subscriptions) {
        connection.subscriptions.delete(topic);
      }

      // Send unsubscription confirmation
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'unsubscribed',
        data: {
          success: true,
          topic,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

      console.log(`WebSocket Mock: Unsubscribed ${connectionId} from topic ${topic}`);

    } catch (error) {
      console.error('Error unsubscribing from mock data updates:', error);
      this.sendError(connectionId, 'UNSUBSCRIPTION_ERROR', 'Failed to unsubscribe from updates', message.data?.requestId);
    }
  }

  /**
   * Handle template validation
   */
  handleValidateTemplate(connectionId, message) {
    try {
      const { structure, requestId } = message.data || {};
      
      if (!structure) {
        this.sendError(connectionId, 'INVALID_TEMPLATE', 'Structure is required for validation', requestId);
        return;
      }

      // Try to generate a sample to validate the template
      try {
        const sampleData = generateMockData(structure, 1);
        
        this.wsServer.sendToConnection(connectionId, {
          type: 'mock',
          event: 'templateValidated',
          data: {
            success: true,
            valid: true,
            sampleData,
            requestId,
            timestamp: new Date().toISOString()
          }
        });
      } catch (validationError) {
        this.wsServer.sendToConnection(connectionId, {
          type: 'mock',
          event: 'templateValidated',
          data: {
            success: true,
            valid: false,
            error: validationError.message,
            requestId,
            timestamp: new Date().toISOString()
          }
        });
      }

    } catch (error) {
      console.error('Error validating template:', error);
      this.sendError(connectionId, 'VALIDATION_ERROR', 'Failed to validate template', message.data?.requestId);
    }
  }

  /**
   * Handle get available templates
   */
  handleGetTemplates(connectionId, message) {
    try {
      const { requestId } = message.data || {};
      
      const templates = {
        personal: [
          "person.fullName", "person.firstName", "person.lastName",
          "person.jobTitle", "person.prefix", "person.suffix",
          "person.gender", "person.bio"
        ],
        contact: [
          "internet.email", "internet.url", "internet.domainName",
          "phone.number", "phone.number(\"###-###-####\")"
        ],
        location: [
          "location.street", "location.city", "location.state",
          "location.country", "location.zipCode",
          "location.latitude", "location.longitude"
        ],
        business: [
          "company.name", "company.catchPhrase",
          "finance.amount", "finance.currencyCode"
        ],
        datetime: [
          "date.past", "date.future", "date.birthdate",
          "date.recent", "date.soon"
        ],
        numbers: [
          "integer(min, max)", "floating(min, max, precision)",
          "boolean"
        ],
        text: [
          "lorem.words(count)", "lorem.sentence",
          "lorem.paragraph", "lorem.text"
        ],
        identifiers: [
          "guid", "objectId()", "random(\"option1\", \"option2\")"
        ]
      };
      
      this.wsServer.sendToConnection(connectionId, {
        type: 'mock',
        event: 'templatesRetrieved',
        data: {
          success: true,
          templates,
          requestId,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Error retrieving templates:', error);
      this.sendError(connectionId, 'TEMPLATES_ERROR', 'Failed to retrieve templates', message.data?.requestId);
    }
  }

  /**
   * Send error message
   */
  sendError(connectionId, code, message, requestId = null) {
    this.wsServer.sendToConnection(connectionId, {
      type: 'mock',
      event: 'error',
      data: {
        success: false,
        code,
        message,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }
}

module.exports = MockHandlers;
