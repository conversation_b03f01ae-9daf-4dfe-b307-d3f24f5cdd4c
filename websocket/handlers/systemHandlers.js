/**
 * WebSocket System Handlers
 * 
 * This file contains handlers for system-level WebSocket operations
 */

class SystemHandlers {
  constructor(wsServer) {
    this.wsServer = wsServer;
    this.registerHandlers();
  }

  /**
   * Register all system handlers
   */
  registerHandlers() {
    // Connection management
    this.wsServer.registerHandler('*', 'system', 'ping', this.handlePing.bind(this));
    this.wsServer.registerHandler('*', 'system', 'getInfo', this.handleGetInfo.bind(this));
    this.wsServer.registerHandler('*', 'system', 'getMetrics', this.handleGetMetrics.bind(this));
    
    // Room management
    this.wsServer.registerHandler('*', 'system', 'listRooms', this.handleListRooms.bind(this));
    this.wsServer.registerHandler('*', 'system', 'getRoomInfo', this.handleGetRoomInfo.bind(this));
    
    // Namespace management
    this.wsServer.registerHandler('*', 'system', 'listNamespaces', this.handleListNamespaces.bind(this));
    
    // Connection management
    this.wsServer.registerHandler('*', 'system', 'listConnections', this.handleListConnections.bind(this));
    this.wsServer.registerHandler('*', 'system', 'getConnectionInfo', this.handleGetConnectionInfo.bind(this));
    
    // Broadcasting
    this.wsServer.registerHandler('*', 'system', 'broadcast', this.handleBroadcast.bind(this));
    this.wsServer.registerHandler('*', 'system', 'broadcastToRoom', this.handleBroadcastToRoom.bind(this));
    this.wsServer.registerHandler('*', 'system', 'broadcastToNamespace', this.handleBroadcastToNamespace.bind(this));
  }

  /**
   * Handle ping request
   */
  handlePing(connectionId, message) {
    const { requestId } = message.data || {};
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'pong',
      data: {
        success: true,
        timestamp: Date.now(),
        serverTime: new Date().toISOString(),
        requestId
      }
    });
  }

  /**
   * Handle get connection info request
   */
  handleGetInfo(connectionId, message) {
    const { requestId } = message.data || {};
    const connectionInfo = this.wsServer.getConnectionInfo(connectionId);
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'info',
      data: {
        success: true,
        connection: connectionInfo,
        server: {
          version: '1.0.0',
          features: {
            heartbeat: true,
            rooms: true,
            namespaces: true,
            binaryData: true,
            compression: this.wsServer.options.compression,
            mockDataGeneration: true
          }
        },
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle get metrics request
   */
  handleGetMetrics(connectionId, message) {
    const { requestId } = message.data || {};
    const metrics = this.wsServer.getMetrics();
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'metrics',
      data: {
        success: true,
        metrics,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle list rooms request
   */
  handleListRooms(connectionId, message) {
    const { requestId } = message.data || {};
    
    const rooms = [];
    this.wsServer.rooms.forEach((connections, roomName) => {
      rooms.push({
        name: roomName,
        connectionCount: connections.size,
        connections: Array.from(connections)
      });
    });
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'roomsList',
      data: {
        success: true,
        rooms,
        totalRooms: rooms.length,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle get room info request
   */
  handleGetRoomInfo(connectionId, message) {
    const { roomName, requestId } = message.data || {};
    
    if (!roomName) {
      this.sendError(connectionId, 'INVALID_ROOM', 'Room name is required', requestId);
      return;
    }
    
    const room = this.wsServer.rooms.get(roomName);
    if (!room) {
      this.sendError(connectionId, 'ROOM_NOT_FOUND', `Room ${roomName} not found`, requestId);
      return;
    }
    
    const roomInfo = {
      name: roomName,
      connectionCount: room.size,
      connections: Array.from(room).map(connId => this.wsServer.getConnectionInfo(connId))
    };
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'roomInfo',
      data: {
        success: true,
        room: roomInfo,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle list namespaces request
   */
  handleListNamespaces(connectionId, message) {
    const { requestId } = message.data || {};
    
    const namespaces = [];
    this.wsServer.namespaces.forEach((connections, namespaceName) => {
      namespaces.push({
        name: namespaceName,
        connectionCount: connections.size,
        connections: Array.from(connections)
      });
    });
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'namespacesList',
      data: {
        success: true,
        namespaces,
        totalNamespaces: namespaces.length,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle list connections request
   */
  handleListConnections(connectionId, message) {
    const { requestId } = message.data || {};
    const connections = this.wsServer.getAllConnections();
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'connectionsList',
      data: {
        success: true,
        connections,
        totalConnections: connections.length,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle get specific connection info request
   */
  handleGetConnectionInfo(connectionId, message) {
    const { targetConnectionId, requestId } = message.data || {};
    
    if (!targetConnectionId) {
      this.sendError(connectionId, 'INVALID_CONNECTION_ID', 'Target connection ID is required', requestId);
      return;
    }
    
    const targetInfo = this.wsServer.getConnectionInfo(targetConnectionId);
    if (!targetInfo) {
      this.sendError(connectionId, 'CONNECTION_NOT_FOUND', `Connection ${targetConnectionId} not found`, requestId);
      return;
    }
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'connectionInfo',
      data: {
        success: true,
        connection: targetInfo,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle broadcast request
   */
  handleBroadcast(connectionId, message) {
    const { broadcastMessage, excludeSelf = true, requestId } = message.data || {};
    
    if (!broadcastMessage) {
      this.sendError(connectionId, 'INVALID_BROADCAST', 'Broadcast message is required', requestId);
      return;
    }
    
    const excludeId = excludeSelf ? connectionId : null;
    const sentCount = this.wsServer.broadcast(broadcastMessage, excludeId);
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'broadcastSent',
      data: {
        success: true,
        sentToConnections: sentCount,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle broadcast to room request
   */
  handleBroadcastToRoom(connectionId, message) {
    const { roomName, broadcastMessage, excludeSelf = true, requestId } = message.data || {};
    
    if (!roomName || !broadcastMessage) {
      this.sendError(connectionId, 'INVALID_ROOM_BROADCAST', 'Room name and broadcast message are required', requestId);
      return;
    }
    
    const excludeId = excludeSelf ? connectionId : null;
    const sentCount = this.wsServer.broadcastToRoom(roomName, broadcastMessage, excludeId);
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'roomBroadcastSent',
      data: {
        success: true,
        roomName,
        sentToConnections: sentCount,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Handle broadcast to namespace request
   */
  handleBroadcastToNamespace(connectionId, message) {
    const { namespaceName, broadcastMessage, excludeSelf = true, requestId } = message.data || {};
    
    if (!namespaceName || !broadcastMessage) {
      this.sendError(connectionId, 'INVALID_NAMESPACE_BROADCAST', 'Namespace name and broadcast message are required', requestId);
      return;
    }
    
    const excludeId = excludeSelf ? connectionId : null;
    const sentCount = this.wsServer.broadcastToNamespace(namespaceName, broadcastMessage, excludeId);
    
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'namespaceBroadcastSent',
      data: {
        success: true,
        namespaceName,
        sentToConnections: sentCount,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Send error message
   */
  sendError(connectionId, code, message, requestId = null) {
    this.wsServer.sendToConnection(connectionId, {
      type: 'system',
      event: 'error',
      data: {
        success: false,
        code,
        message,
        requestId,
        timestamp: new Date().toISOString()
      }
    });
  }
}

module.exports = SystemHandlers;
