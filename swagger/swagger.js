/**
 * Swagger configuration for the Mirage Dynamic Mock API Server
 *
 * This file loads the OpenAPI specification from the YAML file
 * and exports it for use with swagger-ui-express.
 */

const swaggerJsdoc = require('swagger-jsdoc');
const YAML = require('js-yaml');
const fs = require('fs');
const path = require('path');

// Load the YAML file
const yamlPath = path.join(__dirname, 'swagger.yaml');
const yamlSpec = YAML.load(fs.readFileSync(yamlPath, 'utf8'));

// Configure swagger-jsdoc
const options = {
  definition: yamlSpec,
  apis: ['./routes/*.js', './controllers/**/*.js', './app.js'],
};

// Generate the Swagger specification
const specs = swaggerJsdoc(options);

module.exports = specs;
