openapi: 3.0.0
info:
  title: Mirage Dynamic Mock API Server
  version: 1.0.0
  description: A dynamic mock API server that generates realistic mock data based on provided structures using Faker.js
  license:
    name: ISC
    url: https://opensource.org/licenses/ISC
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:3000
    description: Development server

tags:
  - name: Mock
    description: REST Mock API endpoints
  - name: GraphQL
    description: GraphQL API endpoints
  - name: Documentation
    description: API documentation
  - name: AI Onboarding
    description: AI agent onboarding and integration endpoints
  - name: WebSocket
    description: WebSocket API endpoints and information

components:
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message
        errors:
          type: array
          items:
            type: object
            properties:
              msg:
                type: string
                example: Structure is required
              param:
                type: string
                example: structure
              location:
                type: string
                example: body

    MockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          oneOf:
            - type: object
              example:
                id: '{{guid}}'
                name: '{{person.fullName}}'
                email: '{{internet.email}}'
                age: '{{integer(18, 65)}}'
                isActive: '{{boolean}}'
                createdAt: '{{date.past}}'
                address:
                  street: '{{location.street}}'
                  city: '{{location.city}}'
                  state: '{{location.state}}'
                  zipCode: '{{location.zipCode}}'
                  country: '{{location.country}}'
            - type: array
              items:
                type: object
              example:
                - id: '{{guid}}'
                  name: '{{person.fullName}}'
                  email: '{{internet.email}}'
          description: The structure of the mock data to be generated

    MockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: users
        endpoint:
          type: string
          example: list
        records:
          type: integer
          example: 1
        data:
          oneOf:
            - type: object
            - type: array
              items:
                type: object
          example:
            id: '123e4567-e89b-12d3-a456-************'
            name: John Doe
            email: <EMAIL>
            age: 32
            isActive: true
            createdAt: '2023-01-15T08:30:00.000Z'
            address:
              street: 123 Main St
              city: New York
              state: NY
              zipCode: '10001'
              country: USA

    ComplexMockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          type: object
          example:
            _id: '{{objectId()}}'
            guid: '{{guid()}}'
            type: '{{random("person", "company")}}'
            label:
              _id: '{{objectId()}}'
              name: '{{random("client", "reference", "open")}}'
            avatar: https://i.pravatar.cc/500
            name: '{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}'
            email: '{{internet.email()}}'
            phone: '+{{integer(1, 99)}} {{phone.number()}}'
            dob: '{{date.birthdate()}}'
            company:
              _id: '{{objectId()}}'
              companyName: '{{company.name().toUpperCase()}}'
            jobTitle: '{{person.jobTitle()}}'
            department: '{{person.jobArea()}}'
            tags:
              - '{{repeat(7)}}'
              - '{{lorem.words(1)}}'
            friends:
              - '{{repeat(4)}}'
              - _id: '{{objectId()}}'
                name: '{{person.firstName()}} {{person.lastName()}}'
          description: Complex structure with nested objects, arrays, and method chaining

    ComplexMockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: contacts
        endpoint:
          type: string
          example: table
        records:
          type: integer
          example: 1
        data:
          type: array
          items:
            type: object
          example:
            - _id: 682f781e1fea7d5a32f96673
              guid: e646ed09-8ed2-486d-8512-19cee0689e08
              type: company
              label:
                _id: 682f781e1fea7d5a32f96674
                name: reference
              avatar: https://i.pravatar.cc/500
              name: Dr. Alene McCullough
              email: <EMAIL>
              phone: +96 ************
              dob: '1973-11-20T12:05:47.902Z'
              company:
                _id: 682f781e1fea7d5a32f96675
                companyName: CORWIN GROUP
              jobTitle: Forward Accountability Coordinator
              department: Brand
              tags:
                - beatus
                - uberrime
                - abundans
              friends:
                - _id: 682f781e1fea7d5a32f96676
                  name: Alfreda Schumm
                - _id: 682f781e1fea7d5a32f96677
                  name: Virgil Witting

paths:
  /api/mock/{app}/{endpoint}:
    post:
      tags:
        - Mock
      summary: Generate mock data based on the provided structure
      description: This endpoint generates mock data based on the structure provided in the request body. The structure can be an object or an array of objects with templates for generating mock data.
      parameters:
        - name: app
          in: path
          required: true
          schema:
            type: string
          description: Application name (e.g., users, products, orders)
          example: users
        - name: endpoint
          in: path
          required: true
          schema:
            type: string
          description: Endpoint name (e.g., list, details, search)
          example: list
        - name: records
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Number of records to generate
          example: 5
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/MockRequest'
                - $ref: '#/components/schemas/ComplexMockRequest'
      responses:
        '200':
          description: Successful response with generated mock data
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/MockResponse'
                  - $ref: '#/components/schemas/ComplexMockResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /docs:
    get:
      tags:
        - Documentation
      summary: Get HTML documentation
      description: Returns an HTML page with detailed documentation about the API
      responses:
        '200':
          description: HTML documentation page
          content:
            text/html:
              schema:
                type: string

  /graphql:
    post:
      tags:
        - GraphQL
      summary: GraphQL endpoint for queries and mutations
      description: Execute GraphQL queries and mutations for generating mock data
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: string
                  description: GraphQL query or mutation
                  example: |
                    query {
                      getMock(input: {
                        app: "users"
                        endpoint: "list"
                        structure: {
                          id: "{{guid}}"
                          name: "{{person.fullName}}"
                          email: "{{internet.email}}"
                        }
                        records: 3
                      }) {
                        ... on MockDataResponse {
                          success
                          data
                        }
                      }
                    }
                variables:
                  type: object
                  description: GraphQL variables (optional)
                operationName:
                  type: string
                  description: GraphQL operation name (optional)
      responses:
        '200':
          description: GraphQL response
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    description: GraphQL response data
                  errors:
                    type: array
                    items:
                      type: object
                    description: GraphQL errors (if any)
    get:
      tags:
        - GraphQL
      summary: GraphQL Playground
      description: Interactive GraphQL playground for testing queries and mutations
      responses:
        '200':
          description: GraphQL Playground interface
          content:
            text/html:
              schema:
                type: string

  /api-docs:
    get:
      tags:
        - Documentation
      summary: Get Swagger documentation
      description: Returns the Swagger UI for the API documentation
      responses:
        '200':
          description: Swagger UI page
          content:
            text/html:
              schema:
                type: string

  /ai-info:
    get:
      tags:
        - AI Onboarding
      summary: Get AI-friendly API information
      description: Returns comprehensive API information in JSON format specifically designed for AI agents and systems
      responses:
        '200':
          description: AI-friendly API information
          content:
            application/json:
              schema:
                type: object
                properties:
                  api_name:
                    type: string
                    example: Mirage Dynamic Mock API Server
                  version:
                    type: string
                    example: 1.0.0
                  description:
                    type: string
                    example: A dynamic mock API server that generates realistic fake data
                  purpose:
                    type: object
                    properties:
                      primary:
                        type: string
                      use_cases:
                        type: array
                        items:
                          type: string
                  capabilities:
                    type: object
                    description: Detailed API capabilities and features
                  template_system:
                    type: object
                    description: Information about the template system and syntax
                  api_endpoints:
                    type: object
                    description: Complete endpoint documentation
                  quick_start_guide:
                    type: object
                    description: Step-by-step guide for AI agents

  /ai-onboarding.md:
    get:
      tags:
        - AI Onboarding
      summary: Get AI onboarding markdown guide
      description: Returns a detailed markdown guide for AI agent onboarding and integration
      responses:
        '200':
          description: Markdown onboarding guide
          content:
            text/markdown:
              schema:
                type: string

  /ws-info:
    get:
      tags:
        - WebSocket
      summary: Get WebSocket server information
      description: Returns WebSocket server metrics, connection count, and feature information
      responses:
        '200':
          description: WebSocket server information
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  websocket:
                    type: object
                    properties:
                      endpoint:
                        type: string
                        example: /ws
                      protocol:
                        type: string
                        example: ws
                      features:
                        type: object
                        properties:
                          realTimeData:
                            type: boolean
                          rooms:
                            type: boolean
                          namespaces:
                            type: boolean
                          heartbeat:
                            type: boolean
                          compression:
                            type: boolean
                          binaryData:
                            type: boolean
                          mockDataGeneration:
                            type: boolean
                          subscriptions:
                            type: boolean
                      metrics:
                        type: object
                        description: Server performance metrics
                      connections:
                        type: integer
                        description: Number of active connections

  /ws-demo:
    get:
      tags:
        - WebSocket
      summary: Get WebSocket demo page
      description: Returns an interactive HTML page for testing WebSocket functionality
      responses:
        '200':
          description: WebSocket demo HTML page
          content:
            text/html:
              schema:
                type: string

  /ws:
    get:
      tags:
        - WebSocket
      summary: WebSocket connection endpoint
      description: |
        WebSocket endpoint for real-time bidirectional communication.

        **Connection URL**: `ws://localhost:3000/ws`

        **Message Format**:
        ```json
        {
          "type": "mock|system|room|namespace",
          "event": "specific_event_name",
          "data": {
            // Event-specific data
          }
        }
        ```

        **Available Events**:
        - **mock**: generate, generateBatch, generateStream, subscribe, unsubscribe
        - **system**: ping, getInfo, getMetrics, broadcast
        - **room**: join, leave
        - **namespace**: join

        **Features**:
        - Real-time mock data generation
        - Data streaming with configurable intervals
        - Room-based communication
        - Subscription system for automatic updates
        - Heartbeat monitoring
        - Binary data support
        - Message compression
      responses:
        '101':
          description: WebSocket connection established
        '400':
          description: Bad request - Invalid WebSocket upgrade
        '401':
          description: Unauthorized - Authentication failed
