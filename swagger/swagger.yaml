openapi: 3.0.0
info:
  title: Mirage Dynamic Mock API Server
  version: 1.0.0
  description: A dynamic mock API server that generates realistic mock data based on provided structures using Faker.js
  license:
    name: ISC
    url: https://opensource.org/licenses/ISC
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:3000
    description: Development server

tags:
  - name: Mock
    description: Mock API endpoints
  - name: Documentation
    description: API documentation

components:
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message
        errors:
          type: array
          items:
            type: object
            properties:
              msg:
                type: string
                example: Structure is required
              param:
                type: string
                example: structure
              location:
                type: string
                example: body

    MockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          oneOf:
            - type: object
              example:
                id: '{{guid}}'
                name: '{{person.fullName}}'
                email: '{{internet.email}}'
                age: '{{integer(18, 65)}}'
                isActive: '{{boolean}}'
                createdAt: '{{date.past}}'
                address:
                  street: '{{location.street}}'
                  city: '{{location.city}}'
                  state: '{{location.state}}'
                  zipCode: '{{location.zipCode}}'
                  country: '{{location.country}}'
            - type: array
              items:
                type: object
              example:
                - id: '{{guid}}'
                  name: '{{person.fullName}}'
                  email: '{{internet.email}}'
          description: The structure of the mock data to be generated

    MockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: users
        endpoint:
          type: string
          example: list
        records:
          type: integer
          example: 1
        data:
          oneOf:
            - type: object
            - type: array
              items:
                type: object
          example:
            id: '123e4567-e89b-12d3-a456-************'
            name: John Doe
            email: <EMAIL>
            age: 32
            isActive: true
            createdAt: '2023-01-15T08:30:00.000Z'
            address:
              street: 123 Main St
              city: New York
              state: NY
              zipCode: '10001'
              country: USA

    ComplexMockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          type: object
          example:
            _id: '{{objectId()}}'
            guid: '{{guid()}}'
            type: '{{random("person", "company")}}'
            label:
              _id: '{{objectId()}}'
              name: '{{random("client", "reference", "open")}}'
            avatar: https://i.pravatar.cc/500
            name: '{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}'
            email: '{{internet.email()}}'
            phone: '+{{integer(1, 99)}} {{phone.number()}}'
            dob: '{{date.birthdate()}}'
            company:
              _id: '{{objectId()}}'
              companyName: '{{company.name().toUpperCase()}}'
            jobTitle: '{{person.jobTitle()}}'
            department: '{{person.jobArea()}}'
            tags:
              - '{{repeat(7)}}'
              - '{{lorem.words(1)}}'
            friends:
              - '{{repeat(4)}}'
              - _id: '{{objectId()}}'
                name: '{{person.firstName()}} {{person.lastName()}}'
          description: Complex structure with nested objects, arrays, and method chaining

    ComplexMockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: contacts
        endpoint:
          type: string
          example: table
        records:
          type: integer
          example: 1
        data:
          type: array
          items:
            type: object
          example:
            - _id: 682f781e1fea7d5a32f96673
              guid: e646ed09-8ed2-486d-8512-19cee0689e08
              type: company
              label:
                _id: 682f781e1fea7d5a32f96674
                name: reference
              avatar: https://i.pravatar.cc/500
              name: Dr. Alene McCullough
              email: <EMAIL>
              phone: +96 ************
              dob: '1973-11-20T12:05:47.902Z'
              company:
                _id: 682f781e1fea7d5a32f96675
                companyName: CORWIN GROUP
              jobTitle: Forward Accountability Coordinator
              department: Brand
              tags:
                - beatus
                - uberrime
                - abundans
              friends:
                - _id: 682f781e1fea7d5a32f96676
                  name: Alfreda Schumm
                - _id: 682f781e1fea7d5a32f96677
                  name: Virgil Witting

paths:
  /api/mock/{app}/{endpoint}:
    post:
      tags:
        - Mock
      summary: Generate mock data based on the provided structure
      description: This endpoint generates mock data based on the structure provided in the request body. The structure can be an object or an array of objects with templates for generating mock data.
      parameters:
        - name: app
          in: path
          required: true
          schema:
            type: string
          description: Application name (e.g., users, products, orders)
          example: users
        - name: endpoint
          in: path
          required: true
          schema:
            type: string
          description: Endpoint name (e.g., list, details, search)
          example: list
        - name: records
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Number of records to generate
          example: 5
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/MockRequest'
                - $ref: '#/components/schemas/ComplexMockRequest'
      responses:
        '200':
          description: Successful response with generated mock data
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/MockResponse'
                  - $ref: '#/components/schemas/ComplexMockResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /docs:
    get:
      tags:
        - Documentation
      summary: Get HTML documentation
      description: Returns an HTML page with detailed documentation about the API
      responses:
        '200':
          description: HTML documentation page
          content:
            text/html:
              schema:
                type: string

  /api-docs:
    get:
      tags:
        - Documentation
      summary: Get Swagger documentation
      description: Returns the Swagger UI for the API documentation
      responses:
        '200':
          description: Swagger UI page
          content:
            text/html:
              schema:
                type: string
