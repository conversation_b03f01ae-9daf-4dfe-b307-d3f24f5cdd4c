# Mirage - Dynamic Mock API Server

A Node.js Express backend application that provides dynamic mock API endpoints with realistic dummy data generated using Faker.js. This server allows you to define your own data structure and generates mock data based on that structure.

## Features

- Dynamic API endpoints that accept any app and endpoint combination
- Custom data structure definition through request body
- Realistic mock data generation using Faker.js
- Support for nested objects, arrays, and complex data structures
- Method chaining support (e.g., `toUpperCase()`, `toLowerCase()`)
- Comprehensive HTML documentation with examples and usage guides
- Interactive Swagger/OpenAPI documentation
- CORS enabled for all domains (both HTTP and HTTPS)
- Well-structured codebase with separation of concerns

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd mirage

# Install dependencies
npm install
```

## Usage

### Start the server

```bash
# Production mode
npm start

# Development mode (with auto-reload)
npm run dev
```

The server will start on port 3000 by default. You can change this by setting the `PORT` environment variable.

## API Endpoints

### Mock Data Generation

The server provides a dynamic endpoint that can be used to generate mock data:

```
POST /api/mock/:app/:endpoint
```

Where:
- `:app` - Any application name (e.g., users, products, orders)
- `:endpoint` - Any endpoint name (e.g., list, details, search)

### Documentation

The server provides two documentation endpoints:

#### HTML Documentation

```
GET /docs
```

This documentation includes:
- Detailed explanation of the template syntax
- Complete list of available helpers with examples
- Sample requests and responses
- Advanced usage examples

#### Swagger/OpenAPI Documentation

```
GET /api-docs
```

This documentation includes:
- Interactive API documentation
- Request and response schemas
- Try-it-out functionality to test the API directly from the browser
- Detailed parameter descriptions

### Request Body

The request body should contain a `structure` object that defines the structure of the mock data to be generated:

```json
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}",
    "createdAt": "{{date.past}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    }
  }
}
```

### Query Parameters

- `records` - Number of records to generate (default: 1)
  - Example: `/api/mock/users/list?records=5`

### Response

The response will contain the generated mock data based on the provided structure:

```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 32,
    "isActive": true,
    "createdAt": "2023-01-15T08:30:00.000Z",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA"
    }
  }
}
```

## Template Syntax

The structure object supports the following template syntax:

### Basic Syntax

Use double curly braces to indicate a template:

```
{{helperName}}
```

### With Parameters

```
{{helperName(param1, param2, ...)}}
```

### Nested Properties

```
{{category.subcategory}}
```

### Available Helpers

#### ID Generators
- `objectId` - Generate a MongoDB ObjectId
- `guid` - Generate a UUID

#### Boolean Generator
- `boolean` - Generate a random boolean value

#### Person Data
- `person.firstName` - Generate a random first name
- `person.lastName` - Generate a random last name
- `person.fullName` - Generate a random full name
- `person.gender` - Generate a random gender
- `person.jobTitle` - Generate a random job title

#### Internet Data
- `internet.email` - Generate a random email address
- `internet.userName` - Generate a random username
- `internet.url` - Generate a random URL
- `internet.ip` - Generate a random IP address
- `internet.password` - Generate a random password

#### Phone Data
- `phone.number` - Generate a random phone number

#### Location Data
- `location.street` - Generate a random street address
- `location.city` - Generate a random city
- `location.state` - Generate a random state
- `location.country` - Generate a random country
- `location.zipCode` - Generate a random zip code
- `location.latitude` - Generate a random latitude
- `location.longitude` - Generate a random longitude

#### Lorem Ipsum
- `lorem.word` - Generate a random word
- `lorem.words(count)` - Generate random words
- `lorem.sentence` - Generate a random sentence
- `lorem.paragraph` - Generate a random paragraph
- `lorem.paragraphs(count)` - Generate random paragraphs

#### Date
- `date.past` - Generate a date in the past
- `date.future` - Generate a date in the future
- `date.recent` - Generate a recent date
- `date.soon` - Generate a date soon

#### Numbers
- `integer(min, max)` - Generate a random integer between min and max
- `floating(min, max)` - Generate a random float between min and max
- `amount(min, max, decimals)` - Generate a random amount with specified decimals

#### Arrays and Objects
- `random(value1, value2, ...)` - Pick a random value from the provided options
- `repeat(count, operation)` - Repeat an operation count times

#### Commerce
- `commerce.product` - Generate a random product name
- `commerce.price` - Generate a random price
- `commerce.department` - Generate a random department name

#### Custom Helpers
- `timestamp` - Generate current timestamp
- `oneOf(value1, value2, ...)` - Pick a random value from the provided options
- `arrayOf(count, value)` - Generate an array with count items of value

## Project Structure

```
mirage/
├── app.js                  # Main application entry point
├── package.json            # Project metadata and dependencies
├── common/
│   └── errors.js           # Common error handling functions
├── public/
│   └── documentation.html  # HTML documentation page
├── routes/
│   └── mock.js             # API routes definition
├── controllers/
│   └── mock/
│       ├── controller.js   # Controller function for the dynamic endpoint
│       └── helpers.js      # Helper functions for generating mock data
└── swagger/
    ├── swagger.js          # Swagger configuration
    └── swagger.yaml        # OpenAPI specification
```

## Technologies Used

- Node.js
- Express.js
- CORS
- Faker.js (@faker-js/faker)
- Mongoose (for ObjectId generation)
- Express Validator
- Swagger UI Express (for API documentation)
- js-yaml (for parsing YAML files)

## License

ISC
