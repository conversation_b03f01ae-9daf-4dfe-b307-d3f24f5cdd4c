# Mirage - Dynamic Mock API Server

A Node.js Express backend application that provides dynamic mock API endpoints with realistic dummy data generated using Faker.js. This server allows you to define your own data structure and generates mock data based on that structure.

## Features

- **Triple API Support**: REST, GraphQL, and WebSocket APIs for maximum flexibility
- Dynamic API endpoints that accept any app and endpoint combination
- Custom data structure definition through request body
- Realistic mock data generation using Faker.js
- Support for nested objects, arrays, and complex data structures
- Method chaining support (e.g., `toUpperCase()`, `toLowerCase()`)
- **Real-time WebSocket API** with advanced features:
  - Bi-directional real-time communication
  - Custom event-based messaging system
  - Automatic reconnection with exponential backoff
  - Heartbeat/Keep-alive (Ping/Pong)
  - Room/Channel system (Pub/Sub)
  - Binary data support
  - Custom protocol with compression (msgpack)
  - Performance optimization
  - Broadcast helpers and namespace handling
- Comprehensive HTML documentation with examples and usage guides
- Interactive Swagger/OpenAPI documentation for REST API
- Interactive GraphQL playground for GraphQL API
- Interactive WebSocket demo page for real-time testing
- CORS enabled for all domains (both HTTP and HTTPS)
- Well-structured codebase with separation of concerns

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd mirage

# Install dependencies
npm install
```

## Usage

### Start the server

```bash
# Production mode
npm start

# Development mode (with auto-reload)
npm run dev
```

The server will start on port 3000 by default. You can change this by setting the `PORT` environment variable.

## API Endpoints

### REST API

The server provides a dynamic REST endpoint that can be used to generate mock data:

```
POST /api/mock/:app/:endpoint
```

Where:
- `:app` - Any application name (e.g., users, products, orders)
- `:endpoint` - Any endpoint name (e.g., list, details, search)

### GraphQL API

The server also provides a GraphQL endpoint for more flexible data querying:

```
POST /graphql
GET /graphql (GraphQL Playground)
```

Available operations:
- **Query**: `getMock` - For read-only mock data generation
- **Mutation**: `postMock` - For creating new mock data

### WebSocket API

The server provides a real-time WebSocket endpoint for bidirectional communication:

```
WebSocket Connection: ws://localhost:3000/ws
WebSocket Demo: GET /ws-demo
WebSocket Info: GET /ws-info
```

Available event types:
- **mock**: generate, generateBatch, generateStream, subscribe, unsubscribe, validateTemplate, getTemplates
- **system**: ping, getInfo, getMetrics, broadcast, broadcastToRoom, broadcastToNamespace
- **room**: join, leave
- **namespace**: join

WebSocket features:
- Real-time mock data generation and streaming
- Room-based communication for group messaging
- Subscription system for automatic data updates
- Heartbeat monitoring for connection health
- Binary data support with compression
- Automatic reconnection with exponential backoff

### Documentation

The server provides multiple documentation endpoints:

#### HTML Documentation

```
GET /docs
```

This documentation includes:
- Detailed explanation of the template syntax
- Complete list of available helpers with examples
- Sample requests and responses for both REST and GraphQL
- Advanced usage examples

#### Swagger/OpenAPI Documentation

```
GET /api-docs
```

This documentation includes:
- Interactive REST API documentation
- Request and response schemas
- Try-it-out functionality to test the API directly from the browser
- Detailed parameter descriptions

#### GraphQL Playground

```
GET /graphql
```

This documentation includes:
- Interactive GraphQL API testing interface
- Schema explorer
- Query and mutation examples
- Real-time query validation

#### WebSocket Demo & Info

```
GET /ws-demo (Interactive WebSocket testing interface)
GET /ws-info (WebSocket server metrics and information)
```

These endpoints provide:
- Interactive WebSocket demo page for real-time testing
- WebSocket server metrics and connection information
- Live testing of all WebSocket events and features

#### AI Agent Onboarding

```
GET /ai-info (JSON format)
GET /ai-onboarding.md (Markdown format)
```

These endpoints provide:
- Comprehensive API information specifically formatted for AI agents
- Template system documentation and examples
- Use case scenarios and integration patterns
- Quick start guides and best practices
- Common data structure patterns

### Request Body

The request body should contain a `structure` object that defines the structure of the mock data to be generated:

```json
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}",
    "createdAt": "{{date.past}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    }
  }
}
```

### Query Parameters

- `records` - Number of records to generate (default: 1)
  - Example: `/api/mock/users/list?records=5`

### Response

The response will contain the generated mock data based on the provided structure:

```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 32,
    "isActive": true,
    "createdAt": "2023-01-15T08:30:00.000Z",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA"
    }
  }
}
```

## GraphQL Examples

### Query Example

```graphql
query {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
      age: "{{integer(18, 65)}}"
      isActive: "{{boolean}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}
```

### Mutation Example

```graphql
mutation {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      name: "{{person.fullName}}"
      email: "{{internet.email()}}"
      company: {
        _id: "{{objectId()}}"
        name: "{{company.name().toUpperCase()}}"
      }
      address: {
        street: "{{location.street()}}"
        city: "{{location.city()}}"
        country: "{{location.country()}}"
      }
    }
    records: 1
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
    ... on Error {
      success
      message
      errors
    }
  }
}
```

## WebSocket Examples

### Basic Connection and Mock Data Generation

```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:3000/ws');

ws.onopen = function(event) {
  console.log('Connected to WebSocket');

  // Generate mock data
  ws.send(JSON.stringify({
    type: 'mock',
    event: 'generate',
    data: {
      structure: {
        id: '{{guid}}',
        name: '{{person.fullName}}',
        email: '{{internet.email}}'
      },
      records: 3
    }
  }));
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
};
```

### Real-time Data Streaming

```javascript
// Start data streaming
ws.send(JSON.stringify({
  type: 'mock',
  event: 'generateStream',
  data: {
    structure: {
      timestamp: '{{date.recent}}',
      value: '{{integer(1, 100)}}'
    },
    interval: 1000,  // 1 second intervals
    duration: 10000  // 10 seconds total
  }
}));
```

### Room-based Communication

```javascript
// Join a room
ws.send(JSON.stringify({
  type: 'room',
  event: 'join',
  data: {
    roomName: 'analytics-room'
  }
}));

// Broadcast to room
ws.send(JSON.stringify({
  type: 'system',
  event: 'broadcastToRoom',
  data: {
    roomName: 'analytics-room',
    broadcastMessage: {
      type: 'notification',
      message: 'New data available'
    }
  }
}));
```

### Subscription System

```javascript
// Subscribe to automatic updates
ws.send(JSON.stringify({
  type: 'mock',
  event: 'subscribe',
  data: {
    topic: 'user-metrics',
    structure: {
      timestamp: '{{date.recent}}',
      activeUsers: '{{integer(100, 1000)}}'
    }
  }
}));

// Unsubscribe
ws.send(JSON.stringify({
  type: 'mock',
  event: 'unsubscribe',
  data: {
    topic: 'user-metrics'
  }
}));
```

## AI Agent Integration

Mirage is specifically designed to be AI-friendly and provides dedicated endpoints for AI agent onboarding:

### For AI Agents and Systems

1. **Quick Onboarding**: Visit `/ai-info` for comprehensive API information in JSON format
2. **Detailed Guide**: Access `/ai-onboarding.md` for a complete markdown guide
3. **Use Cases**: Perfect for generating mock data in client-side applications, testing scenarios, and prototyping

### Common AI Integration Patterns

```javascript
// User profile generation
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "avatar": "https://i.pravatar.cc/{{integer(100, 500)}}"
  }
}

// Product listing
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{commerce.productName}}",
    "price": "{{finance.amount}}",
    "category": "{{commerce.department}}"
  }
}

// Contact form data
{
  "structure": {
    "_id": "{{objectId()}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "message": "{{lorem.paragraph}}"
  }
}
```

### AI Integration Benefits

- **No Setup Required**: Start generating data immediately
- **Flexible Structures**: Define any data structure on-the-fly
- **Realistic Data**: Uses Faker.js for human-like data generation
- **Method Chaining**: Transform data with built-in string methods
- **Triple APIs**: Choose between REST, GraphQL, and WebSocket based on needs
- **Real-time Capabilities**: Stream live data for dynamic applications
- **Advanced Features**: Rooms, subscriptions, and broadcasting for complex scenarios

## Template Syntax

The structure object supports the following template syntax:

### Basic Syntax

Use double curly braces to indicate a template:

```
{{helperName}}
```

### With Parameters

```
{{helperName(param1, param2, ...)}}
```

### Nested Properties

```
{{category.subcategory}}
```

### Available Helpers

#### ID Generators
- `objectId` - Generate a MongoDB ObjectId
- `guid` - Generate a UUID

#### Boolean Generator
- `boolean` - Generate a random boolean value

#### Person Data
- `person.firstName` - Generate a random first name
- `person.lastName` - Generate a random last name
- `person.fullName` - Generate a random full name
- `person.gender` - Generate a random gender
- `person.jobTitle` - Generate a random job title

#### Internet Data
- `internet.email` - Generate a random email address
- `internet.userName` - Generate a random username
- `internet.url` - Generate a random URL
- `internet.ip` - Generate a random IP address
- `internet.password` - Generate a random password

#### Phone Data
- `phone.number` - Generate a random phone number

#### Location Data
- `location.street` - Generate a random street address
- `location.city` - Generate a random city
- `location.state` - Generate a random state
- `location.country` - Generate a random country
- `location.zipCode` - Generate a random zip code
- `location.latitude` - Generate a random latitude
- `location.longitude` - Generate a random longitude

#### Lorem Ipsum
- `lorem.word` - Generate a random word
- `lorem.words(count)` - Generate random words
- `lorem.sentence` - Generate a random sentence
- `lorem.paragraph` - Generate a random paragraph
- `lorem.paragraphs(count)` - Generate random paragraphs

#### Date
- `date.past` - Generate a date in the past
- `date.future` - Generate a date in the future
- `date.recent` - Generate a recent date
- `date.soon` - Generate a date soon

#### Numbers
- `integer(min, max)` - Generate a random integer between min and max
- `floating(min, max)` - Generate a random float between min and max
- `amount(min, max, decimals)` - Generate a random amount with specified decimals

#### Arrays and Objects
- `random(value1, value2, ...)` - Pick a random value from the provided options
- `repeat(count, operation)` - Repeat an operation count times

#### Commerce
- `commerce.product` - Generate a random product name
- `commerce.price` - Generate a random price
- `commerce.department` - Generate a random department name

#### Custom Helpers
- `timestamp` - Generate current timestamp
- `oneOf(value1, value2, ...)` - Pick a random value from the provided options
- `arrayOf(count, value)` - Generate an array with count items of value

## Project Structure

```
mirage/
├── app.js                  # Main application entry point
├── package.json            # Project metadata and dependencies
├── ai-onboarding.md        # AI agent onboarding guide (markdown)
├── common/
│   └── errors.js           # Common error handling functions
├── public/
│   ├── documentation.html  # HTML documentation page
│   └── websocket-demo.html # Interactive WebSocket demo page
├── routes/
│   └── mock.js             # REST API routes definition
├── controllers/
│   ├── ai-info.js          # AI onboarding information controller
│   └── mock/
│       ├── controller.js   # Controller function for the dynamic endpoint
│       └── helpers.js      # Helper functions for generating mock data
├── graphql/
│   ├── schema.js           # GraphQL schema definition
│   ├── resolvers.js        # GraphQL resolvers
│   └── server.js           # GraphQL server configuration
├── websocket/
│   ├── index.js            # WebSocket integration main file
│   ├── server.js           # WebSocket server implementation
│   ├── handlers/
│   │   ├── mockHandlers.js # Mock data generation handlers
│   │   ├── systemHandlers.js # System event handlers
│   │   └── roomHandlers.js # Room management handlers
│   ├── middleware/
│   │   ├── auth.js         # WebSocket authentication middleware
│   │   └── compression.js  # Message compression middleware
│   └── utils/
│       ├── messageRouter.js # Message routing utilities
│       └── connectionManager.js # Connection management utilities
└── swagger/
    ├── swagger.js          # Swagger configuration
    └── swagger.yaml        # OpenAPI specification
```

## Technologies Used

- Node.js
- Express.js
- CORS
- Faker.js (@faker-js/faker)
- Mongoose (for ObjectId generation)
- Express Validator
- GraphQL (for GraphQL API)
- GraphQL Yoga (for GraphQL server)
- WebSocket (ws) (for WebSocket server)
- msgpack5 (for binary message compression)
- Swagger UI Express (for REST API documentation)
- js-yaml (for parsing YAML files)

## License

ISC
