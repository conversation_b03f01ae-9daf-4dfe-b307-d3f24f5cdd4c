./                                                                                                  000755  000765  000024  00000000000 15014633445 011453  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./AI_ONBOARDING.md                                                                                  000644  000765  000024  00000014571 15013670751 014000  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         # AI Onboarding Guide for Mirage Dynamic Mock API Server

## Project Overview

Mirage is a Node.js Express backend application that provides dynamic mock API endpoints with realistic dummy data generated using Faker.js. The application allows clients to define their own data structure and generates mock data based on that structure.

## Core Requirements

1. **Dynamic Data Generation**:
   - Generate mock data based on client-defined structure
   - Support for nested objects, arrays, and complex data structures
   - Utilize Faker.js for realistic data generation

2. **API Functionality**:
   - Dynamic endpoint that accepts any app and endpoint combination
   - Support for POST operations with structure definition in request body
   - Query parameter support for controlling number of records

3. **CORS Configuration**:
   - Accept requests from all domains (both HTTP and HTTPS)
   - Support for various HTTP methods (GET, POST, PUT, DELETE, OPTIONS)
   - Allow standard headers (Content-Type, Authorization)

4. **Error Handling**:
   - Consistent error response format
   - Validation of request parameters
   - Proper error logging

## Project Structure

```
mirage/
├── app.js                  # Main application entry point
├── package.json            # Project metadata and dependencies
├── common/
│   └── errors.js           # Common error handling functions
├── routes/
│   └── mock.js             # API routes definition
└── controllers/
    └── mock/
        ├── controller.js   # Controller function for the dynamic endpoint
        └── helpers.js      # Helper functions for generating mock data
```

## Key Components

### 1. Main Application (app.js)

The entry point configures Express with necessary middleware, sets up CORS, defines routes, and starts the server.

Key aspects:
- Express initialization
- Middleware configuration (JSON parsing, CORS)
- Route registration
- Server startup

### 2. Routes (routes/mock.js)

Defines the dynamic route for the mock API endpoint.

Key aspects:
- Dynamic route definition with app and endpoint parameters
- Request validation using express-validator
- Routing to the controller function

### 3. Controller (controllers/mock/controller.js)

Contains the logic for handling requests and generating responses.

Key aspects:
- Request validation
- Parameter extraction
- Integration with helper functions for data generation
- Response formatting
- Error handling

### 4. Helpers (controllers/mock/helpers.js)

Contains utility functions for generating mock data using Faker.js.

Key aspects:
- Template parsing and processing
- Helper function definitions for various data types
- Dynamic data generation based on structure
- Support for nested objects and arrays

### 5. Error Handling (common/errors.js)

Contains functions for handling common error scenarios.

Key aspects:
- Consistent error response format
- Functions for different error types (server error, not found, bad request, etc.)

## API Endpoints

### Mock Data Generation

The server provides a dynamic endpoint that can be used to generate mock data:

```
POST /api/mock/:app/:endpoint
```

Where:
- `:app` - Any application name (e.g., users, products, orders)
- `:endpoint` - Any endpoint name (e.g., list, details, search)

### Documentation

The server also provides a documentation endpoint that returns a comprehensive HTML page with detailed information about the API:

```
GET /docs
```

This documentation includes:
- Detailed explanation of the template syntax
- Complete list of available helpers with examples
- Sample requests and responses
- Advanced usage examples

## Template Syntax

The application uses a template syntax for defining data structures:

1. **Basic Syntax**: `{{helperName}}`
2. **With Parameters**: `{{helperName(param1, param2, ...)}}`
3. **Nested Properties**: `{{category.subcategory}}`

## Data Generation Process

1. Client sends a POST request to `/api/mock/:app/:endpoint` with a structure object in the request body
2. The controller validates the request and extracts the structure
3. The structure is passed to the `generateMockData` function
4. The function processes the structure, replacing templates with generated data
5. The generated data is returned to the client

## Example Usage

### Request:

```json
POST /api/mock/users/list?records=2
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "country": "{{location.country}}"
    }
  }
}
```

### Response:

```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 2,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "email": "<EMAIL>",
      "age": 32,
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "country": "USA"
      }
    },
    {
      "id": "223e4567-e89b-12d3-a456-************",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "age": 28,
      "address": {
        "street": "456 Oak Ave",
        "city": "Los Angeles",
        "country": "USA"
      }
    }
  ]
}
```

## Development Guidelines

1. **Code Organization**:
   - Follow the established directory structure
   - Keep controllers thin, move business logic to helpers
   - Use descriptive function and variable names

2. **Error Handling**:
   - Use try/catch blocks for async operations
   - Return consistent error responses
   - Log errors appropriately

3. **Template Processing**:
   - Maintain consistent template syntax
   - Add new helper functions as needed
   - Document new helpers in the README

4. **Testing**:
   - Test with various structure definitions
   - Verify error handling
   - Check response formats

## Running the Application

```bash
# Install dependencies
npm install

# Start in production mode
npm start

# Start in development mode (with auto-reload)
npm run dev
```

The server runs on port 3000 by default, configurable via the PORT environment variable.

## Future Enhancements

Potential areas for improvement:
- Add caching for frequently used structures
- Implement schema validation for structure objects
- Add support for custom helper functions
- Add OpenAPI/Swagger documentation
- Add authentication for protected endpoints
- Implement rate limiting
                                                                                                                                       ./swagger/                                                                                          000755  000765  000024  00000000000 15013676031 013107  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./README.md                                                                                         000644  000765  000024  00000015445 15013676250 012743  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         # Mirage - Dynamic Mock API Server

A Node.js Express backend application that provides dynamic mock API endpoints with realistic dummy data generated using Faker.js. This server allows you to define your own data structure and generates mock data based on that structure.

## Features

- Dynamic API endpoints that accept any app and endpoint combination
- Custom data structure definition through request body
- Realistic mock data generation using Faker.js
- Support for nested objects, arrays, and complex data structures
- Method chaining support (e.g., `toUpperCase()`, `toLowerCase()`)
- Comprehensive HTML documentation with examples and usage guides
- Interactive Swagger/OpenAPI documentation
- CORS enabled for all domains (both HTTP and HTTPS)
- Well-structured codebase with separation of concerns

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd mirage

# Install dependencies
npm install
```

## Usage

### Start the server

```bash
# Production mode
npm start

# Development mode (with auto-reload)
npm run dev
```

The server will start on port 3000 by default. You can change this by setting the `PORT` environment variable.

## API Endpoints

### Mock Data Generation

The server provides a dynamic endpoint that can be used to generate mock data:

```
POST /api/mock/:app/:endpoint
```

Where:
- `:app` - Any application name (e.g., users, products, orders)
- `:endpoint` - Any endpoint name (e.g., list, details, search)

### Documentation

The server provides two documentation endpoints:

#### HTML Documentation

```
GET /docs
```

This documentation includes:
- Detailed explanation of the template syntax
- Complete list of available helpers with examples
- Sample requests and responses
- Advanced usage examples

#### Swagger/OpenAPI Documentation

```
GET /api-docs
```

This documentation includes:
- Interactive API documentation
- Request and response schemas
- Try-it-out functionality to test the API directly from the browser
- Detailed parameter descriptions

### Request Body

The request body should contain a `structure` object that defines the structure of the mock data to be generated:

```json
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}",
    "createdAt": "{{date.past}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    }
  }
}
```

### Query Parameters

- `records` - Number of records to generate (default: 1)
  - Example: `/api/mock/users/list?records=5`

### Response

The response will contain the generated mock data based on the provided structure:

```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 32,
    "isActive": true,
    "createdAt": "2023-01-15T08:30:00.000Z",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA"
    }
  }
}
```

## Template Syntax

The structure object supports the following template syntax:

### Basic Syntax

Use double curly braces to indicate a template:

```
{{helperName}}
```

### With Parameters

```
{{helperName(param1, param2, ...)}}
```

### Nested Properties

```
{{category.subcategory}}
```

### Available Helpers

#### ID Generators
- `objectId` - Generate a MongoDB ObjectId
- `guid` - Generate a UUID

#### Boolean Generator
- `boolean` - Generate a random boolean value

#### Person Data
- `person.firstName` - Generate a random first name
- `person.lastName` - Generate a random last name
- `person.fullName` - Generate a random full name
- `person.gender` - Generate a random gender
- `person.jobTitle` - Generate a random job title

#### Internet Data
- `internet.email` - Generate a random email address
- `internet.userName` - Generate a random username
- `internet.url` - Generate a random URL
- `internet.ip` - Generate a random IP address
- `internet.password` - Generate a random password

#### Phone Data
- `phone.number` - Generate a random phone number

#### Location Data
- `location.street` - Generate a random street address
- `location.city` - Generate a random city
- `location.state` - Generate a random state
- `location.country` - Generate a random country
- `location.zipCode` - Generate a random zip code
- `location.latitude` - Generate a random latitude
- `location.longitude` - Generate a random longitude

#### Lorem Ipsum
- `lorem.word` - Generate a random word
- `lorem.words(count)` - Generate random words
- `lorem.sentence` - Generate a random sentence
- `lorem.paragraph` - Generate a random paragraph
- `lorem.paragraphs(count)` - Generate random paragraphs

#### Date
- `date.past` - Generate a date in the past
- `date.future` - Generate a date in the future
- `date.recent` - Generate a recent date
- `date.soon` - Generate a date soon

#### Numbers
- `integer(min, max)` - Generate a random integer between min and max
- `floating(min, max)` - Generate a random float between min and max
- `amount(min, max, decimals)` - Generate a random amount with specified decimals

#### Arrays and Objects
- `random(value1, value2, ...)` - Pick a random value from the provided options
- `repeat(count, operation)` - Repeat an operation count times

#### Commerce
- `commerce.product` - Generate a random product name
- `commerce.price` - Generate a random price
- `commerce.department` - Generate a random department name

#### Custom Helpers
- `timestamp` - Generate current timestamp
- `oneOf(value1, value2, ...)` - Pick a random value from the provided options
- `arrayOf(count, value)` - Generate an array with count items of value

## Project Structure

```
mirage/
├── app.js                  # Main application entry point
├── package.json            # Project metadata and dependencies
├── common/
│   └── errors.js           # Common error handling functions
├── public/
│   └── documentation.html  # HTML documentation page
├── routes/
│   └── mock.js             # API routes definition
├── controllers/
│   └── mock/
│       ├── controller.js   # Controller function for the dynamic endpoint
│       └── helpers.js      # Helper functions for generating mock data
└── swagger/
    ├── swagger.js          # Swagger configuration
    └── swagger.yaml        # OpenAPI specification
```

## Technologies Used

- Node.js
- Express.js
- CORS
- Faker.js (@faker-js/faker)
- Mongoose (for ObjectId generation)
- Express Validator
- Swagger UI Express (for API documentation)
- js-yaml (for parsing YAML files)

## License

ISC
                                                                                                                                                                                                                           ./common/                                                                                           000755  000765  000024  00000000000 15013667750 012750  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./public/                                                                                           000755  000765  000024  00000000000 15013670655 012734  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./package-lock.json                                                                                 000644  000765  000024  00000171254 15013676136 014704  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         {
  "name": "mirage",
  "version": "1.0.0",
  "lockfileVersion": 3,
  "requires": true,
  "packages": {
    "": {
      "name": "mirage",
      "version": "1.0.0",
      "license": "ISC",
      "dependencies": {
        "@faker-js/faker": "^9.8.0",
        "cors": "^2.8.5",
        "express": "^5.1.0",
        "express-validator": "^7.2.1",
        "js-yaml": "^4.1.0",
        "mongoose": "^8.15.0",
        "swagger-jsdoc": "^6.2.8",
        "swagger-ui-express": "^5.0.1"
      },
      "devDependencies": {
        "nodemon": "^3.1.10"
      }
    },
    "node_modules/@apidevtools/json-schema-ref-parser": {
      "version": "9.1.2",
      "resolved": "https://registry.npmjs.org/@apidevtools/json-schema-ref-parser/-/json-schema-ref-parser-9.1.2.tgz",
      "integrity": "sha512-r1w81DpR+KyRWd3f+rk6TNqMgedmAxZP5v5KWlXQWlgMUUtyEJch0DKEci1SorPMiSeM8XPl7MZ3miJ60JIpQg==",
      "license": "MIT",
      "dependencies": {
        "@jsdevtools/ono": "^7.1.3",
        "@types/json-schema": "^7.0.6",
        "call-me-maybe": "^1.0.1",
        "js-yaml": "^4.1.0"
      }
    },
    "node_modules/@apidevtools/openapi-schemas": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/@apidevtools/openapi-schemas/-/openapi-schemas-2.1.0.tgz",
      "integrity": "sha512-Zc1AlqrJlX3SlpupFGpiLi2EbteyP7fXmUOGup6/DnkRgjP9bgMM/ag+n91rsv0U1Gpz0H3VILA/o3bW7Ua6BQ==",
      "license": "MIT",
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/@apidevtools/swagger-methods": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/@apidevtools/swagger-methods/-/swagger-methods-3.0.2.tgz",
      "integrity": "sha512-QAkD5kK2b1WfjDS/UQn/qQkbwF31uqRjPTrsCs5ZG9BQGAkjwvqGFjjPqAuzac/IYzpPtRzjCP1WrTuAIjMrXg==",
      "license": "MIT"
    },
    "node_modules/@apidevtools/swagger-parser": {
      "version": "10.0.3",
      "resolved": "https://registry.npmjs.org/@apidevtools/swagger-parser/-/swagger-parser-10.0.3.tgz",
      "integrity": "sha512-sNiLY51vZOmSPFZA5TF35KZ2HbgYklQnTSDnkghamzLb3EkNtcQnrBQEj5AOCxHpTtXpqMCRM1CrmV2rG6nw4g==",
      "license": "MIT",
      "dependencies": {
        "@apidevtools/json-schema-ref-parser": "^9.0.6",
        "@apidevtools/openapi-schemas": "^2.0.4",
        "@apidevtools/swagger-methods": "^3.0.2",
        "@jsdevtools/ono": "^7.1.3",
        "call-me-maybe": "^1.0.1",
        "z-schema": "^5.0.1"
      },
      "peerDependencies": {
        "openapi-types": ">=7"
      }
    },
    "node_modules/@faker-js/faker": {
      "version": "9.8.0",
      "resolved": "https://registry.npmjs.org/@faker-js/faker/-/faker-9.8.0.tgz",
      "integrity": "sha512-U9wpuSrJC93jZBxx/Qq2wPjCuYISBueyVUGK7qqdmj7r/nxaxwW8AQDCLeRO7wZnjj94sh3p246cAYjUKuqgfg==",
      "funding": [
        {
          "type": "opencollective",
          "url": "https://opencollective.com/fakerjs"
        }
      ],
      "license": "MIT",
      "engines": {
        "node": ">=18.0.0",
        "npm": ">=9.0.0"
      }
    },
    "node_modules/@jsdevtools/ono": {
      "version": "7.1.3",
      "resolved": "https://registry.npmjs.org/@jsdevtools/ono/-/ono-7.1.3.tgz",
      "integrity": "sha512-4JQNk+3mVzK3xh2rqd6RB4J46qUR19azEHBneZyTZM+c456qOrbbM/5xcR8huNCCcbVt7+UmizG6GuUvPvKUYg==",
      "license": "MIT"
    },
    "node_modules/@mongodb-js/saslprep": {
      "version": "1.2.2",
      "resolved": "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.2.2.tgz",
      "integrity": "sha512-EB0O3SCSNRUFk66iRCpI+cXzIjdswfCs7F6nOC3RAGJ7xr5YhaicvsRwJ9eyzYvYRlCSDUO/c7g4yNulxKC1WA==",
      "license": "MIT",
      "dependencies": {
        "sparse-bitfield": "^3.0.3"
      }
    },
    "node_modules/@scarf/scarf": {
      "version": "1.4.0",
      "resolved": "https://registry.npmjs.org/@scarf/scarf/-/scarf-1.4.0.tgz",
      "integrity": "sha512-xxeapPiUXdZAE3che6f3xogoJPeZgig6omHEy1rIY5WVsB3H2BHNnZH+gHG6x91SCWyQCzWGsuL2Hh3ClO5/qQ==",
      "hasInstallScript": true,
      "license": "Apache-2.0"
    },
    "node_modules/@types/json-schema": {
      "version": "7.0.15",
      "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz",
      "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==",
      "license": "MIT"
    },
    "node_modules/@types/webidl-conversions": {
      "version": "7.0.3",
      "resolved": "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz",
      "integrity": "sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==",
      "license": "MIT"
    },
    "node_modules/@types/whatwg-url": {
      "version": "11.0.5",
      "resolved": "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-11.0.5.tgz",
      "integrity": "sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==",
      "license": "MIT",
      "dependencies": {
        "@types/webidl-conversions": "*"
      }
    },
    "node_modules/accepts": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz",
      "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==",
      "license": "MIT",
      "dependencies": {
        "mime-types": "^3.0.0",
        "negotiator": "^1.0.0"
      },
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/anymatch": {
      "version": "3.1.3",
      "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz",
      "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==",
      "dev": true,
      "license": "ISC",
      "dependencies": {
        "normalize-path": "^3.0.0",
        "picomatch": "^2.0.4"
      },
      "engines": {
        "node": ">= 8"
      }
    },
    "node_modules/argparse": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz",
      "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==",
      "license": "Python-2.0"
    },
    "node_modules/balanced-match": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz",
      "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==",
      "license": "MIT"
    },
    "node_modules/binary-extensions": {
      "version": "2.3.0",
      "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz",
      "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=8"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/body-parser": {
      "version": "2.2.0",
      "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz",
      "integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==",
      "license": "MIT",
      "dependencies": {
        "bytes": "^3.1.2",
        "content-type": "^1.0.5",
        "debug": "^4.4.0",
        "http-errors": "^2.0.0",
        "iconv-lite": "^0.6.3",
        "on-finished": "^2.4.1",
        "qs": "^6.14.0",
        "raw-body": "^3.0.0",
        "type-is": "^2.0.0"
      },
      "engines": {
        "node": ">=18"
      }
    },
    "node_modules/brace-expansion": {
      "version": "1.1.11",
      "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz",
      "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==",
      "license": "MIT",
      "dependencies": {
        "balanced-match": "^1.0.0",
        "concat-map": "0.0.1"
      }
    },
    "node_modules/braces": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz",
      "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "fill-range": "^7.1.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/bson": {
      "version": "6.10.3",
      "resolved": "https://registry.npmjs.org/bson/-/bson-6.10.3.tgz",
      "integrity": "sha512-MTxGsqgYTwfshYWTRdmZRC+M7FnG1b4y7RO7p2k3X24Wq0yv1m77Wsj0BzlPzd/IowgESfsruQCUToa7vbOpPQ==",
      "license": "Apache-2.0",
      "engines": {
        "node": ">=16.20.1"
      }
    },
    "node_modules/bytes": {
      "version": "3.1.2",
      "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz",
      "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/call-bind-apply-helpers": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz",
      "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==",
      "license": "MIT",
      "dependencies": {
        "es-errors": "^1.3.0",
        "function-bind": "^1.1.2"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/call-bound": {
      "version": "1.0.4",
      "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz",
      "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==",
      "license": "MIT",
      "dependencies": {
        "call-bind-apply-helpers": "^1.0.2",
        "get-intrinsic": "^1.3.0"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/call-me-maybe": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/call-me-maybe/-/call-me-maybe-1.0.2.tgz",
      "integrity": "sha512-HpX65o1Hnr9HH25ojC1YGs7HCQLq0GCOibSaWER0eNpgJ/Z1MZv2mTc7+xh6WOPxbRVcmgbv4hGU+uSQ/2xFZQ==",
      "license": "MIT"
    },
    "node_modules/chokidar": {
      "version": "3.6.0",
      "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz",
      "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "anymatch": "~3.1.2",
        "braces": "~3.0.2",
        "glob-parent": "~5.1.2",
        "is-binary-path": "~2.1.0",
        "is-glob": "~4.0.1",
        "normalize-path": "~3.0.0",
        "readdirp": "~3.6.0"
      },
      "engines": {
        "node": ">= 8.10.0"
      },
      "funding": {
        "url": "https://paulmillr.com/funding/"
      },
      "optionalDependencies": {
        "fsevents": "~2.3.2"
      }
    },
    "node_modules/commander": {
      "version": "6.2.0",
      "resolved": "https://registry.npmjs.org/commander/-/commander-6.2.0.tgz",
      "integrity": "sha512-zP4jEKbe8SHzKJYQmq8Y9gYjtO/POJLgIdKgV7B9qNmABVFVc+ctqSX6iXh4mCpJfRBOabiZ2YKPg8ciDw6C+Q==",
      "license": "MIT",
      "engines": {
        "node": ">= 6"
      }
    },
    "node_modules/concat-map": {
      "version": "0.0.1",
      "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz",
      "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==",
      "license": "MIT"
    },
    "node_modules/content-disposition": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz",
      "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==",
      "license": "MIT",
      "dependencies": {
        "safe-buffer": "5.2.1"
      },
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/content-type": {
      "version": "1.0.5",
      "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz",
      "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/cookie": {
      "version": "0.7.2",
      "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz",
      "integrity": "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/cookie-signature": {
      "version": "1.2.2",
      "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz",
      "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==",
      "license": "MIT",
      "engines": {
        "node": ">=6.6.0"
      }
    },
    "node_modules/cors": {
      "version": "2.8.5",
      "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz",
      "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==",
      "license": "MIT",
      "dependencies": {
        "object-assign": "^4",
        "vary": "^1"
      },
      "engines": {
        "node": ">= 0.10"
      }
    },
    "node_modules/debug": {
      "version": "4.4.1",
      "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz",
      "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==",
      "license": "MIT",
      "dependencies": {
        "ms": "^2.1.3"
      },
      "engines": {
        "node": ">=6.0"
      },
      "peerDependenciesMeta": {
        "supports-color": {
          "optional": true
        }
      }
    },
    "node_modules/depd": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz",
      "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/doctrine": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz",
      "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==",
      "license": "Apache-2.0",
      "dependencies": {
        "esutils": "^2.0.2"
      },
      "engines": {
        "node": ">=6.0.0"
      }
    },
    "node_modules/dunder-proto": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz",
      "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==",
      "license": "MIT",
      "dependencies": {
        "call-bind-apply-helpers": "^1.0.1",
        "es-errors": "^1.3.0",
        "gopd": "^1.2.0"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/ee-first": {
      "version": "1.1.1",
      "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz",
      "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==",
      "license": "MIT"
    },
    "node_modules/encodeurl": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz",
      "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/es-define-property": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz",
      "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/es-errors": {
      "version": "1.3.0",
      "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz",
      "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/es-object-atoms": {
      "version": "1.1.1",
      "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz",
      "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==",
      "license": "MIT",
      "dependencies": {
        "es-errors": "^1.3.0"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/escape-html": {
      "version": "1.0.3",
      "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz",
      "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==",
      "license": "MIT"
    },
    "node_modules/esutils": {
      "version": "2.0.3",
      "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz",
      "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==",
      "license": "BSD-2-Clause",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/etag": {
      "version": "1.8.1",
      "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz",
      "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/express": {
      "version": "5.1.0",
      "resolved": "https://registry.npmjs.org/express/-/express-5.1.0.tgz",
      "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==",
      "license": "MIT",
      "dependencies": {
        "accepts": "^2.0.0",
        "body-parser": "^2.2.0",
        "content-disposition": "^1.0.0",
        "content-type": "^1.0.5",
        "cookie": "^0.7.1",
        "cookie-signature": "^1.2.1",
        "debug": "^4.4.0",
        "encodeurl": "^2.0.0",
        "escape-html": "^1.0.3",
        "etag": "^1.8.1",
        "finalhandler": "^2.1.0",
        "fresh": "^2.0.0",
        "http-errors": "^2.0.0",
        "merge-descriptors": "^2.0.0",
        "mime-types": "^3.0.0",
        "on-finished": "^2.4.1",
        "once": "^1.4.0",
        "parseurl": "^1.3.3",
        "proxy-addr": "^2.0.7",
        "qs": "^6.14.0",
        "range-parser": "^1.2.1",
        "router": "^2.2.0",
        "send": "^1.1.0",
        "serve-static": "^2.2.0",
        "statuses": "^2.0.1",
        "type-is": "^2.0.1",
        "vary": "^1.1.2"
      },
      "engines": {
        "node": ">= 18"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/express"
      }
    },
    "node_modules/express-validator": {
      "version": "7.2.1",
      "resolved": "https://registry.npmjs.org/express-validator/-/express-validator-7.2.1.tgz",
      "integrity": "sha512-CjNE6aakfpuwGaHQZ3m8ltCG2Qvivd7RHtVMS/6nVxOM7xVGqr4bhflsm4+N5FP5zI7Zxp+Hae+9RE+o8e3ZOQ==",
      "license": "MIT",
      "dependencies": {
        "lodash": "^4.17.21",
        "validator": "~13.12.0"
      },
      "engines": {
        "node": ">= 8.0.0"
      }
    },
    "node_modules/fill-range": {
      "version": "7.1.1",
      "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz",
      "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "to-regex-range": "^5.0.1"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/finalhandler": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz",
      "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==",
      "license": "MIT",
      "dependencies": {
        "debug": "^4.4.0",
        "encodeurl": "^2.0.0",
        "escape-html": "^1.0.3",
        "on-finished": "^2.4.1",
        "parseurl": "^1.3.3",
        "statuses": "^2.0.1"
      },
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/forwarded": {
      "version": "0.2.0",
      "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz",
      "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/fresh": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz",
      "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/fs.realpath": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz",
      "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==",
      "license": "ISC"
    },
    "node_modules/fsevents": {
      "version": "2.3.3",
      "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz",
      "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==",
      "dev": true,
      "hasInstallScript": true,
      "license": "MIT",
      "optional": true,
      "os": [
        "darwin"
      ],
      "engines": {
        "node": "^8.16.0 || ^10.6.0 || >=11.0.0"
      }
    },
    "node_modules/function-bind": {
      "version": "1.1.2",
      "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz",
      "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==",
      "license": "MIT",
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/get-intrinsic": {
      "version": "1.3.0",
      "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz",
      "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==",
      "license": "MIT",
      "dependencies": {
        "call-bind-apply-helpers": "^1.0.2",
        "es-define-property": "^1.0.1",
        "es-errors": "^1.3.0",
        "es-object-atoms": "^1.1.1",
        "function-bind": "^1.1.2",
        "get-proto": "^1.0.1",
        "gopd": "^1.2.0",
        "has-symbols": "^1.1.0",
        "hasown": "^2.0.2",
        "math-intrinsics": "^1.1.0"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/get-proto": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz",
      "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==",
      "license": "MIT",
      "dependencies": {
        "dunder-proto": "^1.0.1",
        "es-object-atoms": "^1.0.0"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/glob": {
      "version": "7.1.6",
      "resolved": "https://registry.npmjs.org/glob/-/glob-7.1.6.tgz",
      "integrity": "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA==",
      "deprecated": "Glob versions prior to v9 are no longer supported",
      "license": "ISC",
      "dependencies": {
        "fs.realpath": "^1.0.0",
        "inflight": "^1.0.4",
        "inherits": "2",
        "minimatch": "^3.0.4",
        "once": "^1.3.0",
        "path-is-absolute": "^1.0.0"
      },
      "engines": {
        "node": "*"
      },
      "funding": {
        "url": "https://github.com/sponsors/isaacs"
      }
    },
    "node_modules/glob-parent": {
      "version": "5.1.2",
      "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz",
      "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==",
      "dev": true,
      "license": "ISC",
      "dependencies": {
        "is-glob": "^4.0.1"
      },
      "engines": {
        "node": ">= 6"
      }
    },
    "node_modules/gopd": {
      "version": "1.2.0",
      "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz",
      "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/has-flag": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz",
      "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/has-symbols": {
      "version": "1.1.0",
      "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz",
      "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/hasown": {
      "version": "2.0.2",
      "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz",
      "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==",
      "license": "MIT",
      "dependencies": {
        "function-bind": "^1.1.2"
      },
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/http-errors": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz",
      "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==",
      "license": "MIT",
      "dependencies": {
        "depd": "2.0.0",
        "inherits": "2.0.4",
        "setprototypeof": "1.2.0",
        "statuses": "2.0.1",
        "toidentifier": "1.0.1"
      },
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/iconv-lite": {
      "version": "0.6.3",
      "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz",
      "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==",
      "license": "MIT",
      "dependencies": {
        "safer-buffer": ">= 2.1.2 < 3.0.0"
      },
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/ignore-by-default": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz",
      "integrity": "sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA==",
      "dev": true,
      "license": "ISC"
    },
    "node_modules/inflight": {
      "version": "1.0.6",
      "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz",
      "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==",
      "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.",
      "license": "ISC",
      "dependencies": {
        "once": "^1.3.0",
        "wrappy": "1"
      }
    },
    "node_modules/inherits": {
      "version": "2.0.4",
      "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz",
      "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==",
      "license": "ISC"
    },
    "node_modules/ipaddr.js": {
      "version": "1.9.1",
      "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz",
      "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.10"
      }
    },
    "node_modules/is-binary-path": {
      "version": "2.1.0",
      "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz",
      "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "binary-extensions": "^2.0.0"
      },
      "engines": {
        "node": ">=8"
      }
    },
    "node_modules/is-extglob": {
      "version": "2.1.1",
      "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz",
      "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/is-glob": {
      "version": "4.0.3",
      "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz",
      "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "is-extglob": "^2.1.1"
      },
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/is-number": {
      "version": "7.0.0",
      "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz",
      "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=0.12.0"
      }
    },
    "node_modules/is-promise": {
      "version": "4.0.0",
      "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz",
      "integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==",
      "license": "MIT"
    },
    "node_modules/js-yaml": {
      "version": "4.1.0",
      "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz",
      "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==",
      "license": "MIT",
      "dependencies": {
        "argparse": "^2.0.1"
      },
      "bin": {
        "js-yaml": "bin/js-yaml.js"
      }
    },
    "node_modules/kareem": {
      "version": "2.6.3",
      "resolved": "https://registry.npmjs.org/kareem/-/kareem-2.6.3.tgz",
      "integrity": "sha512-C3iHfuGUXK2u8/ipq9LfjFfXFxAZMQJJq7vLS45r3D9Y2xQ/m4S8zaR4zMLFWh9AsNPXmcFfUDhTEO8UIC/V6Q==",
      "license": "Apache-2.0",
      "engines": {
        "node": ">=12.0.0"
      }
    },
    "node_modules/lodash": {
      "version": "4.17.21",
      "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz",
      "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==",
      "license": "MIT"
    },
    "node_modules/lodash.get": {
      "version": "4.4.2",
      "resolved": "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz",
      "integrity": "sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==",
      "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead.",
      "license": "MIT"
    },
    "node_modules/lodash.isequal": {
      "version": "4.5.0",
      "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz",
      "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==",
      "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead.",
      "license": "MIT"
    },
    "node_modules/lodash.mergewith": {
      "version": "4.6.2",
      "resolved": "https://registry.npmjs.org/lodash.mergewith/-/lodash.mergewith-4.6.2.tgz",
      "integrity": "sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==",
      "license": "MIT"
    },
    "node_modules/math-intrinsics": {
      "version": "1.1.0",
      "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz",
      "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      }
    },
    "node_modules/media-typer": {
      "version": "1.1.0",
      "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz",
      "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/memory-pager": {
      "version": "1.5.0",
      "resolved": "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz",
      "integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==",
      "license": "MIT"
    },
    "node_modules/merge-descriptors": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz",
      "integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==",
      "license": "MIT",
      "engines": {
        "node": ">=18"
      },
      "funding": {
        "url": "https://github.com/sponsors/sindresorhus"
      }
    },
    "node_modules/mime-db": {
      "version": "1.54.0",
      "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz",
      "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/mime-types": {
      "version": "3.0.1",
      "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz",
      "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==",
      "license": "MIT",
      "dependencies": {
        "mime-db": "^1.54.0"
      },
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/minimatch": {
      "version": "3.1.2",
      "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz",
      "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==",
      "license": "ISC",
      "dependencies": {
        "brace-expansion": "^1.1.7"
      },
      "engines": {
        "node": "*"
      }
    },
    "node_modules/mongodb": {
      "version": "6.16.0",
      "resolved": "https://registry.npmjs.org/mongodb/-/mongodb-6.16.0.tgz",
      "integrity": "sha512-D1PNcdT0y4Grhou5Zi/qgipZOYeWrhLEpk33n3nm6LGtz61jvO88WlrWCK/bigMjpnOdAUKKQwsGIl0NtWMyYw==",
      "license": "Apache-2.0",
      "dependencies": {
        "@mongodb-js/saslprep": "^1.1.9",
        "bson": "^6.10.3",
        "mongodb-connection-string-url": "^3.0.0"
      },
      "engines": {
        "node": ">=16.20.1"
      },
      "peerDependencies": {
        "@aws-sdk/credential-providers": "^3.188.0",
        "@mongodb-js/zstd": "^1.1.0 || ^2.0.0",
        "gcp-metadata": "^5.2.0",
        "kerberos": "^2.0.1",
        "mongodb-client-encryption": ">=6.0.0 <7",
        "snappy": "^7.2.2",
        "socks": "^2.7.1"
      },
      "peerDependenciesMeta": {
        "@aws-sdk/credential-providers": {
          "optional": true
        },
        "@mongodb-js/zstd": {
          "optional": true
        },
        "gcp-metadata": {
          "optional": true
        },
        "kerberos": {
          "optional": true
        },
        "mongodb-client-encryption": {
          "optional": true
        },
        "snappy": {
          "optional": true
        },
        "socks": {
          "optional": true
        }
      }
    },
    "node_modules/mongodb-connection-string-url": {
      "version": "3.0.2",
      "resolved": "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz",
      "integrity": "sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA==",
      "license": "Apache-2.0",
      "dependencies": {
        "@types/whatwg-url": "^11.0.2",
        "whatwg-url": "^14.1.0 || ^13.0.0"
      }
    },
    "node_modules/mongoose": {
      "version": "8.15.0",
      "resolved": "https://registry.npmjs.org/mongoose/-/mongoose-8.15.0.tgz",
      "integrity": "sha512-WFKsY1q12ScGabnZWUB9c/QzZmz/ESorrV27OembB7Gz6rrh9m3GA4Srsv1uvW1s9AHO5DeZ6DdUTyF9zyNERQ==",
      "license": "MIT",
      "dependencies": {
        "bson": "^6.10.3",
        "kareem": "2.6.3",
        "mongodb": "~6.16.0",
        "mpath": "0.9.0",
        "mquery": "5.0.0",
        "ms": "2.1.3",
        "sift": "17.1.3"
      },
      "engines": {
        "node": ">=16.20.1"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/mongoose"
      }
    },
    "node_modules/mpath": {
      "version": "0.9.0",
      "resolved": "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz",
      "integrity": "sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==",
      "license": "MIT",
      "engines": {
        "node": ">=4.0.0"
      }
    },
    "node_modules/mquery": {
      "version": "5.0.0",
      "resolved": "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz",
      "integrity": "sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==",
      "license": "MIT",
      "dependencies": {
        "debug": "4.x"
      },
      "engines": {
        "node": ">=14.0.0"
      }
    },
    "node_modules/ms": {
      "version": "2.1.3",
      "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz",
      "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==",
      "license": "MIT"
    },
    "node_modules/negotiator": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz",
      "integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/nodemon": {
      "version": "3.1.10",
      "resolved": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.10.tgz",
      "integrity": "sha512-WDjw3pJ0/0jMFmyNDp3gvY2YizjLmmOUQo6DEBY+JgdvW/yQ9mEeSw6H5ythl5Ny2ytb7f9C2nIbjSxMNzbJXw==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "chokidar": "^3.5.2",
        "debug": "^4",
        "ignore-by-default": "^1.0.1",
        "minimatch": "^3.1.2",
        "pstree.remy": "^1.1.8",
        "semver": "^7.5.3",
        "simple-update-notifier": "^2.0.0",
        "supports-color": "^5.5.0",
        "touch": "^3.1.0",
        "undefsafe": "^2.0.5"
      },
      "bin": {
        "nodemon": "bin/nodemon.js"
      },
      "engines": {
        "node": ">=10"
      },
      "funding": {
        "type": "opencollective",
        "url": "https://opencollective.com/nodemon"
      }
    },
    "node_modules/normalize-path": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz",
      "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/object-assign": {
      "version": "4.1.1",
      "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz",
      "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==",
      "license": "MIT",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/object-inspect": {
      "version": "1.13.4",
      "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz",
      "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/on-finished": {
      "version": "2.4.1",
      "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz",
      "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==",
      "license": "MIT",
      "dependencies": {
        "ee-first": "1.1.1"
      },
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/once": {
      "version": "1.4.0",
      "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz",
      "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==",
      "license": "ISC",
      "dependencies": {
        "wrappy": "1"
      }
    },
    "node_modules/openapi-types": {
      "version": "12.1.3",
      "resolved": "https://registry.npmjs.org/openapi-types/-/openapi-types-12.1.3.tgz",
      "integrity": "sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==",
      "license": "MIT",
      "peer": true
    },
    "node_modules/parseurl": {
      "version": "1.3.3",
      "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz",
      "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/path-is-absolute": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz",
      "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==",
      "license": "MIT",
      "engines": {
        "node": ">=0.10.0"
      }
    },
    "node_modules/path-to-regexp": {
      "version": "8.2.0",
      "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz",
      "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==",
      "license": "MIT",
      "engines": {
        "node": ">=16"
      }
    },
    "node_modules/picomatch": {
      "version": "2.3.1",
      "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz",
      "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==",
      "dev": true,
      "license": "MIT",
      "engines": {
        "node": ">=8.6"
      },
      "funding": {
        "url": "https://github.com/sponsors/jonschlinkert"
      }
    },
    "node_modules/proxy-addr": {
      "version": "2.0.7",
      "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz",
      "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==",
      "license": "MIT",
      "dependencies": {
        "forwarded": "0.2.0",
        "ipaddr.js": "1.9.1"
      },
      "engines": {
        "node": ">= 0.10"
      }
    },
    "node_modules/pstree.remy": {
      "version": "1.1.8",
      "resolved": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz",
      "integrity": "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==",
      "dev": true,
      "license": "MIT"
    },
    "node_modules/punycode": {
      "version": "2.3.1",
      "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz",
      "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==",
      "license": "MIT",
      "engines": {
        "node": ">=6"
      }
    },
    "node_modules/qs": {
      "version": "6.14.0",
      "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz",
      "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==",
      "license": "BSD-3-Clause",
      "dependencies": {
        "side-channel": "^1.1.0"
      },
      "engines": {
        "node": ">=0.6"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/range-parser": {
      "version": "1.2.1",
      "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz",
      "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/raw-body": {
      "version": "3.0.0",
      "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz",
      "integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==",
      "license": "MIT",
      "dependencies": {
        "bytes": "3.1.2",
        "http-errors": "2.0.0",
        "iconv-lite": "0.6.3",
        "unpipe": "1.0.0"
      },
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/readdirp": {
      "version": "3.6.0",
      "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz",
      "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "picomatch": "^2.2.1"
      },
      "engines": {
        "node": ">=8.10.0"
      }
    },
    "node_modules/router": {
      "version": "2.2.0",
      "resolved": "https://registry.npmjs.org/router/-/router-2.2.0.tgz",
      "integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==",
      "license": "MIT",
      "dependencies": {
        "debug": "^4.4.0",
        "depd": "^2.0.0",
        "is-promise": "^4.0.0",
        "parseurl": "^1.3.3",
        "path-to-regexp": "^8.0.0"
      },
      "engines": {
        "node": ">= 18"
      }
    },
    "node_modules/safe-buffer": {
      "version": "5.2.1",
      "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz",
      "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==",
      "funding": [
        {
          "type": "github",
          "url": "https://github.com/sponsors/feross"
        },
        {
          "type": "patreon",
          "url": "https://www.patreon.com/feross"
        },
        {
          "type": "consulting",
          "url": "https://feross.org/support"
        }
      ],
      "license": "MIT"
    },
    "node_modules/safer-buffer": {
      "version": "2.1.2",
      "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz",
      "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==",
      "license": "MIT"
    },
    "node_modules/semver": {
      "version": "7.7.2",
      "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz",
      "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==",
      "dev": true,
      "license": "ISC",
      "bin": {
        "semver": "bin/semver.js"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/send": {
      "version": "1.2.0",
      "resolved": "https://registry.npmjs.org/send/-/send-1.2.0.tgz",
      "integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==",
      "license": "MIT",
      "dependencies": {
        "debug": "^4.3.5",
        "encodeurl": "^2.0.0",
        "escape-html": "^1.0.3",
        "etag": "^1.8.1",
        "fresh": "^2.0.0",
        "http-errors": "^2.0.0",
        "mime-types": "^3.0.1",
        "ms": "^2.1.3",
        "on-finished": "^2.4.1",
        "range-parser": "^1.2.1",
        "statuses": "^2.0.1"
      },
      "engines": {
        "node": ">= 18"
      }
    },
    "node_modules/serve-static": {
      "version": "2.2.0",
      "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz",
      "integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==",
      "license": "MIT",
      "dependencies": {
        "encodeurl": "^2.0.0",
        "escape-html": "^1.0.3",
        "parseurl": "^1.3.3",
        "send": "^1.2.0"
      },
      "engines": {
        "node": ">= 18"
      }
    },
    "node_modules/setprototypeof": {
      "version": "1.2.0",
      "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz",
      "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==",
      "license": "ISC"
    },
    "node_modules/side-channel": {
      "version": "1.1.0",
      "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz",
      "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==",
      "license": "MIT",
      "dependencies": {
        "es-errors": "^1.3.0",
        "object-inspect": "^1.13.3",
        "side-channel-list": "^1.0.0",
        "side-channel-map": "^1.0.1",
        "side-channel-weakmap": "^1.0.2"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/side-channel-list": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz",
      "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==",
      "license": "MIT",
      "dependencies": {
        "es-errors": "^1.3.0",
        "object-inspect": "^1.13.3"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/side-channel-map": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz",
      "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==",
      "license": "MIT",
      "dependencies": {
        "call-bound": "^1.0.2",
        "es-errors": "^1.3.0",
        "get-intrinsic": "^1.2.5",
        "object-inspect": "^1.13.3"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/side-channel-weakmap": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz",
      "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==",
      "license": "MIT",
      "dependencies": {
        "call-bound": "^1.0.2",
        "es-errors": "^1.3.0",
        "get-intrinsic": "^1.2.5",
        "object-inspect": "^1.13.3",
        "side-channel-map": "^1.0.1"
      },
      "engines": {
        "node": ">= 0.4"
      },
      "funding": {
        "url": "https://github.com/sponsors/ljharb"
      }
    },
    "node_modules/sift": {
      "version": "17.1.3",
      "resolved": "https://registry.npmjs.org/sift/-/sift-17.1.3.tgz",
      "integrity": "sha512-Rtlj66/b0ICeFzYTuNvX/EF1igRbbnGSvEyT79McoZa/DeGhMyC5pWKOEsZKnpkqtSeovd5FL/bjHWC3CIIvCQ==",
      "license": "MIT"
    },
    "node_modules/simple-update-notifier": {
      "version": "2.0.0",
      "resolved": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz",
      "integrity": "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "semver": "^7.5.3"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/sparse-bitfield": {
      "version": "3.0.3",
      "resolved": "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz",
      "integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==",
      "license": "MIT",
      "dependencies": {
        "memory-pager": "^1.0.2"
      }
    },
    "node_modules/statuses": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz",
      "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/supports-color": {
      "version": "5.5.0",
      "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz",
      "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "has-flag": "^3.0.0"
      },
      "engines": {
        "node": ">=4"
      }
    },
    "node_modules/swagger-jsdoc": {
      "version": "6.2.8",
      "resolved": "https://registry.npmjs.org/swagger-jsdoc/-/swagger-jsdoc-6.2.8.tgz",
      "integrity": "sha512-VPvil1+JRpmJ55CgAtn8DIcpBs0bL5L3q5bVQvF4tAW/k/9JYSj7dCpaYCAv5rufe0vcCbBRQXGvzpkWjvLklQ==",
      "license": "MIT",
      "dependencies": {
        "commander": "6.2.0",
        "doctrine": "3.0.0",
        "glob": "7.1.6",
        "lodash.mergewith": "^4.6.2",
        "swagger-parser": "^10.0.3",
        "yaml": "2.0.0-1"
      },
      "bin": {
        "swagger-jsdoc": "bin/swagger-jsdoc.js"
      },
      "engines": {
        "node": ">=12.0.0"
      }
    },
    "node_modules/swagger-parser": {
      "version": "10.0.3",
      "resolved": "https://registry.npmjs.org/swagger-parser/-/swagger-parser-10.0.3.tgz",
      "integrity": "sha512-nF7oMeL4KypldrQhac8RyHerJeGPD1p2xDh900GPvc+Nk7nWP6jX2FcC7WmkinMoAmoO774+AFXcWsW8gMWEIg==",
      "license": "MIT",
      "dependencies": {
        "@apidevtools/swagger-parser": "10.0.3"
      },
      "engines": {
        "node": ">=10"
      }
    },
    "node_modules/swagger-ui-dist": {
      "version": "5.22.0",
      "resolved": "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-5.22.0.tgz",
      "integrity": "sha512-8YlCSxiyb8uPFa7qoB1lRHYr1PBbT1NuV9RvQdFFPFPudRBTPf9coU5jl02KhzvrtmTEw4jXRgb0kg8pJvVuWQ==",
      "license": "Apache-2.0",
      "dependencies": {
        "@scarf/scarf": "=1.4.0"
      }
    },
    "node_modules/swagger-ui-express": {
      "version": "5.0.1",
      "resolved": "https://registry.npmjs.org/swagger-ui-express/-/swagger-ui-express-5.0.1.tgz",
      "integrity": "sha512-SrNU3RiBGTLLmFU8GIJdOdanJTl4TOmT27tt3bWWHppqYmAZ6IDuEuBvMU6nZq0zLEe6b/1rACXCgLZqO6ZfrA==",
      "license": "MIT",
      "dependencies": {
        "swagger-ui-dist": ">=5.0.0"
      },
      "engines": {
        "node": ">= v0.10.32"
      },
      "peerDependencies": {
        "express": ">=4.0.0 || >=5.0.0-beta"
      }
    },
    "node_modules/to-regex-range": {
      "version": "5.0.1",
      "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz",
      "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==",
      "dev": true,
      "license": "MIT",
      "dependencies": {
        "is-number": "^7.0.0"
      },
      "engines": {
        "node": ">=8.0"
      }
    },
    "node_modules/toidentifier": {
      "version": "1.0.1",
      "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz",
      "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==",
      "license": "MIT",
      "engines": {
        "node": ">=0.6"
      }
    },
    "node_modules/touch": {
      "version": "3.1.1",
      "resolved": "https://registry.npmjs.org/touch/-/touch-3.1.1.tgz",
      "integrity": "sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==",
      "dev": true,
      "license": "ISC",
      "bin": {
        "nodetouch": "bin/nodetouch.js"
      }
    },
    "node_modules/tr46": {
      "version": "5.1.1",
      "resolved": "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz",
      "integrity": "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw==",
      "license": "MIT",
      "dependencies": {
        "punycode": "^2.3.1"
      },
      "engines": {
        "node": ">=18"
      }
    },
    "node_modules/type-is": {
      "version": "2.0.1",
      "resolved": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz",
      "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==",
      "license": "MIT",
      "dependencies": {
        "content-type": "^1.0.5",
        "media-typer": "^1.1.0",
        "mime-types": "^3.0.0"
      },
      "engines": {
        "node": ">= 0.6"
      }
    },
    "node_modules/undefsafe": {
      "version": "2.0.5",
      "resolved": "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz",
      "integrity": "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA==",
      "dev": true,
      "license": "MIT"
    },
    "node_modules/unpipe": {
      "version": "1.0.0",
      "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz",
      "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/validator": {
      "version": "13.12.0",
      "resolved": "https://registry.npmjs.org/validator/-/validator-13.12.0.tgz",
      "integrity": "sha512-c1Q0mCiPlgdTVVVIJIrBuxNicYE+t/7oKeI9MWLj3fh/uq2Pxh/3eeWbVZ4OcGW1TUf53At0njHw5SMdA3tmMg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.10"
      }
    },
    "node_modules/vary": {
      "version": "1.1.2",
      "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz",
      "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==",
      "license": "MIT",
      "engines": {
        "node": ">= 0.8"
      }
    },
    "node_modules/webidl-conversions": {
      "version": "7.0.0",
      "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz",
      "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==",
      "license": "BSD-2-Clause",
      "engines": {
        "node": ">=12"
      }
    },
    "node_modules/whatwg-url": {
      "version": "14.2.0",
      "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz",
      "integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==",
      "license": "MIT",
      "dependencies": {
        "tr46": "^5.1.0",
        "webidl-conversions": "^7.0.0"
      },
      "engines": {
        "node": ">=18"
      }
    },
    "node_modules/wrappy": {
      "version": "1.0.2",
      "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz",
      "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==",
      "license": "ISC"
    },
    "node_modules/yaml": {
      "version": "2.0.0-1",
      "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.0.0-1.tgz",
      "integrity": "sha512-W7h5dEhywMKenDJh2iX/LABkbFnBxasD27oyXWDS/feDsxiw0dD5ncXdYXgkvAsXIY2MpW/ZKkr9IU30DBdMNQ==",
      "license": "ISC",
      "engines": {
        "node": ">= 6"
      }
    },
    "node_modules/z-schema": {
      "version": "5.0.5",
      "resolved": "https://registry.npmjs.org/z-schema/-/z-schema-5.0.5.tgz",
      "integrity": "sha512-D7eujBWkLa3p2sIpJA0d1pr7es+a7m0vFAnZLlCEKq/Ij2k0MLi9Br2UPxoxdYystm5K1yeBGzub0FlYUEWj2Q==",
      "license": "MIT",
      "dependencies": {
        "lodash.get": "^4.4.2",
        "lodash.isequal": "^4.5.0",
        "validator": "^13.7.0"
      },
      "bin": {
        "z-schema": "bin/z-schema"
      },
      "engines": {
        "node": ">=8.0.0"
      },
      "optionalDependencies": {
        "commander": "^9.4.1"
      }
    },
    "node_modules/z-schema/node_modules/commander": {
      "version": "9.5.0",
      "resolved": "https://registry.npmjs.org/commander/-/commander-9.5.0.tgz",
      "integrity": "sha512-KRs7WVDKg86PWiuAqhDrAQnTXZKraVcCc6vFdL14qrZ/DcWwuRo7VoiYXalXO7S5GKpqYiVEwCbgFDfxNHKJBQ==",
      "license": "MIT",
      "optional": true,
      "engines": {
        "node": "^12.20.0 || >=14"
      }
    }
  }
}
                                                                                                                                                                                                                                                                                                                                                    ./package.json                                                                                      000644  000765  000024  00000001202 15013676136 013737  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         {
  "name": "mirage",
  "version": "1.0.0",
  "main": "app.js",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
    "test": "echo \"Error: no test specified\" && exit 1"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "description": "Express backend app with mock data endpoints using Faker",
  "dependencies": {
    "@faker-js/faker": "^9.8.0",
    "cors": "^2.8.5",
    "express": "^5.1.0",
    "express-validator": "^7.2.1",
    "js-yaml": "^4.1.0",
    "mongoose": "^8.15.0",
    "swagger-jsdoc": "^6.2.8",
    "swagger-ui-express": "^5.0.1"
  },
  "devDependencies": {
    "nodemon": "^3.1.10"
  }
}
                                                                                                                                                                                                                                                                                                                                                                                              ./controllers/                                                                                      000755  000765  000024  00000000000 15013666124 014020  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./routes/                                                                                           000755  000765  000024  00000000000 15013666150 012772  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./app.js                                                                                            000644  000765  000024  00000005333 15013675762 012604  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Main application entry point
 *
 * This file sets up the Express server with necessary middleware
 * and routes for the dynamic mock API endpoints.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const { body } = require('express-validator');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger/swagger');
const mockRoutes = require('./routes/mock');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Configure CORS to allow requests from all domains (both HTTP and HTTPS)
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Routes
app.use('/api/mock', mockRoutes);

// Swagger documentation route
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Mirage API Documentation',
}));

// HTML documentation route
app.get('/docs', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'documentation.html'));
});

// Root route with documentation links
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to the Dynamic Mock API Server',
    description: 'Generate custom mock data based on your defined structure',
    documentation: {
      html: {
        url: '/docs',
        description: 'Visit the HTML documentation page for detailed information and examples'
      },
      swagger: {
        url: '/api-docs',
        description: 'Visit the Swagger UI for interactive API documentation'
      }
    },
    usage: {
      endpoint: '/api/mock/:app/:endpoint',
      method: 'POST',
      body: {
        structure: 'Object or Array defining the data structure with faker templates'
      },
      query: {
        records: 'Number of records to generate (default: 1)'
      }
    },
    example: {
      request: {
        url: '/api/mock/users/list?records=5',
        body: {
          structure: {
            id: '{{guid}}',
            name: '{{person.fullName}}',
            email: '{{internet.email}}',
            age: '{{integer(18, 65)}}',
            address: {
              street: '{{location.street}}',
              city: '{{location.city}}',
              country: '{{location.country}}'
            }
          }
        }
      }
    }
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Dynamic Mock API Server is running on port ${PORT}`);
});

module.exports = app;
                                                                                                                                                                                                                                                                                                     ./routes/mock.js                                                                                    000644  000765  000024  00000001345 15013667353 014272  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Dynamic Mock API Routes
 *
 * This file defines the dynamic route for the mock API endpoints.
 * The route accepts any app and endpoint combination and passes
 * the request to the mock controller.
 */

const express = require('express');
const router = express.Router();
const mockController = require('../controllers/mock/controller');
const { body } = require('express-validator');

// Dynamic route that accepts any app and endpoint combination
// Example: /api/mock/users/list, /api/mock/products/details, etc.
router.route('/:app/:endpoint').post(
  // Validate that the structure is provided in the request body
  [body('structure').exists().withMessage('Structure is required')],
  mockController
);

module.exports = router;
                                                                                                                                                                                                                                                                                           ./controllers/mock/                                                                                 000755  000765  000024  00000000000 15013666211 014746  5                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         ./controllers/mock/controller.js                                                                    000644  000765  000024  00000003317 15013667773 017511  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Dynamic Mock API Controller
 *
 * This file contains the controller function for the dynamic mock API endpoint.
 * It validates the request, extracts the structure from the request body,
 * and uses the helper function to generate mock data based on the structure.
 */

const { validationResult } = require('express-validator');
const errorsMsg = require('../../common/errors');
const { generateMockData } = require('./helpers');

/**
 * Dynamic mock controller that generates data based on the provided structure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const mockController = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorsMsg.validationError(res, errors.array());
    }

    // Extract parameters
    const { app, endpoint } = req.params;
    const { structure } = req.body;
    const records = parseInt(req.query.records) || 1;

    // Validate structure
    if (!structure) {
      return errorsMsg.badRequest(res, 'Invalid structure format');
    }

    // Generate mock data
    const mockData = generateMockData(structure, Number(records));

    // Log request info (for debugging)
    console.log(`Generated mock data for ${app}/${endpoint} with ${records} records`);

    // Return response
    return res.status(200).json({
      success: true,
      app,
      endpoint,
      records: Array.isArray(mockData) ? mockData.length : 1,
      data: mockData
    });
  } catch (err) {
    console.error('Error generating mock data:', err);
    return errorsMsg.serverError(res, 'Server error occurred while generating mock data');
  }
};

module.exports = mockController;
                                                                                                                                                                                                                                                                                                                 ./controllers/mock/helpers.js                                                                       000644  000765  000024  00000040212 15013674112 016744  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Dynamic Mock API Helper Functions
 *
 * This file contains helper functions to generate dynamic mock data
 * using the Faker library based on a provided structure.
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

/**
 * Clean string by removing quotes
 * @param {string} str - String to clean
 * @returns {string} Cleaned string
 */
const cleanString = (str) =>
  Boolean(str) && typeof str === "string" ? str?.replace(/['"]/g, "") : str;

/**
 * Helper functions for generating different types of mock data
 */
/**
 * Determine if a string represents an object or a function
 * @param {string} str - String to check
 * @returns {string} 'object' or 'function'
 */
const operationType = (str) => {
  try {
    str = str?.trim();

    if (str?.startsWith("{") && str?.endsWith("}")) {
      return "object";
    } else {
      return "function";
    }
  } catch (error) {
    console.error("Error determining operation type:", error);
    return "";
  }
};

/**
 * Handle repeat operations
 * @param {number} count - Number of times to repeat
 * @param {string|object} operation - Operation to repeat
 * @returns {Array} Array of generated values
 */
const handleRepeat = (count, operation) => {
  try {
    if (operationType(operation) === "object") {
      return generateMockData(operation, count);
    } else {
      return Array.from({ length: count }).map((_, index) => {
        const [helperName, ...args] = operation?.split(/[(),]/).filter(Boolean);
        const trimmedArgs = args.map((arg) => arg.trim().replace(/^'|'$/g, ""));
        return handleWrite({ helperName, trimmedArgs, index });
      });
    }
  } catch (error) {
    console.error("Error handling repeat:", error);
    return [];
  }
};

/**
 * Resolve a helper function from a path
 * @param {string} helperName - Name of the helper function
 * @returns {Function} Helper function
 */
const resolveHelperPath = (helperName) => {
  const parts = helperName.split(".");
  let obj = helpers;

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];

    if (typeof obj[part] === "function") {
      return obj[part];
    } else {
      obj = obj[part];
    }
  }

  return obj;
};

/**
 * Check if a string can be parsed as JSON
 * @param {string} str - String to check
 * @returns {boolean} True if parsable, false otherwise
 */
const isParsable = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Handle method chaining (e.g., toUpperCase(), toLowerCase())
 * @param {string} result - The result to apply methods to
 * @param {string} chainedMethods - The chained methods to apply
 * @returns {string} The result after applying the methods
 */
const applyMethodChain = (result, chainedMethods) => {
  if (!chainedMethods) return result;

  // Extract method name without parentheses
  const methodName = chainedMethods.replace(/\(\)$/, '');
  let currentResult = result;

  // Apply the method
  if (methodName === 'toUpperCase' && typeof currentResult === 'string') {
    currentResult = currentResult.toUpperCase();
  } else if (methodName === 'toLowerCase' && typeof currentResult === 'string') {
    currentResult = currentResult.toLowerCase();
  } else if (methodName === 'trim' && typeof currentResult === 'string') {
    currentResult = currentResult.trim();
  }
  // Add more method handlers as needed

  return currentResult;
};

/**
 * Helper functions for generating different types of mock data
 */
const helpers = {
  // ID generators
  objectId: ({ args }) => {
    const id = args?.[0];
    return id ? String(id) : new mongoose.Types.ObjectId();
  },
  index: ({ index }) => index ?? "",
  guid: () => faker.string.uuid(),

  // Boolean generator
  boolean: () => faker.datatype.boolean(),

  // Person data generators
  person: ({ args, operation }) => {
    if (typeof faker.person[cleanString(operation)] === "function") {
      return faker.person[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for person: ${operation}`);
    }
  },

  // Company data generators
  company: ({ args, operation }) => {
    if (typeof faker.company[cleanString(operation)] === "function") {
      return faker.company[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for company: ${operation}`);
    }
  },

  // Internet data generators
  internet: ({ args, operation }) => {
    if (typeof faker.internet[cleanString(operation)] === "function") {
      return faker.internet[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for internet: ${operation}`);
    }
  },

  // Phone data generators
  phone: ({ args, operation }) => {
    if (typeof faker.phone[cleanString(operation)] === "function") {
      return faker.phone[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for phone: ${operation}`);
    }
  },

  // Location data generators
  location: ({ args, operation }) => {
    if (typeof faker.location[cleanString(operation)] === "function") {
      return faker.location[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for location: ${operation}`);
    }
  },

  // Lorem ipsum generators
  lorem: ({ args, operation }) => {
    if (typeof faker.lorem[cleanString(operation)] === "function") {
      return faker.lorem[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for lorem: ${operation}`);
    }
  },

  // Date generators
  date: ({ args, operation }) => {
    if (typeof faker.date[cleanString(operation)] === "function") {
      return faker.date[cleanString(operation)](...args)?.toISOString();
    } else {
      throw new Error(`Invalid type for date: ${operation}`);
    }
  },

  // Number generators
  amount: ({ args }) => {
    const [min, max, decimals] = args;
    return faker.finance.amount({
      min: cleanString(min),
      max: cleanString(max),
      decimals: cleanString(decimals),
    });
  },
  floating: ({ args }) => {
    const [min, max] = args;
    return faker.number.float({ min: cleanString(min), max: cleanString(max) });
  },
  integer: ({ args }) => {
    const [min, max] = args;
    return faker.number.int({ min: cleanString(min), max: cleanString(max) });
  },

  // Array and object generators
  random: ({ args }) => faker.helpers.arrayElement(args),
  repeat: ({ args }) => {
    return handleRepeat(...args);
  },

  // Commerce generators
  commerce: ({ args, operation }) => {
    if (typeof faker.commerce[cleanString(operation)] === "function") {
      return faker.commerce[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for commerce: ${operation}`);
    }
  },

  // Image generators
  image: ({ args, operation }) => {
    if (typeof faker.image[cleanString(operation)] === "function") {
      return faker.image[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for image: ${operation}`);
    }
  },

  // Finance generators
  finance: ({ args, operation }) => {
    if (typeof faker.finance[cleanString(operation)] === "function") {
      return faker.finance[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for finance: ${operation}`);
    }
  },

  // System generators
  system: ({ args, operation }) => {
    if (typeof faker.system[cleanString(operation)] === "function") {
      return faker.system[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for system: ${operation}`);
    }
  },

  // Vehicle generators
  vehicle: ({ args, operation }) => {
    if (typeof faker.vehicle[cleanString(operation)] === "function") {
      return faker.vehicle[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for vehicle: ${operation}`);
    }
  },

  // Color generators
  color: ({ args, operation }) => {
    if (typeof faker.color[cleanString(operation)] === "function") {
      return faker.color[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for color: ${operation}`);
    }
  },

  // Word generators
  word: ({ args, operation }) => {
    if (typeof faker.word[cleanString(operation)] === "function") {
      return faker.word[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for word: ${operation}`);
    }
  },

  // Music generators
  music: ({ args, operation }) => {
    if (typeof faker.music[cleanString(operation)] === "function") {
      return faker.music[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for music: ${operation}`);
    }
  },

  // Custom helpers
  timestamp: () => Date.now(),
  oneOf: ({ args }) => faker.helpers.arrayElement(args),
  arrayOf: ({ args }) => {
    const [count, value] = args;
    return Array.from({ length: parseInt(count) }).map(() => value);
  }
};

/**
 * Handle writing a value based on a helper function
 * @param {Object} params - Parameters for the helper function
 * @param {string} params.helperName - Name of the helper function
 * @param {Array} params.trimmedArgs - Arguments for the helper function
 * @param {string} params.operation - Operation to perform
 * @param {number} params.index - Index of the current item
 * @returns {*} Generated value
 */
const handleWrite = ({ helperName, trimmedArgs, operation, index }) => {
  try {
    if (helperName) {
      const action = helperName?.includes(".")
        ? resolveHelperPath(helperName)
        : helpers[helperName];

      const args =
        trimmedArgs.length > 0
          ? trimmedArgs.map((arg) => (isNaN(arg) ? arg : parseFloat(arg)))
          : [];

      if (typeof action === "function") {
        const output = action({
          args,
          index,
          operation:
            helperName === "repeat" ? operation : helperName?.split(".")[1],
        });

        if (isParsable(output)) {
          return JSON.parse(output);
        } else {
          return output;
        }
      } else {
        return helperName;
      }
    } else {
      return helperName;
    }
  } catch (error) {
    console.error("Error handling write:", error);
    return helperName;
  }
};

/**
 * Generate mock data based on a structure
 * @param {Object|Array} structure - Structure to generate data from
 * @param {number} numRecords - Number of records to generate
 * @returns {Object|Array} Generated mock data
 */
const generateMockData = (structure, numRecords) => {
  try {
    /**
     * Generate data for an array element
     * @param {*} operation - Operation to perform
     * @param {number} index - Index of the current item
     * @returns {*} Generated value
     */
    const generateArrayData = (operation, index) => {
      try {
        if (typeof operation === "object") {
          return generateSingleObject(operation, index);
        } else if (typeof operation === "string") {
          const action = operation?.startsWith("{{")
            ? operation?.slice(2, -2)?.trim()
            : "";

          // Check for method chaining (e.g., company.name().toUpperCase())
          const methodChainMatch = action.match(/^(.*?\))\.(.+)$/);

          if (methodChainMatch) {
            // Extract the base function call and the chained methods
            const baseCall = methodChainMatch[1];
            const chainedMethods = methodChainMatch[2];

            // Process the base function call
            const [helperName, ...args] = baseCall
              .split(/[(),]/)
              .filter(Boolean);
            const trimmedArgs = args.map((arg) =>
              arg.trim().replace(/^'|'$/g, "")
            );

            // Get the result from the base function
            let result = handleWrite({ helperName, trimmedArgs, index });

            // Apply chained methods
            result = applyMethodChain(result, chainedMethods);

            return result;
          } else {
            // Regular function call without chaining
            const [helperName, ...args] = action.split(/[(),]/).filter(Boolean);
            const trimmedArgs = args.map((arg) =>
              arg.trim().replace(/^'|'$/g, "")
            );

            return handleWrite({ helperName, trimmedArgs, index });
          }
        } else {
          return operation;
        }
      } catch (error) {
        console.error("Error generating array data:", error);
        return operation;
      }
    };

    /**
     * Generate a single object based on a structure
     * @param {Object} structure - Structure to generate data from
     * @param {number} index - Index of the current item
     * @returns {Object} Generated object
     */
    const generateSingleObject = (structure, index) => {
      return Object.entries(structure)?.reduce((acc, [key, value]) => {
        let newValue;

        if (typeof value === "string") {
          newValue = value.replace(/{{(.*?)}}/g, (obj, helperCall) => {
            // Check for method chaining (e.g., company.name().toUpperCase())
            const methodChainMatch = helperCall.match(/^(.*?\))\.(.+)$/);

            if (methodChainMatch) {
              // Extract the base function call and the chained methods
              const baseCall = methodChainMatch[1];
              const chainedMethods = methodChainMatch[2];

              // Process the base function call
              const [helperName, ...args] = baseCall
                .split(/[(),]/)
                .filter(Boolean);
              const trimmedArgs = args.map((arg) =>
                arg.trim().replace(/^'|'$/g, "")
              );

              // Get the result from the base function
              let result = handleWrite({ helperName, trimmedArgs, index });

              // Apply chained methods
              result = applyMethodChain(result, chainedMethods);

              return result;
            } else {
              // Regular function call without chaining
              const [helperName, ...args] = helperCall
                .split(/[(),]/)
                .filter(Boolean);
              const trimmedArgs = args.map((arg) =>
                arg.trim().replace(/^'|'$/g, "")
              );

              return handleWrite({ helperName, trimmedArgs, index });
            }
          });
        } else if (Array.isArray(value)) {
          let action = value?.[0] ?? "";
          let repeats = 0;
          if (typeof action === "string" && action.includes("repeat")) {
            repeats = parseInt(action.match(/\d+/)?.[0] || "0");
          }
          const operationLength = value?.length;
          let operation = value?.[1] ?? "";

          if (repeats && operationLength <= 2) {
            newValue = Array.from({ length: repeats }).map((_, index) =>
              generateArrayData(operation, index)
            );
          } else {
            const repeatIndex = value?.findIndex((item) =>
              String(item)?.includes("repeat")
            );

            const dataToUse =
              repeatIndex > 0 ? value?.slice(0, repeatIndex) : value;

            newValue = dataToUse.map((op, index) =>
              generateArrayData(op, index)
            );

            if (repeatIndex > -1) {
              action = value?.[repeatIndex] ?? "";
              if (typeof action === "string") {
                repeats = parseInt(action.match(/\d+/)?.[0] || "0");
              }

              operation = value?.[repeatIndex + 1] ?? "";

              const repetition = Array.from({ length: repeats }).map(
                (_, index) => generateArrayData(operation, index)
              );
              newValue = newValue?.concat(repetition);
            }
          }
        } else if (typeof value === "object") {
          newValue = generateSingleObject(value, index);
        } else {
          newValue = value;
        }

        if (isParsable(newValue)) {
          acc[key] = JSON.parse(newValue);
        } else {
          acc[key] = newValue;
        }

        return acc;
      }, {});
    };

    // Handle array or object structure
    if (Array.isArray(structure)) {
      return Array.from({ length: numRecords }).map((_, index) =>
        generateSingleObject(structure[0], index)
      );
    } else {
      return generateSingleObject(structure, 0);
    }
  } catch (error) {
    console.error("Error generating mock data:", error);
    return "No data";
  }
};

module.exports = {
  generateMockData,
};
                                                                                                                                                                                                                                                                                                                                                                                      ./public/documentation.html                                                                         000644  000765  000024  00000103635 15014062432 016470  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mirage - Dynamic Mock API Server Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        code {
            background-color: #f8f8f8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
        }
        pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .content {
            flex: 1;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .example {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .note {
            background-color: #fff8dc;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #f1c40f;
        }
        .nav {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .nav ul {
            list-style-type: none;
            padding: 0;
        }
        .nav ul li {
            display: inline-block;
            margin-right: 15px;
        }
        .nav a {
            text-decoration: none;
            color: #3498db;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <h1>Mirage - Dynamic Mock API Server Documentation</h1>

            <div class="nav">
                <ul>
                    <li><a href="#overview">Overview</a></li>
                    <li><a href="#getting-started">Getting Started</a></li>
                    <li><a href="#api-endpoint">API Endpoint</a></li>
                    <li><a href="#template-syntax">Template Syntax</a></li>
                    <li><a href="#available-helpers">Available Helpers</a></li>
                    <li><a href="#examples">Examples</a></li>
                    <li><a href="#advanced-usage">Advanced Usage</a></li>
                </ul>
            </div>

            <section id="overview">
                <h2>Overview</h2>
                <p>
                    Mirage is a dynamic mock API server that allows you to generate realistic mock data based on your own defined structure.
                    It uses Faker.js to create realistic data for testing, development, and demonstration purposes.
                </p>
                <p>
                    Unlike traditional mock servers with predefined endpoints and data structures, Mirage allows you to define your own
                    data structure on-the-fly and generates mock data based on that structure. This makes it extremely flexible and
                    suitable for a wide range of use cases.
                </p>
            </section>

            <section id="getting-started">
                <h2>Getting Started</h2>
                <h3>Installation</h3>
                <pre><code>
# Clone the repository
git clone &lt;repository-url&gt;
cd mirage

# Install dependencies
npm install

# Start the server
npm start
                </code></pre>
                <p>
                    The server will start on port 3000 by default. You can change this by setting the <code>PORT</code> environment variable.
                </p>
            </section>

            <section id="api-endpoint">
                <h2>API Endpoint</h2>
                <p>
                    Mirage provides a single dynamic endpoint that can be used to generate mock data:
                </p>
                <pre><code>POST /api/mock/:app/:endpoint</code></pre>
                <p>
                    Where:
                </p>
                <ul>
                    <li><code>:app</code> - Any application name (e.g., users, products, orders)</li>
                    <li><code>:endpoint</code> - Any endpoint name (e.g., list, details, search)</li>
                </ul>

                <h3>Request Body</h3>
                <p>
                    The request body should contain a <code>structure</code> object that defines the structure of the mock data to be generated:
                </p>
                <pre><code>{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}",
    "createdAt": "{{date.past}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    }
  }
}</code></pre>

                <h3>Query Parameters</h3>
                <p>
                    <code>records</code> - Number of records to generate (default: 1)
                </p>
                <p>
                    Example: <code>/api/mock/users/list?records=5</code>
                </p>

                <h3>Response</h3>
                <p>
                    The response will contain the generated mock data based on the provided structure:
                </p>
                <pre><code>{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 32,
    "isActive": true,
    "createdAt": "2023-01-15T08:30:00.000Z",
    "address": {
      "street": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zipCode": "10001",
      "country": "USA"
    }
  }
}</code></pre>
            </section>

            <section id="template-syntax">
                <h2>Template Syntax</h2>
                <p>
                    The structure object supports the following template syntax:
                </p>

                <h3>Basic Syntax</h3>
                <p>
                    Use double curly braces to indicate a template:
                </p>
                <pre><code>{{helperName}}</code></pre>

                <h3>With Parameters</h3>
                <pre><code>{{helperName(param1, param2, ...)}}</code></pre>

                <h3>Nested Properties</h3>
                <pre><code>{{category.subcategory}}</code></pre>

                <div class="note">
                    <strong>Note:</strong> All templates must be enclosed in quotes when used in JSON, e.g., <code>"{{guid}}"</code>
                </div>
            </section>

            <section id="available-helpers">
                <h2>Available Helpers</h2>

                <h3>ID Generators</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>objectId</code></td>
                        <td>Generate a MongoDB ObjectId</td>
                        <td><code>"{{objectId}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>guid</code></td>
                        <td>Generate a UUID</td>
                        <td><code>"{{guid}}"</code></td>
                    </tr>
                </table>

                <h3>Boolean Generator</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>boolean</code></td>
                        <td>Generate a random boolean value</td>
                        <td><code>"{{boolean}}"</code></td>
                    </tr>
                </table>

                <h3>Person Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>person.firstName</code></td>
                        <td>Generate a random first name</td>
                        <td><code>"{{person.firstName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.lastName</code></td>
                        <td>Generate a random last name</td>
                        <td><code>"{{person.lastName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.fullName</code></td>
                        <td>Generate a random full name</td>
                        <td><code>"{{person.fullName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.gender</code></td>
                        <td>Generate a random gender</td>
                        <td><code>"{{person.gender}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>person.jobTitle</code></td>
                        <td>Generate a random job title</td>
                        <td><code>"{{person.jobTitle}}"</code></td>
                    </tr>
                </table>

                <h3>Internet Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>internet.email</code></td>
                        <td>Generate a random email address</td>
                        <td><code>"{{internet.email}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.userName</code></td>
                        <td>Generate a random username</td>
                        <td><code>"{{internet.userName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.url</code></td>
                        <td>Generate a random URL</td>
                        <td><code>"{{internet.url}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.ip</code></td>
                        <td>Generate a random IP address</td>
                        <td><code>"{{internet.ip}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>internet.password</code></td>
                        <td>Generate a random password</td>
                        <td><code>"{{internet.password}}"</code></td>
                    </tr>
                </table>

                <h3>Location Data</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>location.street</code></td>
                        <td>Generate a random street address</td>
                        <td><code>"{{location.street}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.city</code></td>
                        <td>Generate a random city</td>
                        <td><code>"{{location.city}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.state</code></td>
                        <td>Generate a random state</td>
                        <td><code>"{{location.state}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.country</code></td>
                        <td>Generate a random country</td>
                        <td><code>"{{location.country}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>location.zipCode</code></td>
                        <td>Generate a random zip code</td>
                        <td><code>"{{location.zipCode}}"</code></td>
                    </tr>
                </table>

                <h3>Date</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>date.past</code></td>
                        <td>Generate a date in the past</td>
                        <td><code>"{{date.past}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>date.future</code></td>
                        <td>Generate a date in the future</td>
                        <td><code>"{{date.future}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>date.recent</code></td>
                        <td>Generate a recent date</td>
                        <td><code>"{{date.recent}}"</code></td>
                    </tr>
                </table>

                <h3>Numbers</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>integer(min, max)</code></td>
                        <td>Generate a random integer between min and max</td>
                        <td><code>"{{integer(1, 100)}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>floating(min, max)</code></td>
                        <td>Generate a random float between min and max</td>
                        <td><code>"{{floating(1, 100)}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>amount(min, max, decimals)</code></td>
                        <td>Generate a random amount with specified decimals</td>
                        <td><code>"{{amount(10, 1000, 2)}}"</code></td>
                    </tr>
                </table>

                <h3>Commerce</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>commerce.productName</code></td>
                        <td>Generate a random product name</td>
                        <td><code>"{{commerce.productName}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.productDescription</code></td>
                        <td>Generate a random product description</td>
                        <td><code>"{{commerce.productDescription}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.price</code></td>
                        <td>Generate a random price</td>
                        <td><code>"{{commerce.price}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>commerce.department</code></td>
                        <td>Generate a random department name</td>
                        <td><code>"{{commerce.department}}"</code></td>
                    </tr>
                </table>

                <h3>Arrays and Objects</h3>
                <table>
                    <tr>
                        <th>Helper</th>
                        <th>Description</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td><code>random(value1, value2, ...)</code></td>
                        <td>Pick a random value from the provided options</td>
                        <td><code>"{{random('red', 'green', 'blue')}}"</code></td>
                    </tr>
                    <tr>
                        <td><code>repeat(count, operation)</code></td>
                        <td>Repeat an operation count times</td>
                        <td>See Advanced Usage section</td>
                    </tr>
                </table>
            </section>

            <section id="examples">
                <h2>Examples</h2>

                <h3>Basic User Object</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/users/list
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "age": "{{integer(18, 65)}}",
    "isActive": "{{boolean}}"
  }
}</code></pre>
                </div>

                <h3>Product with Nested Objects</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/products/details
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{commerce.productName}}",
    "description": "{{commerce.productDescription}}",
    "price": "{{floating(10, 1000)}}",
    "category": "{{commerce.department}}",
    "manufacturer": {
      "name": "{{company.name}}",
      "location": "{{location.country}}",
      "established": "{{date.past}}"
    },
    "specifications": {
      "weight": "{{floating(0.1, 10)}}",
      "dimensions": {
        "length": "{{floating(1, 100)}}",
        "width": "{{floating(1, 100)}}",
        "height": "{{floating(1, 100)}}"
      }
    }
  }
}</code></pre>
                </div>

                <h3>Array of Items</h3>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/orders/list?records=3
{
  "structure": [{
    "id": "{{guid}}",
    "customer": {
      "name": "{{person.fullName}}",
      "email": "{{internet.email}}"
    },
    "orderDate": "{{date.past}}",
    "status": "{{random('pending', 'processing', 'shipped', 'delivered')}}",
    "total": "{{amount(10, 500, 2)}}"
  }]
}</code></pre>
                </div>
            </section>

            <section id="advanced-usage">
                <h2>Advanced Usage</h2>

                <h3>Generating Arrays with Repeat</h3>
                <p>
                    You can use the <code>repeat</code> helper to generate arrays of items:
                </p>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/products/details
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{commerce.productName}}",
    "price": "{{floating(10, 1000)}}",
    "tags": ["repeat(3)", "{{lorem.word}}"],
    "reviews": ["repeat(2)", {
      "user": "{{person.fullName}}",
      "rating": "{{integer(1, 5)}}",
      "comment": "{{lorem.sentence}}"
    }]
  }
}</code></pre>
                </div>

                <h3>Combining Multiple Helpers</h3>
                <p>
                    You can combine multiple helpers to create complex data structures:
                </p>
                <div class="example">
                    <h4>Request:</h4>
                    <pre><code>POST /api/mock/users/profile
{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}",
    "address": {
      "street": "{{location.street}}",
      "city": "{{location.city}}",
      "state": "{{location.state}}",
      "zipCode": "{{location.zipCode}}",
      "country": "{{location.country}}"
    },
    "phoneNumbers": ["repeat(2)", {
      "type": "{{random('home', 'work', 'mobile')}}",
      "number": "{{phone.number}}"
    }],
    "preferences": {
      "theme": "{{random('light', 'dark', 'system')}}",
      "notifications": "{{boolean}}",
      "language": "{{random('en', 'es', 'fr', 'de')}}"
    },
    "subscription": {
      "plan": "{{random('free', 'basic', 'premium')}}",
      "startDate": "{{date.past}}",
      "endDate": "{{date.future}}",
      "autoRenew": "{{boolean}}"
    }
  }
}</code></pre>
                </div>

                <h3>Complex Data Structures for Contact Management</h3>
                <p>
                    Here are examples of complex data structures for contact management systems:
                </p>

                <h4>Contact Table Row</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/table
{
  "structure": [{
    "_id": "{{objectId()}}",
    "guid": "{{guid()}}",
    "type": "{{random('person', 'company')}}",
    "label": {
      "_id": "{{objectId()}}",
      "name": "{{random('client', 'reference', 'open')}}"
    },
    "avatar": "https://i.pravatar.cc/500",
    "name": "{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}",
    "email": "{{internet.email()}}",
    "phone": "+{{integer(1, 99)}} {{phone.number()}}",
    "dob": "{{date.birthdate()}}",
    "company": {
      "_id": "{{objectId()}}",
      "companyName": "{{company.name().toUpperCase()}}"
    },
    "jobTitle": "{{person.jobTitle()}}",
    "department": "{{person.jobArea()}}",
    "reportingTo": "{{person.fullName()}}",
    "contactOwner": "{{person.fullName()}}",
    "createdBy": "{{person.fullName()}}",
    "assistant": "{{person.fullName()}}",
    "assistantPhone": "+{{integer(1, 99)}} {{phone.number()}}",
    "gender": "{{person.gender()}}",
    "description": "{{person.bio()}}",
    "leadSource": "{{lorem.word(5)}}",
    "address": "{{integer(100, 999)}} {{location.street()}}, {{location.city()}}, {{location.state()}}, {{integer(100, 10000)}}",
    "latitude": "{{floating(-90.000001, 90)}}",
    "longitude": "{{floating(-180.000001, 180)}}",
    "local": {
      "latitude": "{{floating(-90.000001, 90)}}",
      "longitude": "{{floating(-180.000001, 180)}}"
    },
    "tags": ["{{repeat(7)}}", "{{lorem.words(1)}}"],
    "friends": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "name": "{{person.firstName()}} {{person.lastName()}}"
      }
    ],
    "greeting": "Hello, {{person.firstName()}} {{person.lastName()}}! You have {{integer(1, 10)}} unread messages.",
    "favoriteFruit": "{{random('apple', 'banana', 'strawberry')}}",
    "createdAt": "{{date.past()}}",
    "updatedAt": "{{date.recent()}}"
  }]
}</code></pre>
                </div>

                <h4>Contact Form Data</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/form
{
  "structure": {
    "_id": "{{objectId(66ca2151b2038dfb2582f258)}}",
    "guid": "{{guid()}}",
    "type": "{{random('person', 'company')}}",
    "status": "{{random('active', 'inactive')}}",
    "category": {
      "_id": "{{objectId()}}",
      "name": "{{random('client', 'reference', 'open')}}"
    },
    "domain": "{{internet.domainName()}}",
    "avatar": "https://i.pravatar.cc/500",
    "salutation": "{{person.prefix()}}",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "company": {
      "_id": "{{objectId()}}",
      "companyName": "{{company.name().toUpperCase()}}"
    },
    "companyName": "{{company.name()}}",
    "owner": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}",
      "avatar": "https://i.pravatar.cc/200"
    },
    "email": "{{internet.email()}}",
    "emails": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "email": "{{internet.email()}}"
      }
    ],
    "countryPhoneCode": "+{{integer(1, 99)}}",
    "phone": "{{phone.number('##########')}}",
    "phones": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "countryPhoneCode": "+{{integer(1, 99)}}",
        "phone": "{{phone.number('##########')}}"
      }
    ],
    "group": {
      "_id": "{{objectId()}}",
      "name": "{{lorem.words(1)}}"
    },
    "address": [
      {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      },
      {
        "_id": "{{objectId()}}",
        "type": "billing",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      },
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "type": "addon",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      }
    ],
    "billingOnPrimaryAddress": "{{boolean()}}",
    "jobTitle": "{{person.jobTitle()}}",
    "department": "{{person.jobArea()}}",
    "reportingTo": "{{person.fullName()}}",
    "createdBy": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}"
    },
    "assistant": "{{person.fullName()}}",
    "assistantPhone": "+{{integer(1, 99)}} {{phone.number()}}",
    "gender": "{{person.gender()}}",
    "description": "{{person.bio()}}",
    "leadSource": "{{random('apple', 'banana', 'strawberry')}}",
    "notes": [
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      },
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      }
    ],
    "dates": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "date": "{{date.past()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "customFields": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "field": "{{lorem.words(2)}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "relatedPersons": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "person": "{{person.fullName()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "websites": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "url": "{{internet.url()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "tags": [
      "{{repeat(7)}}",
      {
        "_id": "{{objectId()}}",
        "tag": "{{lorem.words(1)}}",
        "icon": "https://i.pravatar.cc/200"
      }
    ],
    "createdAt": "{{date.past()}}",
    "updatedAt": "{{date.recent()}}"
  }
}</code></pre>
                </div>

                <h4>Contact Profile Details</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/profile
{
  "structure": {
    "_id": "{{objectId()}}",
    "type": "{{random('person')}}",
    "avatar": "https://i.pravatar.cc/500",
    "salutation": "{{person.prefix()}}",
    "jobTitle": "{{person.jobTitle()}}",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "companyName": "{{company.name().toUpperCase()}}",
    "domain": "{{internet.domainName()}}",
    "email": "{{internet.email()}}",
    "countryPhoneCode": "+{{integer(1, 235)}}",
    "phone": "{{phone.number('##########')}}",
    "company": {
      "_id": "{{objectId()}}",
      "avatar": "https://i.pravatar.cc/500",
      "domain": "{{internet.domainName()}}",
      "companyName": "{{company.name().toUpperCase()}}",
      "email": "{{internet.email()}}",
      "countryPhoneCode": "+{{integer(1, 99)}}",
      "phone": "{{phone.number('##########')}}",
      "address": {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      }
    },
    "address": {
      "_id": "{{objectId()}}",
      "type": "primary",
      "country": "{{location.state()}}",
      "street": "{{location.street()}}",
      "city": "{{location.city(5)}}",
      "state": "{{location.state()}}",
      "postalCode": "{{integer(100000, 999999)}}",
      "label": "home"
    }
  }
}</code></pre>
                </div>

                <h4>Contact Lead Information</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/leads
{
  "structure": {
    "_id": "{{objectId()}}",
    "email": "{{internet.email()}}",
    "emails": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "email": "{{internet.email()}}"
      }
    ],
    "countryPhoneCode": "+{{integer(1, 99)}}",
    "phone": "{{phone.number('##########')}}",
    "phones": [
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "countryPhoneCode": "+{{integer(1, 99)}}",
        "phone": "{{phone.number('##########')}}"
      }
    ],
    "group": {
      "_id": "{{objectId()}}",
      "name": "{{lorem.words(1)}}"
    },
    "createdBy": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}"
    },
    "owner": {
      "_id": "{{objectId()}}",
      "name": "{{person.fullName()}}",
      "avatar": "https://i.pravatar.cc/200"
    },
    "notes": [
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      },
      {
        "_id": "{{objectId()}}",
        "note": "{{lorem.paragraph(1)}}"
      }
    ],
    "dates": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "date": "{{date.past()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "customFields": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "field": "{{lorem.words(2)}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "relatedPersons": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "person": "{{person.fullName()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "websites": [
      "{{repeat(3)}}",
      {
        "_id": "{{objectId()}}",
        "url": "{{internet.url()}}",
        "label": "{{lorem.words(1)}}"
      }
    ],
    "tags": [
      "{{repeat(7)}}",
      {
        "_id": "{{objectId()}}",
        "tag": "{{lorem.words(1)}}",
        "icon": "https://i.pravatar.cc/200"
      }
    ],
    "createdAt": "{{date.past()}}"
  }
}</code></pre>
                </div>

                <h4>Contact Address Information</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/address
{
  "structure": {
    "address": [
      {
        "_id": "{{objectId()}}",
        "type": "primary",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "home"
      },
      {
        "_id": "{{objectId()}}",
        "type": "billing",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      },
      "{{repeat(4)}}",
      {
        "_id": "{{objectId()}}",
        "type": "addon",
        "country": "{{location.state()}}",
        "street": "{{location.street()}}",
        "city": "{{location.city(5)}}",
        "state": "{{location.state()}}",
        "postalCode": "{{integer(100000, 999999)}}",
        "label": "work"
      }
    ],
    "billingOnPrimaryAddress": "{{boolean()}}"
  }
}</code></pre>
                </div>

                <h4>Associated Persons</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/associated
{
  "structure": [{
    "_id": "{{objectId()}}",
    "avatar": "https://i.pravatar.cc/500",
    "firstName": "{{person.firstName()}}",
    "middleName": "{{person.middleName()}}",
    "lastName": "{{person.lastName()}}",
    "email": "{{internet.email()}}",
    "address": {
      "_id": "{{objectId()}}",
      "type": "primary",
      "country": "{{location.state()}}",
      "street": "{{location.street()}}",
      "city": "{{location.city(5)}}",
      "state": "{{location.state()}}",
      "postalCode": "{{integer(100000, 999999)}}",
      "label": "home"
    }
  }]
}</code></pre>
                </div>

                <h4>Company and Owner Data</h4>
                <div class="example">
                    <pre><code>POST /api/mock/contacts/company
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}"
  }]
}

POST /api/mock/contacts/owner
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}",
    "avatar": "https://i.pravatar.cc/200"
  }]
}

POST /api/mock/contacts/group
{
  "structure": [{
    "_id": "{{objectId()}}",
    "name": "{{company.name()}}"
  }]
}</code></pre>
                </div>
            </section>
        </div>

        <div class="footer">
            <p>Mirage - Dynamic Mock API Server By Innvoq &copy; 2025</p>
        </div>
    </div>
</body>
</html>
                                                                                                   ./common/errors.js                                                                                  000644  000765  000024  00000003701 15013667750 014623  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Common error handling functions
 * 
 * This file contains functions for handling common error scenarios
 * and returning appropriate error responses.
 */

/**
 * Handle server errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.serverError = (res, message = 'Internal server error') => {
  return res.status(500).json({
    success: false,
    message
  });
};

/**
 * Handle not found errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.notFound = (res, message = 'Resource not found') => {
  return res.status(404).json({
    success: false,
    message
  });
};

/**
 * Handle bad request errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 * @param {Array} errors - Optional validation errors
 */
exports.badRequest = (res, message = 'Bad request', errors = null) => {
  const response = {
    success: false,
    message
  };
  
  if (errors) {
    response.errors = errors;
  }
  
  return res.status(400).json(response);
};

/**
 * Handle unauthorized errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.unauthorized = (res, message = 'Unauthorized') => {
  return res.status(401).json({
    success: false,
    message
  });
};

/**
 * Handle forbidden errors
 * @param {Object} res - Express response object
 * @param {String} message - Optional custom error message
 */
exports.forbidden = (res, message = 'Forbidden') => {
  return res.status(403).json({
    success: false,
    message
  });
};

/**
 * Handle validation errors
 * @param {Object} res - Express response object
 * @param {Array} errors - Validation errors
 */
exports.validationError = (res, errors) => {
  return res.status(422).json({
    success: false,
    message: 'Validation failed',
    errors
  });
};
                                                               ./swagger/swagger.yaml                                                                              000644  000765  000024  00000017630 15013676031 015441  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         openapi: 3.0.0
info:
  title: Mirage Dynamic Mock API Server
  version: 1.0.0
  description: A dynamic mock API server that generates realistic mock data based on provided structures using Faker.js
  license:
    name: ISC
    url: https://opensource.org/licenses/ISC
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:3000
    description: Development server

tags:
  - name: Mock
    description: Mock API endpoints
  - name: Documentation
    description: API documentation

components:
  schemas:
    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message
        errors:
          type: array
          items:
            type: object
            properties:
              msg:
                type: string
                example: Structure is required
              param:
                type: string
                example: structure
              location:
                type: string
                example: body

    MockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          oneOf:
            - type: object
              example:
                id: '{{guid}}'
                name: '{{person.fullName}}'
                email: '{{internet.email}}'
                age: '{{integer(18, 65)}}'
                isActive: '{{boolean}}'
                createdAt: '{{date.past}}'
                address:
                  street: '{{location.street}}'
                  city: '{{location.city}}'
                  state: '{{location.state}}'
                  zipCode: '{{location.zipCode}}'
                  country: '{{location.country}}'
            - type: array
              items:
                type: object
              example:
                - id: '{{guid}}'
                  name: '{{person.fullName}}'
                  email: '{{internet.email}}'
          description: The structure of the mock data to be generated

    MockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: users
        endpoint:
          type: string
          example: list
        records:
          type: integer
          example: 1
        data:
          oneOf:
            - type: object
            - type: array
              items:
                type: object
          example:
            id: '123e4567-e89b-12d3-a456-************'
            name: John Doe
            email: <EMAIL>
            age: 32
            isActive: true
            createdAt: '2023-01-15T08:30:00.000Z'
            address:
              street: 123 Main St
              city: New York
              state: NY
              zipCode: '10001'
              country: USA

    ComplexMockRequest:
      type: object
      required:
        - structure
      properties:
        structure:
          type: object
          example:
            _id: '{{objectId()}}'
            guid: '{{guid()}}'
            type: '{{random("person", "company")}}'
            label:
              _id: '{{objectId()}}'
              name: '{{random("client", "reference", "open")}}'
            avatar: https://i.pravatar.cc/500
            name: '{{person.prefix()}} {{person.firstName()}} {{person.lastName()}}'
            email: '{{internet.email()}}'
            phone: '+{{integer(1, 99)}} {{phone.number()}}'
            dob: '{{date.birthdate()}}'
            company:
              _id: '{{objectId()}}'
              companyName: '{{company.name().toUpperCase()}}'
            jobTitle: '{{person.jobTitle()}}'
            department: '{{person.jobArea()}}'
            tags:
              - '{{repeat(7)}}'
              - '{{lorem.words(1)}}'
            friends:
              - '{{repeat(4)}}'
              - _id: '{{objectId()}}'
                name: '{{person.firstName()}} {{person.lastName()}}'
          description: Complex structure with nested objects, arrays, and method chaining

    ComplexMockResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        app:
          type: string
          example: contacts
        endpoint:
          type: string
          example: table
        records:
          type: integer
          example: 1
        data:
          type: array
          items:
            type: object
          example:
            - _id: 682f781e1fea7d5a32f96673
              guid: e646ed09-8ed2-486d-8512-19cee0689e08
              type: company
              label:
                _id: 682f781e1fea7d5a32f96674
                name: reference
              avatar: https://i.pravatar.cc/500
              name: Dr. Alene McCullough
              email: <EMAIL>
              phone: +96 878.502.5645
              dob: '1973-11-20T12:05:47.902Z'
              company:
                _id: 682f781e1fea7d5a32f96675
                companyName: CORWIN GROUP
              jobTitle: Forward Accountability Coordinator
              department: Brand
              tags:
                - beatus
                - uberrime
                - abundans
              friends:
                - _id: 682f781e1fea7d5a32f96676
                  name: Alfreda Schumm
                - _id: 682f781e1fea7d5a32f96677
                  name: Virgil Witting

paths:
  /api/mock/{app}/{endpoint}:
    post:
      tags:
        - Mock
      summary: Generate mock data based on the provided structure
      description: This endpoint generates mock data based on the structure provided in the request body. The structure can be an object or an array of objects with templates for generating mock data.
      parameters:
        - name: app
          in: path
          required: true
          schema:
            type: string
          description: Application name (e.g., users, products, orders)
          example: users
        - name: endpoint
          in: path
          required: true
          schema:
            type: string
          description: Endpoint name (e.g., list, details, search)
          example: list
        - name: records
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Number of records to generate
          example: 5
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/MockRequest'
                - $ref: '#/components/schemas/ComplexMockRequest'
      responses:
        '200':
          description: Successful response with generated mock data
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/MockResponse'
                  - $ref: '#/components/schemas/ComplexMockResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /docs:
    get:
      tags:
        - Documentation
      summary: Get HTML documentation
      description: Returns an HTML page with detailed documentation about the API
      responses:
        '200':
          description: HTML documentation page
          content:
            text/html:
              schema:
                type: string

  /api-docs:
    get:
      tags:
        - Documentation
      summary: Get Swagger documentation
      description: Returns the Swagger UI for the API documentation
      responses:
        '200':
          description: Swagger UI page
          content:
            text/html:
              schema:
                type: string
                                                                                                        ./swagger/swagger.js                                                                                000644  000765  000024  00000001302 15013676114 015102  0                                                                                                    ustar 00admin                           staff                           000000  000000                                                                                                                                                                         /**
 * Swagger configuration for the Mirage Dynamic Mock API Server
 *
 * This file loads the OpenAPI specification from the YAML file
 * and exports it for use with swagger-ui-express.
 */

const swaggerJsdoc = require('swagger-jsdoc');
const YAML = require('js-yaml');
const fs = require('fs');
const path = require('path');

// Load the YAML file
const yamlPath = path.join(__dirname, 'swagger.yaml');
const yamlSpec = YAML.load(fs.readFileSync(yamlPath, 'utf8'));

// Configure swagger-jsdoc
const options = {
  definition: yamlSpec,
  apis: ['./routes/*.js', './controllers/**/*.js', './app.js'],
};

// Generate the Swagger specification
const specs = swaggerJsdoc(options);

module.exports = specs;
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              