# Mirage Dynamic Mock API Server - AI Agent Onboarding Guide

## Overview for AI Systems

Mirage is a dynamic mock API server that generates realistic fake data based on custom structures. It's designed to help AI agents and developers quickly create mock data for testing, prototyping, and development purposes.

## Key Capabilities

### 1. Dynamic Data Generation
- Generate realistic mock data using Faker.js templates
- Support for any data structure (objects, arrays, nested data)
- Method chaining support (e.g., `.toUpperCase()`, `.toLowerCase()`)
- Custom field types and relationships

### 2. Triple API Support
- **REST API**: Traditional HTTP endpoints
- **GraphQL API**: Query and mutation support
- **WebSocket API**: Real-time bidirectional communication
- All APIs use the same underlying data generation logic

### 3. Template System
Use `{{template}}` syntax with Faker.js helpers:
- `{{person.fullName}}` → "John Doe"
- `{{internet.email}}` → "<EMAIL>"
- `{{company.name().toUpperCase()}}` → "ACME CORPORATION"
- `{{integer(1, 100)}}` → 42
- `{{date.past}}` → "2023-05-15T10:30:00.000Z"

## API Endpoints

### REST API
```
POST /api/mock/{app}/{endpoint}
Content-Type: application/json

{
  "structure": {
    "id": "{{guid}}",
    "name": "{{person.fullName}}",
    "email": "{{internet.email}}"
  }
}

Query Parameters:
- records: Number of records to generate (default: 1)
```

### GraphQL API
```
POST /graphql
Content-Type: application/json

Query Example:
{
  "query": "query { getMock(input: { app: \"users\", endpoint: \"list\", structure: { id: \"{{guid}}\", name: \"{{person.fullName}}\" }, records: 3 }) { ... on MockDataResponse { success data } } }"
}

Mutation Example:
{
  "query": "mutation { postMock(input: { app: \"contacts\", endpoint: \"form\", structure: { name: \"{{person.fullName}}\", company: { name: \"{{company.name().toUpperCase()}}\" } } }) { ... on MockDataResponse { success data } } }"
}
```

### WebSocket API
```
WebSocket Connection: ws://localhost:3000/ws

Message Format:
{
  "type": "mock|system|room|namespace",
  "event": "specific_event_name",
  "data": {
    // Event-specific data
  }
}

Examples:

// Generate mock data
{
  "type": "mock",
  "event": "generate",
  "data": {
    "structure": {
      "id": "{{guid}}",
      "name": "{{person.fullName}}"
    },
    "records": 3
  }
}

// Join a room
{
  "type": "room",
  "event": "join",
  "data": {
    "roomName": "chat-room"
  }
}

// Subscribe to real-time updates
{
  "type": "mock",
  "event": "subscribe",
  "data": {
    "topic": "user-updates",
    "structure": {
      "id": "{{guid}}",
      "name": "{{person.fullName}}"
    }
  }
}
```

## Common Use Cases for AI Agents

### 1. Frontend Development Support
Generate mock data for UI components, forms, and data tables.

### 2. API Testing
Create realistic test data for API endpoints and database seeding.

### 3. Prototype Development
Quickly generate sample data for demos and proof-of-concepts.

### 4. Data Modeling
Test different data structures and relationships.

### 5. Real-time Applications
Generate live data streams for real-time dashboards and monitoring.

### 6. Interactive Demos
Create dynamic demonstrations with live data updates.

### 7. Load Testing
Generate continuous data streams for performance testing.

## Template Categories

### Personal Data
- `{{person.fullName}}`, `{{person.firstName}}`, `{{person.lastName}}`
- `{{person.jobTitle}}`, `{{person.prefix}}`, `{{person.suffix}}`
- `{{person.gender}}`, `{{person.bio}}`

### Contact Information
- `{{internet.email}}`, `{{internet.url}}`, `{{internet.domainName}}`
- `{{phone.number}}`, `{{phone.number("###-###-####")}}`

### Location Data
- `{{location.street}}`, `{{location.city}}`, `{{location.state}}`
- `{{location.country}}`, `{{location.zipCode}}`
- `{{location.latitude}}`, `{{location.longitude}}`

### Business Data
- `{{company.name}}`, `{{company.catchPhrase}}`
- `{{finance.amount}}`, `{{finance.currencyCode}}`

### Dates and Numbers
- `{{date.past}}`, `{{date.future}}`, `{{date.birthdate}}`
- `{{integer(min, max)}}`, `{{floating(min, max)}}`

### Text Content
- `{{lorem.words(count)}}`, `{{lorem.sentence}}`
- `{{lorem.paragraph}}`, `{{lorem.text}}`

### Identifiers
- `{{guid}}`, `{{objectId()}}`, `{{random("option1", "option2")}}`

## Advanced Features

### Method Chaining
Apply JavaScript string methods to generated data:
- `{{company.name().toUpperCase()}}` → "ACME CORPORATION"
- `{{person.firstName().toLowerCase()}}` → "john"

### Array Generation
Use `{{repeat(count)}}` for arrays:
```json
{
  "tags": ["{{repeat(3)}}","{{lorem.words(1)}}"],
  "friends": [
    "{{repeat(5)}}",
    {
      "id": "{{guid}}",
      "name": "{{person.fullName}}"
    }
  ]
}
```

### Nested Objects
Create complex data structures:
```json
{
  "user": {
    "profile": {
      "personal": {
        "name": "{{person.fullName}}",
        "age": "{{integer(18, 65)}}"
      },
      "contact": {
        "email": "{{internet.email}}",
        "phone": "{{phone.number}}"
      }
    }
  }
}
```

## WebSocket Advanced Features

### Real-time Data Streaming
Generate continuous data streams with configurable intervals:
```javascript
{
  "type": "mock",
  "event": "generateStream",
  "data": {
    "structure": { "id": "{{guid}}", "value": "{{integer(1, 100)}}" },
    "interval": 1000,
    "duration": 10000
  }
}
```

### Room-based Communication
Join rooms for group-based data sharing:
```javascript
// Join room
{
  "type": "room",
  "event": "join",
  "data": { "roomName": "analytics-room" }
}

// Broadcast to room
{
  "type": "system",
  "event": "broadcastToRoom",
  "data": {
    "roomName": "analytics-room",
    "broadcastMessage": { "alert": "New data available" }
  }
}
```

### Subscription System
Subscribe to automatic data updates:
```javascript
{
  "type": "mock",
  "event": "subscribe",
  "data": {
    "topic": "user-metrics",
    "structure": {
      "timestamp": "{{date.recent}}",
      "users": "{{integer(100, 1000)}}"
    }
  }
}
```

### Heartbeat & Connection Management
Automatic connection health monitoring with ping/pong:
```javascript
{
  "type": "system",
  "event": "ping",
  "data": {}
}
```

## Response Format

### REST API Response
```json
{
  "success": true,
  "app": "users",
  "endpoint": "list",
  "records": 1,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

### GraphQL Response
```json
{
  "data": {
    "getMock": {
      "success": true,
      "data": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    }
  }
}
```

## Error Handling

Both APIs return structured error responses:
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

## Documentation Endpoints

- **Interactive REST API**: `/api-docs` (Swagger UI)
- **Interactive GraphQL**: `/graphql` (GraphQL Playground)
- **WebSocket Demo**: `/ws-demo` (Interactive WebSocket testing interface)
- **WebSocket Info**: `/ws-info` (WebSocket server metrics and information)
- **HTML Documentation**: `/docs`
- **AI Onboarding**: `/ai-info` (This information in JSON format)

## Quick Start for AI Agents

1. **Choose API Style**: REST or GraphQL based on your needs
2. **Define Structure**: Create a JSON structure with Faker.js templates
3. **Make Request**: Send POST request to appropriate endpoint
4. **Use Data**: Extract generated data from response

## Server Information

- **Base URL**: `http://localhost:3000` (default)
- **CORS**: Enabled for all origins
- **Content-Type**: `application/json`
- **Rate Limiting**: None (development server)

This API is perfect for AI agents that need to generate realistic mock data for various applications, testing scenarios, or development purposes.
