- Understand the currunt project structure & the achievements in project.

**New Feature**

- as eelier we added support for Rest & Graphql in app for mock responses, now additionally we need to add support for WebSocket for real time communication.
- follow the current folder structure & keep this new feature as separate folder to be organized.
- we have maintained documents at various places like file or end point so you need to update all the documents according to new feature in a same formate as eelier.
- Follow below practices to develop strong websocket service:
  1. Bi-directional Real-time Communication
  2. Custom Event-based Messaging
    - Implement your own event routing system.
    - Use a simple JSON structure.
  3. Build a handler map
  4. Automatic Reconnection
    - Client-side: use retry strategies (exponential backoff).
    - Socket.IO does this out of the box — you’ll reimplement it manually.
    - Detect onclose or onerror → retry after delay.
  5. Heartbeat / Keep-alive (Ping/Pong)
    - Periodically ping from server → expect a pong back.
    - If no response within X ms, terminate connection.
    - Helps detect dead connections — crucial for real-time apps.
  6. Room / Channel System (Pub/Sub)
    - Use in-memory "rooms":
  7. Binary Data Support (Native in raw WebSocket.)
    - WebSocket supports both text and binary (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>) out-of-the-box.
    - More performant than JSON when optimized.
  8. Custom Protocol & Compression
    - Define your own message protocol (e.g., binary + opcodes).
    - Use libraries like msgpack or protobuf (only if its open source & free to use) for compact messages.
    - Customize per your performance or bandwidth requirements.
  9. Performance Optimization
    - Raw WebSocket is faster than Socket.IO due to lack of abstraction layers.
    - With custom logic, you can optimize:
        - Memory usage
        - Message size
        - Event handling time
        - Bandwidth
  10. Create your own broadcast helper logic.
  11. Namespaces Can be handled via message event patterns.
  12. You are open yo use any library in need (only if its open source & free to use)

- Error handling should be good enough.
- You need to complete all this task recursively without stopping till successfully completions of  all requirements
- reminder: we have maintained documents at various places like file or end point so you need to update all the documents according to new feature in a same formate as eelier.