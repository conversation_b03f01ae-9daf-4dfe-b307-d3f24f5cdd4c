# Docker Compose configuration for Mirage API Server
# This file provides different environments for development and production

version: '3.8'

services:
  # =============================================================================
  # Mirage API Server
  # =============================================================================
  mirage-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: mirage-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
    networks:
      - mirage-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mirage-api.rule=Host(`api.yourdomain.com`)"
      - "traefik.http.routers.mirage-api.tls=true"
      - "traefik.http.routers.mirage-api.tls.certresolver=letsencrypt"

  # =============================================================================
  # Nginx Reverse Proxy (Optional)
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: mirage-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - mirage-api
    networks:
      - mirage-network
    profiles:
      - with-nginx

  # =============================================================================
  # Monitoring with Prometheus (Optional)
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: mirage-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - mirage-network
    profiles:
      - monitoring

  # =============================================================================
  # Grafana Dashboard (Optional)
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: mirage-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - mirage-network
    profiles:
      - monitoring

# =============================================================================
# Networks
# =============================================================================
networks:
  mirage-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# Volumes
# =============================================================================
volumes:
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
