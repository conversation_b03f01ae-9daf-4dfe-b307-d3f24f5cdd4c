/**
 * Comprehensive WebSocket Test Script
 *
 * This script tests all WebSocket functionality of the Mirage API server
 */

const WebSocket = require("ws");

// Test configuration
const tests = [
  {
    name: "System Ping",
    message: {
      type: "system",
      event: "ping",
      data: {},
    },
  },
  {
    name: "Get System Info",
    message: {
      type: "system",
      event: "getInfo",
      data: {},
    },
  },
  {
    name: "Get Server Metrics",
    message: {
      type: "system",
      event: "getMetrics",
      data: {},
    },
  },
  {
    name: "Generate Single Mock Data",
    message: {
      type: "mock",
      event: "generate",
      data: {
        app: "test",
        endpoint: "users",
        structure: {
          id: "{{guid}}",
          name: "{{person.fullName}}",
          email: "{{internet.email}}",
          company: "{{company.name().toUpperCase()}}",
        },
        records: 2,
      },
    },
  },
  {
    name: "Generate Batch Mock Data",
    message: {
      type: "mock",
      event: "generateBatch",
      data: {
        requests: [
          {
            app: "users",
            endpoint: "list",
            structure: { id: "{{guid}}", name: "{{person.fullName}}" },
            records: 1,
          },
          {
            app: "products",
            endpoint: "list",
            structure: {
              id: "{{guid}}",
              name: "{{commerce.productName}}",
              price: "{{finance.amount}}",
            },
            records: 1,
          },
        ],
      },
    },
  },
  {
    name: "Validate Template",
    message: {
      type: "mock",
      event: "validateTemplate",
      data: {
        structure: {
          id: "{{guid}}",
          name: "{{person.fullName}}",
          invalid: "{{invalid.helper}}",
        },
      },
    },
  },
  {
    name: "Get Available Templates",
    message: {
      type: "mock",
      event: "getTemplates",
      data: {},
    },
  },
  {
    name: "Join Room",
    message: {
      type: "room",
      event: "join",
      data: {
        roomName: "test-room",
      },
    },
  },
  {
    name: "Subscribe to Updates",
    message: {
      type: "mock",
      event: "subscribe",
      data: {
        topic: "user-updates",
        structure: {
          id: "{{guid}}",
          status: '{{random("online", "offline")}}',
        },
      },
    },
  },
  {
    name: "Generate Stream (5 seconds)",
    message: {
      type: "mock",
      event: "generateStream",
      data: {
        structure: {
          timestamp: "{{date.recent}}",
          value: "{{integer(1, 100)}}",
        },
        interval: 1000,
        duration: 5000,
      },
    },
  },
];

let currentTestIndex = 0;
let ws = null;
let testResults = [];

function runTest(testIndex) {
  if (testIndex >= tests.length) {
    console.log("\n🎉 All tests completed!");
    console.log("\n📊 Test Results Summary:");
    testResults.forEach((result, index) => {
      const status = result.success ? "✅" : "❌";
      console.log(`${status} ${tests[index]?.name}: ${result?.message}`);
    });

    // Clean up and exit
    setTimeout(() => {
      if (ws) ws.close();
      process.exit(0);
    }, 2000);
    return;
  }

  const test = tests[testIndex];
  console.log(
    `\n🔄 Running Test ${testIndex + 1}/${tests.length}: ${test.name}`
  );

  try {
    ws.send(JSON.stringify(test.message));
    console.log("📤 Sent:", JSON.stringify(test.message, null, 2));

    // Wait for response before next test
    setTimeout(() => {
      runTest(testIndex + 1);
    }, 2000);
  } catch (error) {
    console.error(`❌ Error sending test ${testIndex + 1}:`, error.message);
    testResults.push({ success: false, message: error.message });
    runTest(testIndex + 1);
  }
}

// Connect to WebSocket server
console.log("🚀 Starting comprehensive WebSocket tests...");
ws = new WebSocket("ws://localhost:3000/ws");

ws.on("open", function open() {
  console.log("✅ WebSocket connected successfully");

  // Start running tests
  setTimeout(() => {
    runTest(0);
  }, 1000);
});

ws.on("message", function message(data) {
  try {
    const message = JSON.parse(data);
    console.log("📨 Received response:");
    console.log(`   Type: ${message.type}`);
    console.log(`   Event: ${message.event}`);

    // Record test result
    const isSuccess = message.data && message.data.success !== false;
    const resultMessage = message.data?.message || "Response received";

    testResults.push({
      success: isSuccess,
      message: resultMessage,
      response: message,
    });

    if (message.data) {
      if (message.data.data && typeof message.data.data === "object") {
        console.log(
          "   Generated Data:",
          JSON.stringify(message.data.data, null, 2)
        );
      } else if (message.data.success !== undefined) {
        console.log(`   Success: ${message.data.success}`);
        if (message.data.message) {
          console.log(`   Message: ${message.data.message}`);
        }
        if (message.data.code) {
          console.log(`   Code: ${message.data.code}`);
        }
      }
    }
  } catch (error) {
    console.log("📨 Received raw message:", data.toString());
    testResults.push({
      success: false,
      message: "Failed to parse response",
      response: data.toString(),
    });
  }
});

ws.on("close", function close(code, reason) {
  console.log(`\n❌ WebSocket connection closed: ${code} - ${reason}`);
});

ws.on("error", function error(err) {
  console.error("\n❌ WebSocket error:", err.message);
  process.exit(1);
});

// Handle process termination
process.on("SIGINT", () => {
  console.log("\n🛑 Terminating tests...");
  if (ws) ws.close();
  process.exit(0);
});

// Timeout for the entire test suite
setTimeout(() => {
  console.log("\n⏰ Test suite timeout reached");
  if (ws) ws.close();
  process.exit(1);
}, 60000); // 60 seconds timeout
