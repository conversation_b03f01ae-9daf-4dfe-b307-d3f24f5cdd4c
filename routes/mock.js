/**
 * Dynamic Mock API Routes
 *
 * This file defines the dynamic route for the mock API endpoints.
 * The route accepts any app and endpoint combination and passes
 * the request to the mock controller.
 */

const express = require('express');
const router = express.Router();
const mockController = require('../controllers/mock/controller');
const { body } = require('express-validator');

// Dynamic route that accepts any app and endpoint combination
// Example: /api/mock/users/list, /api/mock/products/details, etc.
router.route('/:app/:endpoint').post(
  // Validate that the structure is provided in the request body
  [body('structure').exists().withMessage('Structure is required')],
  mockController
);

module.exports = router;
