/**
 * WebSocket Test Script
 * 
 * This script tests the WebSocket functionality of the Mirage API server
 */

const WebSocket = require('ws');

// Connect to WebSocket server
const ws = new WebSocket('ws://localhost:3000/ws');

ws.on('open', function open() {
  console.log('✅ WebSocket connected successfully');
  
  // Test 1: Ping server
  console.log('\n🔄 Testing ping...');
  ws.send(JSON.stringify({
    type: 'system',
    event: 'ping',
    data: {}
  }));
  
  // Test 2: Generate mock data
  setTimeout(() => {
    console.log('\n🔄 Testing mock data generation...');
    ws.send(JSON.stringify({
      type: 'mock',
      event: 'generate',
      data: {
        app: 'test',
        endpoint: 'users',
        structure: {
          id: '{{guid}}',
          name: '{{person.fullName}}',
          email: '{{internet.email}}',
          company: '{{company.name().toUpperCase()}}'
        },
        records: 2
      }
    }));
  }, 1000);
  
  // Test 3: Join room
  setTimeout(() => {
    console.log('\n🔄 Testing room join...');
    ws.send(JSON.stringify({
      type: 'room',
      event: 'join',
      data: {
        roomName: 'test-room'
      }
    }));
  }, 2000);
  
  // Test 4: Get server info
  setTimeout(() => {
    console.log('\n🔄 Testing server info...');
    ws.send(JSON.stringify({
      type: 'system',
      event: 'getInfo',
      data: {}
    }));
  }, 3000);
  
  // Test 5: Subscribe to updates
  setTimeout(() => {
    console.log('\n🔄 Testing subscription...');
    ws.send(JSON.stringify({
      type: 'mock',
      event: 'subscribe',
      data: {
        topic: 'user-updates',
        structure: {
          id: '{{guid}}',
          status: '{{random("online", "offline")}}'
        }
      }
    }));
  }, 4000);
  
  // Close connection after tests
  setTimeout(() => {
    console.log('\n🔄 Closing connection...');
    ws.close();
  }, 8000);
});

ws.on('message', function message(data) {
  try {
    const message = JSON.parse(data);
    console.log('\n📨 Received message:');
    console.log(`   Type: ${message.type}`);
    console.log(`   Event: ${message.event}`);
    
    if (message.data) {
      if (message.data.data && typeof message.data.data === 'object') {
        console.log('   Generated Data:');
        console.log('  ', JSON.stringify(message.data.data, null, 4));
      } else if (message.data.success !== undefined) {
        console.log(`   Success: ${message.data.success}`);
        if (message.data.message) {
          console.log(`   Message: ${message.data.message}`);
        }
      }
    }
  } catch (error) {
    console.log('\n📨 Received raw message:', data.toString());
  }
});

ws.on('close', function close() {
  console.log('\n❌ WebSocket connection closed');
  process.exit(0);
});

ws.on('error', function error(err) {
  console.error('\n❌ WebSocket error:', err.message);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Terminating...');
  ws.close();
  process.exit(0);
});
