# 🚀 Mirage API Server - GitLab CI/CD Deployment Guide

This guide provides step-by-step instructions for setting up GitLab CI/CD pipeline to deploy the Mirage API server to your VPS.

## 📋 Prerequisites

- GitLab account with your repository
- VPS/Server with Ubuntu 20.04+ or similar Linux distribution
- SSH access to your VPS
- Domain name (optional, for production)

## 🏗️ Part 1: VPS Server Setup

### Step 1: Prepare Your VPS

1. **Connect to your VPS via SSH:**
   ```bash
   ssh root@your-server-ip
   ```

2. **Download and run the setup script:**
   ```bash
   wget https://raw.githubusercontent.com/your-repo/mirage-api/main/scripts/setup-server.sh
   chmod +x setup-server.sh
   sudo ./setup-server.sh
   ```

   Or manually copy the `scripts/setup-server.sh` file to your server and run it.

3. **Follow the prompts** - the script will:
   - Update system packages
   - Install Node.js 18
   - Install PM2 process manager
   - Create deploy user
   - Setup directories and permissions
   - Configure firewall
   - Optionally install Nginx
   - Setup log rotation and monitoring

### Step 2: Configure SSH Access

1. **Generate SSH key pair** (on your local machine):
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```

2. **Copy public key to your VPS:**
   ```bash
   ssh-copy-id -i ~/.ssh/id_rsa.pub deploy@your-server-ip
   ```

3. **Test SSH connection:**
   ```bash
   ssh deploy@your-server-ip
   ```

4. **Copy the private key content** for GitLab CI/CD variables:
   ```bash
   cat ~/.ssh/id_rsa
   ```

## 🔧 Part 2: GitLab CI/CD Configuration

### Step 1: Set GitLab CI/CD Variables

Go to your GitLab project → **Settings** → **CI/CD** → **Variables** and add the following variables:

#### 🔑 Required Variables

| Variable Name | Value | Protected | Masked | Description |
|---------------|-------|-----------|---------|-------------|
| `SSH_PRIVATE_KEY` | Your private SSH key content | ✅ | ❌ | SSH private key for server access |
| `DEPLOY_USER` | `deploy` | ❌ | ❌ | Username for deployment |
| `STAGING_SERVER_IP` | Your staging server IP | ❌ | ❌ | Staging server IP address |
| `PRODUCTION_SERVER_IP` | Your production server IP | ✅ | ❌ | Production server IP address |

#### 📝 Example SSH Private Key Format
```
-----BEGIN OPENSSH PRIVATE KEY-----
b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAAlwAAAAdzc2gtcn
NhAAAAAwEAAQAAAIEA1234567890abcdef...
...
-----END OPENSSH PRIVATE KEY-----
```

### Step 2: Configure Branch Protection (Optional)

1. Go to **Settings** → **Repository** → **Protected branches**
2. Protect `main` branch:
   - **Allowed to merge**: Maintainers
   - **Allowed to push**: No one
3. Protect `develop` branch:
   - **Allowed to merge**: Developers + Maintainers
   - **Allowed to push**: Developers + Maintainers

### Step 3: Environment Configuration

The pipeline creates two environments:
- **Staging**: Deploys from `develop` branch automatically
- **Production**: Deploys from `main` branch manually

## 🔄 Part 3: Pipeline Workflow

### Pipeline Stages

1. **Validate** 🔍
   - Syntax validation
   - Dependency security check

2. **Test** 🧪
   - Unit tests
   - Integration tests
   - API endpoint testing

3. **Build** 🏗️
   - Application build
   - Production dependencies installation

4. **Security** 🔒
   - Security vulnerability scanning

5. **Deploy** 🚀
   - Staging deployment (automatic on `develop`)
   - Production deployment (manual on `main`)

6. **Verify** ✅
   - Health checks
   - API endpoint verification

### Deployment Flow

```mermaid
graph LR
    A[Push to develop] --> B[Auto Deploy to Staging]
    C[Push to main] --> D[Manual Deploy to Production]
    B --> E[Staging Verification]
    D --> F[Production Verification]
```

## 🚀 Part 4: Deployment Process

### Staging Deployment

1. **Push to develop branch:**
   ```bash
   git checkout develop
   git add .
   git commit -m "feat: add new feature"
   git push origin develop
   ```

2. **Pipeline automatically:**
   - Runs tests
   - Builds application
   - Deploys to staging server
   - Verifies deployment

3. **Access staging:**
   ```
   http://your-staging-server-ip:3000
   ```

### Production Deployment

1. **Merge to main branch:**
   ```bash
   git checkout main
   git merge develop
   git push origin main
   ```

2. **Manual deployment:**
   - Go to GitLab → **CI/CD** → **Pipelines**
   - Find the pipeline for main branch
   - Click **Manual** button for production deployment

3. **Access production:**
   ```
   http://your-production-server-ip:3000
   ```

## 🔧 Part 5: Server Management

### PM2 Commands

```bash
# Check application status
pm2 status

# View logs
pm2 logs mirage-api

# Restart application
pm2 restart mirage-api

# Stop application
pm2 stop mirage-api

# Monitor resources
pm2 monit
```

### Nginx Commands (if installed)

```bash
# Check Nginx status
sudo systemctl status nginx

# Restart Nginx
sudo systemctl restart nginx

# Test configuration
sudo nginx -t

# View access logs
sudo tail -f /var/log/nginx/access.log
```

### Log Locations

- **Application logs**: `/var/log/mirage-api/`
- **Nginx logs**: `/var/log/nginx/`
- **PM2 logs**: `~/.pm2/logs/`

## 🔍 Part 6: Monitoring & Troubleshooting

### Health Checks

The setup script creates automatic health checks that run every 5 minutes:

```bash
# Manual health check
/usr/local/bin/mirage-health-check

# View health check logs
tail -f /var/log/mirage-api/health-check.log
```

### Common Issues

#### 1. SSH Connection Failed
```bash
# Test SSH connection
ssh -v deploy@your-server-ip

# Check SSH key format in GitLab variables
# Ensure no extra spaces or characters
```

#### 2. Permission Denied
```bash
# Fix ownership
sudo chown -R deploy:deploy /opt/mirage-api

# Fix permissions
sudo chmod -R 755 /opt/mirage-api
```

#### 3. Application Won't Start
```bash
# Check PM2 status
pm2 status

# View detailed logs
pm2 logs mirage-api --lines 100

# Check Node.js version
node --version
```

#### 4. Port Already in Use
```bash
# Check what's using port 3000
sudo lsof -i :3000

# Kill process if needed
sudo kill -9 <PID>
```

### Debugging Commands

```bash
# Check system resources
htop

# Check disk space
df -h

# Check memory usage
free -h

# Check network connections
netstat -tulpn | grep :3000

# Test API endpoints
curl http://localhost:3000/
curl http://localhost:3000/api-docs
```

## 🔐 Part 7: Security Best Practices

### Server Security

1. **Keep system updated:**
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **Configure fail2ban:**
   ```bash
   sudo apt install fail2ban
   sudo systemctl enable fail2ban
   ```

3. **Disable root SSH:**
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Set: PermitRootLogin no
   sudo systemctl restart ssh
   ```

### Application Security

1. **Environment variables** - Store sensitive data in environment variables
2. **HTTPS** - Use SSL certificates for production
3. **Rate limiting** - Implement rate limiting for APIs
4. **CORS** - Configure CORS properly for production

## 📚 Part 8: Additional Resources

### Useful Commands

```bash
# View GitLab CI/CD pipeline logs
# Go to GitLab → CI/CD → Pipelines → Click on pipeline

# Backup application
sudo tar -czf /opt/backups/mirage-api-$(date +%Y%m%d).tar.gz /opt/mirage-api

# Restore from backup
sudo tar -xzf /opt/backups/mirage-api-YYYYMMDD.tar.gz -C /
```

### Environment URLs

- **Staging**: `http://your-staging-server-ip:3000`
- **Production**: `http://your-production-server-ip:3000`
- **API Documentation**: `/api-docs`
- **Client Integration Guide**: `/client-integration`
- **WebSocket Demo**: `/ws-demo`

## 🎉 Conclusion

Your Mirage API server is now set up with a robust CI/CD pipeline! The pipeline will automatically test and deploy your code, ensuring reliable deployments to both staging and production environments.

For any issues or questions, check the troubleshooting section or review the pipeline logs in GitLab.
