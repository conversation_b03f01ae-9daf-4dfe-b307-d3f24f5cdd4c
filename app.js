/**
 * Main application entry point
 *
 * This file sets up the Express server with necessary middleware
 * and routes for the dynamic mock API endpoints.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const { body } = require('express-validator');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger/swagger');
const { createGraphQLServer } = require('./graphql/server');
const mockRoutes = require('./routes/mock');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Configure CORS to allow requests from all domains (both HTTP and HTTPS)
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Routes
app.use('/api/mock', mockRoutes);

// GraphQL server
const graphQLServer = createGraphQLServer();
app.use('/graphql', graphQLServer);

// Swagger documentation route
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Mirage API Documentation',
}));

// HTML documentation route
app.get('/docs', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'documentation.html'));
});

// Root route with documentation links
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to the Dynamic Mock API Server',
    description: 'Generate custom mock data based on your defined structure',
    apis: {
      rest: {
        endpoint: '/api/mock/:app/:endpoint',
        method: 'POST',
        description: 'REST API for generating mock data'
      },
      graphql: {
        endpoint: '/graphql',
        description: 'GraphQL API for generating mock data with queries and mutations'
      }
    },
    documentation: {
      html: {
        url: '/docs',
        description: 'Visit the HTML documentation page for detailed information and examples'
      },
      swagger: {
        url: '/api-docs',
        description: 'Visit the Swagger UI for interactive REST API documentation'
      },
      graphql: {
        url: '/graphql',
        description: 'Visit the GraphQL playground for interactive GraphQL API testing'
      }
    },
    usage: {
      rest: {
        endpoint: '/api/mock/:app/:endpoint',
        method: 'POST',
        body: {
          structure: 'Object or Array defining the data structure with faker templates'
        },
        query: {
          records: 'Number of records to generate (default: 1)'
        }
      },
      graphql: {
        endpoint: '/graphql',
        queries: ['getMock'],
        mutations: ['postMock'],
        input: {
          app: 'Application name (e.g., users, products, orders)',
          endpoint: 'Endpoint name (e.g., list, details, search)',
          structure: 'Object or Array defining the data structure with faker templates',
          records: 'Number of records to generate (default: 1)'
        }
      }
    },
    examples: {
      rest: {
        request: {
          url: '/api/mock/users/list?records=5',
          body: {
            structure: {
              id: '{{guid}}',
              name: '{{person.fullName}}',
              email: '{{internet.email}}',
              age: '{{integer(18, 65)}}',
              address: {
                street: '{{location.street}}',
                city: '{{location.city}}',
                country: '{{location.country}}'
              }
            }
          }
        }
      },
      graphql: {
        query: `query {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      data
    }
  }
}`,
        mutation: `mutation {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      name: "{{person.fullName}}"
      company: {
        name: "{{company.name().toUpperCase()}}"
      }
    }
  }) {
    ... on MockDataResponse {
      success
      data
    }
  }
}`
      }
    }
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Dynamic Mock API Server is running on port ${PORT}`);
});

module.exports = app;
