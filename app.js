/**
 * Main application entry point
 *
 * This file sets up the Express server with necessary middleware
 * and routes for the dynamic mock API endpoints.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const { body } = require('express-validator');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger/swagger');
const mockRoutes = require('./routes/mock');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Configure CORS to allow requests from all domains (both HTTP and HTTPS)
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Routes
app.use('/api/mock', mockRoutes);

// Swagger documentation route
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Mirage API Documentation',
}));

// HTML documentation route
app.get('/docs', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'documentation.html'));
});

// Root route with documentation links
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to the Dynamic Mock API Server',
    description: 'Generate custom mock data based on your defined structure',
    documentation: {
      html: {
        url: '/docs',
        description: 'Visit the HTML documentation page for detailed information and examples'
      },
      swagger: {
        url: '/api-docs',
        description: 'Visit the Swagger UI for interactive API documentation'
      }
    },
    usage: {
      endpoint: '/api/mock/:app/:endpoint',
      method: 'POST',
      body: {
        structure: 'Object or Array defining the data structure with faker templates'
      },
      query: {
        records: 'Number of records to generate (default: 1)'
      }
    },
    example: {
      request: {
        url: '/api/mock/users/list?records=5',
        body: {
          structure: {
            id: '{{guid}}',
            name: '{{person.fullName}}',
            email: '{{internet.email}}',
            age: '{{integer(18, 65)}}',
            address: {
              street: '{{location.street}}',
              city: '{{location.city}}',
              country: '{{location.country}}'
            }
          }
        }
      }
    }
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Dynamic Mock API Server is running on port ${PORT}`);
});

module.exports = app;
