/**
 * Main application entry point
 *
 * This file sets up the Express server with necessary middleware
 * and routes for the dynamic mock API endpoints.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const { body } = require('express-validator');
const swaggerUi = require('swagger-ui-express');
const swaggerSpecs = require('./swagger/swagger');
const { createGraphQLServer } = require('./graphql/server');
const { getAIInfo } = require('./controllers/ai-info');
const WebSocketIntegration = require('./websocket');
const mockRoutes = require('./routes/mock');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Configure CORS to allow requests from all domains (both HTTP and HTTPS)
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Routes
app.use('/api/mock', mockRoutes);

// GraphQL server
const graphQLServer = createGraphQLServer();
app.use('/graphql', graphQLServer);

// Swagger documentation route
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpecs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Mirage API Documentation',
}));

// HTML documentation route
app.get('/docs', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'documentation.html'));
});

// WebSocket demo route
app.get('/ws-demo', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'websocket-demo.html'));
});

// AI onboarding routes
app.get('/ai-info', getAIInfo);
app.get('/ai-onboarding.md', (req, res) => {
  res.sendFile(path.join(__dirname, 'ai-onboarding.md'));
});

// Root route with documentation links
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to the Dynamic Mock API Server',
    description: 'Generate custom mock data based on your defined structure',
    apis: {
      rest: {
        endpoint: '/api/mock/:app/:endpoint',
        method: 'POST',
        description: 'REST API for generating mock data'
      },
      graphql: {
        endpoint: '/graphql',
        description: 'GraphQL API for generating mock data with queries and mutations'
      },
      websocket: {
        endpoint: '/ws',
        protocol: 'WebSocket',
        description: 'Real-time WebSocket API for live mock data generation and streaming'
      }
    },
    documentation: {
      html: {
        url: '/docs',
        description: 'Visit the HTML documentation page for detailed information and examples'
      },
      swagger: {
        url: '/api-docs',
        description: 'Visit the Swagger UI for interactive REST API documentation'
      },
      graphql: {
        url: '/graphql',
        description: 'Visit the GraphQL playground for interactive GraphQL API testing'
      },
      ai_onboarding: {
        json: {
          url: '/ai-info',
          description: 'AI-friendly JSON endpoint with comprehensive API information for AI agents'
        },
        markdown: {
          url: '/ai-onboarding.md',
          description: 'Detailed markdown guide for AI agent onboarding and integration'
        }
      },
      websocket: {
        info: {
          url: '/ws-info',
          description: 'WebSocket server information and metrics'
        },
        endpoint: {
          url: '/ws',
          description: 'WebSocket connection endpoint for real-time communication'
        }
      }
    },
    usage: {
      rest: {
        endpoint: '/api/mock/:app/:endpoint',
        method: 'POST',
        body: {
          structure: 'Object or Array defining the data structure with faker templates'
        },
        query: {
          records: 'Number of records to generate (default: 1)'
        }
      },
      graphql: {
        endpoint: '/graphql',
        queries: ['getMock'],
        mutations: ['postMock'],
        input: {
          app: 'Application name (e.g., users, products, orders)',
          endpoint: 'Endpoint name (e.g., list, details, search)',
          structure: 'Object or Array defining the data structure with faker templates',
          records: 'Number of records to generate (default: 1)'
        }
      },
      websocket: {
        endpoint: '/ws',
        events: {
          mock: ['generate', 'generateBatch', 'generateStream', 'subscribe', 'unsubscribe'],
          system: ['ping', 'getInfo', 'getMetrics', 'broadcast'],
          room: ['join', 'leave'],
          namespace: ['join']
        },
        features: {
          realTimeData: 'Live mock data generation and streaming',
          rooms: 'Join/leave rooms for group communication',
          namespaces: 'Organize connections by namespace',
          heartbeat: 'Automatic connection health monitoring',
          compression: 'Message compression for better performance',
          binaryData: 'Support for binary message formats'
        }
      }
    },
    examples: {
      rest: {
        request: {
          url: '/api/mock/users/list?records=5',
          body: {
            structure: {
              id: '{{guid}}',
              name: '{{person.fullName}}',
              email: '{{internet.email}}',
              age: '{{integer(18, 65)}}',
              address: {
                street: '{{location.street}}',
                city: '{{location.city}}',
                country: '{{location.country}}'
              }
            }
          }
        }
      },
      graphql: {
        query: `query {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      data
    }
  }
}`,
        mutation: `mutation {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      name: "{{person.fullName}}"
      company: {
        name: "{{company.name().toUpperCase()}}"
      }
    }
  }) {
    ... on MockDataResponse {
      success
      data
    }
  }
}`
      },
      websocket: {
        connection: `const ws = new WebSocket('ws://localhost:3000/ws');`,
        generateMockData: `ws.send(JSON.stringify({
  type: 'mock',
  event: 'generate',
  data: {
    structure: {
      id: "{{guid}}",
      name: "{{person.fullName}}",
      email: "{{internet.email}}"
    },
    records: 3
  }
}));`,
        joinRoom: `ws.send(JSON.stringify({
  type: 'room',
  event: 'join',
  data: { roomName: 'chat-room' }
}));`,
        subscribe: `ws.send(JSON.stringify({
  type: 'mock',
  event: 'subscribe',
  data: {
    topic: 'user-updates',
    structure: { id: "{{guid}}", name: "{{person.fullName}}" }
  }
}));`
      }
    }
  });
});

// Start the server
const server = app.listen(PORT, () => {
  console.log(`Dynamic Mock API Server is running on port ${PORT}`);
  console.log(`REST API: http://localhost:${PORT}/api/mock`);
  console.log(`GraphQL API: http://localhost:${PORT}/graphql`);
  console.log(`WebSocket API: ws://localhost:${PORT}/ws`);
  console.log(`API Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`HTML Documentation: http://localhost:${PORT}/docs`);
  console.log(`AI Onboarding: http://localhost:${PORT}/ai-info`);
});

// Initialize WebSocket server
const wsIntegration = new WebSocketIntegration(server, {
  compression: true,
  binaryProtocol: false,
  allowAnonymous: true,
  rateLimitMaxRequests: 100,
  rateLimitWindowMs: 60000
});

// Add WebSocket info endpoint
app.get('/ws-info', (req, res) => {
  res.json({
    success: true,
    websocket: {
      endpoint: '/ws',
      protocol: 'ws',
      features: {
        realTimeData: true,
        rooms: true,
        namespaces: true,
        heartbeat: true,
        compression: true,
        binaryData: true,
        mockDataGeneration: true,
        subscriptions: true
      },
      metrics: wsIntegration.getMetrics(),
      connections: wsIntegration.getConnections().length
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = app;
