{"name": "mirage", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Express backend app with mock data endpoints using Faker", "dependencies": {"@faker-js/faker": "^9.8.0", "cors": "^2.8.5", "express": "^5.1.0", "express-validator": "^7.2.1", "graphql": "^16.11.0", "graphql-yoga": "^5.13.5", "js-yaml": "^4.1.0", "mongoose": "^8.15.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}