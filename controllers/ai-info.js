/**
 * AI Information Controller
 * 
 * This controller provides comprehensive information about the Mirage API
 * specifically formatted for AI agents and systems to understand and use.
 */

/**
 * Get AI-friendly information about the Mirage API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAIInfo = (req, res) => {
  const aiInfo = {
    api_name: "Mirage Dynamic Mock API Server",
    version: "1.0.0",
    description: "A dynamic mock API server that generates realistic fake data based on custom structures using Faker.js",
    
    purpose: {
      primary: "Generate realistic mock data for development, testing, and prototyping",
      use_cases: [
        "Frontend development and UI testing",
        "API testing and database seeding",
        "Prototype development and demos",
        "Data modeling and structure testing",
        "Client-side application development",
        "Mock data for presentations and documentation"
      ]
    },

    capabilities: {
      data_generation: {
        description: "Generate realistic mock data using Faker.js templates",
        features: [
          "Custom data structures (objects, arrays, nested data)",
          "Method chaining support (e.g., .toUpperCase(), .toLowerCase())",
          "Dynamic field relationships",
          "Configurable data volume (1 to many records)",
          "Real-time data generation"
        ]
      },
      api_support: {
        rest: {
          endpoint: "/api/mock/{app}/{endpoint}",
          method: "POST",
          description: "Traditional HTTP REST API"
        },
        graphql: {
          endpoint: "/graphql",
          methods: ["POST", "GET"],
          description: "GraphQL API with queries and mutations",
          operations: ["getMock (query)", "postMock (mutation)"]
        },
        websocket: {
          endpoint: "/ws",
          protocol: "WebSocket",
          description: "Real-time bidirectional communication API",
          features: [
            "Real-time mock data generation",
            "Data streaming",
            "Room-based communication",
            "Subscription system",
            "Heartbeat monitoring",
            "Binary data support",
            "Message compression"
          ]
        }
      }
    },

    template_system: {
      syntax: "{{template_name}}",
      description: "Use Faker.js templates wrapped in double curly braces",
      examples: {
        basic: {
          "{{person.fullName}}": "John Doe",
          "{{internet.email}}": "<EMAIL>",
          "{{integer(1, 100)}}": 42,
          "{{date.past}}": "2023-05-15T10:30:00.000Z"
        },
        method_chaining: {
          "{{company.name().toUpperCase()}}": "ACME CORPORATION",
          "{{person.firstName().toLowerCase()}}": "john",
          "{{location.state().toUpperCase()}}": "CALIFORNIA"
        }
      }
    },

    template_categories: {
      personal: [
        "person.fullName", "person.firstName", "person.lastName",
        "person.jobTitle", "person.prefix", "person.suffix",
        "person.gender", "person.bio"
      ],
      contact: [
        "internet.email", "internet.url", "internet.domainName",
        "phone.number", "phone.number(\"###-###-####\")"
      ],
      location: [
        "location.street", "location.city", "location.state",
        "location.country", "location.zipCode",
        "location.latitude", "location.longitude"
      ],
      business: [
        "company.name", "company.catchPhrase",
        "finance.amount", "finance.currencyCode"
      ],
      datetime: [
        "date.past", "date.future", "date.birthdate",
        "date.recent", "date.soon"
      ],
      numbers: [
        "integer(min, max)", "floating(min, max, precision)",
        "boolean"
      ],
      text: [
        "lorem.words(count)", "lorem.sentence",
        "lorem.paragraph", "lorem.text"
      ],
      identifiers: [
        "guid", "objectId()", "random(\"option1\", \"option2\")"
      ]
    },

    api_endpoints: {
      rest: {
        endpoint: "/api/mock/{app}/{endpoint}",
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        request_body: {
          structure: "Object or Array defining the data structure with faker templates",
          example: {
            structure: {
              id: "{{guid}}",
              name: "{{person.fullName}}",
              email: "{{internet.email}}",
              age: "{{integer(18, 65)}}",
              company: {
                name: "{{company.name().toUpperCase()}}",
                department: "{{person.jobArea}}"
              }
            }
          }
        },
        query_parameters: {
          records: {
            type: "integer",
            default: 1,
            description: "Number of records to generate"
          }
        },
        response_format: {
          success: "boolean",
          app: "string",
          endpoint: "string", 
          records: "integer",
          data: "generated mock data"
        }
      },
      graphql: {
        endpoint: "/graphql",
        operations: {
          query: {
            name: "getMock",
            description: "Read-only mock data generation",
            example: `query {
  getMock(input: {
    app: "users"
    endpoint: "list"
    structure: {
      id: "{{guid}}"
      name: "{{person.fullName}}"
      email: "{{internet.email}}"
    }
    records: 3
  }) {
    ... on MockDataResponse {
      success
      app
      endpoint
      records
      data
    }
  }
}`
          },
          mutation: {
            name: "postMock",
            description: "Create new mock data",
            example: `mutation {
  postMock(input: {
    app: "contacts"
    endpoint: "form"
    structure: {
      _id: "{{objectId()}}"
      name: "{{person.fullName}}"
      company: {
        name: "{{company.name().toUpperCase()}}"
      }
    }
  }) {
    ... on MockDataResponse {
      success
      data
    }
  }
}`
          }
        }
      },
      websocket: {
        endpoint: "/ws",
        protocol: "WebSocket",
        connection: "ws://localhost:3000/ws",
        message_format: {
          type: "string (mock|system|room|namespace)",
          event: "string (specific event name)",
          data: "object (event-specific data)"
        },
        events: {
          mock: ["generate", "generateBatch", "generateStream", "subscribe", "unsubscribe"],
          system: ["ping", "getInfo", "getMetrics", "broadcast"],
          room: ["join", "leave"],
          namespace: ["join"]
        },
        examples: {
          generate_mock: {
            type: "mock",
            event: "generate",
            data: {
              structure: { id: "{{guid}}", name: "{{person.fullName}}" },
              records: 3
            }
          },
          stream_data: {
            type: "mock",
            event: "generateStream",
            data: {
              structure: { timestamp: "{{date.recent}}", value: "{{integer(1, 100)}}" },
              interval: 1000,
              duration: 10000
            }
          },
          join_room: {
            type: "room",
            event: "join",
            data: { roomName: "analytics-room" }
          }
        }
      }
    },

    advanced_features: {
      array_generation: {
        description: "Generate arrays of data using repeat syntax",
        syntax: "{{repeat(count)}}",
        example: {
          tags: ["{{repeat(3)}}", "{{lorem.words(1)}}"],
          friends: [
            "{{repeat(5)}}",
            {
              id: "{{guid}}",
              name: "{{person.fullName}}"
            }
          ]
        }
      },
      nested_structures: {
        description: "Create complex nested data structures",
        example: {
          user: {
            profile: {
              personal: {
                name: "{{person.fullName}}",
                age: "{{integer(18, 65)}}"
              },
              contact: {
                email: "{{internet.email}}",
                phone: "{{phone.number}}"
              }
            },
            preferences: {
              theme: "{{random(\"light\", \"dark\")}}",
              notifications: "{{boolean}}"
            }
          }
        }
      }
    },

    documentation_endpoints: {
      swagger_ui: "/api-docs",
      graphql_playground: "/graphql",
      websocket_demo: "/ws-demo",
      websocket_info: "/ws-info",
      html_documentation: "/docs",
      ai_onboarding_file: "/ai-onboarding.md",
      ai_info_endpoint: "/ai-info"
    },

    quick_start_guide: {
      step_1: "Choose API style (REST, GraphQL, or WebSocket)",
      step_2: "Define your data structure using Faker.js templates",
      step_3: "Send request to appropriate endpoint or establish WebSocket connection",
      step_4: "Extract generated data from response or handle real-time messages",
      example_curl: `curl -X POST http://localhost:3000/api/mock/users/list \\
  -H "Content-Type: application/json" \\
  -d '{
    "structure": {
      "id": "{{guid}}",
      "name": "{{person.fullName}}",
      "email": "{{internet.email}}"
    }
  }'`
    },

    server_info: {
      default_port: 3000,
      cors_enabled: true,
      rate_limiting: "None (development server)",
      content_type: "application/json",
      base_url_local: "http://localhost:3000"
    },

    ai_integration_tips: {
      best_practices: [
        "Start with simple structures and gradually add complexity",
        "Use method chaining for data transformation",
        "Leverage array generation for list data",
        "Combine multiple template types for realistic data",
        "Use GraphQL for more flexible data querying",
        "Use WebSocket for real-time data requirements",
        "Implement proper error handling for WebSocket connections",
        "Use rooms and namespaces for organized communication"
      ],
      common_patterns: {
        user_profile: {
          id: "{{guid}}",
          name: "{{person.fullName}}",
          email: "{{internet.email}}",
          avatar: "https://i.pravatar.cc/{{integer(100, 500)}}"
        },
        product_listing: {
          id: "{{guid}}",
          name: "{{commerce.productName}}",
          price: "{{finance.amount}}",
          category: "{{commerce.department}}"
        },
        contact_form: {
          _id: "{{objectId()}}",
          name: "{{person.fullName}}",
          email: "{{internet.email}}",
          message: "{{lorem.paragraph}}"
        }
      }
    }
  };

  res.json(aiInfo);
};

module.exports = {
  getAIInfo
};
