/**
 * Dynamic Mock API Controller
 *
 * This file contains the controller function for the dynamic mock API endpoint.
 * It validates the request, extracts the structure from the request body,
 * and uses the helper function to generate mock data based on the structure.
 */

const { validationResult } = require('express-validator');
const errorsMsg = require('../../common/errors');
const { generateMockData } = require('./helpers');

/**
 * Dynamic mock controller that generates data based on the provided structure
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const mockController = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorsMsg.validationError(res, errors.array());
    }

    // Extract parameters
    const { app, endpoint } = req.params;
    const { structure } = req.body;
    const records = parseInt(req.query.records) || 1;

    // Validate structure
    if (!structure) {
      return errorsMsg.badRequest(res, 'Invalid structure format');
    }

    // Generate mock data
    const mockData = generateMockData(structure, Number(records));

    // Log request info (for debugging)
    console.log(`Generated mock data for ${app}/${endpoint} with ${records} records`);

    // Return response
    return res.status(200).json({
      success: true,
      app,
      endpoint,
      records: Array.isArray(mockData) ? mockData.length : 1,
      data: mockData
    });
  } catch (err) {
    console.error('Error generating mock data:', err);
    return errorsMsg.serverError(res, 'Server error occurred while generating mock data');
  }
};

module.exports = mockController;
