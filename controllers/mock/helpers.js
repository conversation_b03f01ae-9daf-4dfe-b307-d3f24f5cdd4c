/**
 * Dynamic Mock API Helper Functions
 *
 * This file contains helper functions to generate dynamic mock data
 * using the Faker library based on a provided structure.
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

/**
 * Clean string by removing quotes
 * @param {string} str - String to clean
 * @returns {string} Cleaned string
 */
const cleanString = (str) =>
  Boolean(str) && typeof str === "string" ? str?.replace(/['"]/g, "") : str;

/**
 * Helper functions for generating different types of mock data
 */
/**
 * Determine if a string represents an object or a function
 * @param {string} str - String to check
 * @returns {string} 'object' or 'function'
 */
const operationType = (str) => {
  try {
    str = str?.trim();

    if (str?.startsWith("{") && str?.endsWith("}")) {
      return "object";
    } else {
      return "function";
    }
  } catch (error) {
    console.error("Error determining operation type:", error);
    return "";
  }
};

/**
 * Handle repeat operations
 * @param {number} count - Number of times to repeat
 * @param {string|object} operation - Operation to repeat
 * @returns {Array} Array of generated values
 */
const handleRepeat = (count, operation) => {
  try {
    if (operationType(operation) === "object") {
      return generateMockData(operation, count);
    } else {
      return Array.from({ length: count }).map((_, index) => {
        const [helperName, ...args] = operation?.split(/[(),]/).filter(Boolean);
        const trimmedArgs = args.map((arg) => arg.trim().replace(/^'|'$/g, ""));
        return handleWrite({ helperName, trimmedArgs, index });
      });
    }
  } catch (error) {
    console.error("Error handling repeat:", error);
    return [];
  }
};

/**
 * Resolve a helper function from a path
 * @param {string} helperName - Name of the helper function
 * @returns {Function} Helper function
 */
const resolveHelperPath = (helperName) => {
  const parts = helperName.split(".");
  let obj = helpers;

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];

    if (typeof obj[part] === "function") {
      return obj[part];
    } else {
      obj = obj[part];
    }
  }

  return obj;
};

/**
 * Check if a string can be parsed as JSON
 * @param {string} str - String to check
 * @returns {boolean} True if parsable, false otherwise
 */
const isParsable = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Handle method chaining (e.g., toUpperCase(), toLowerCase())
 * @param {string} result - The result to apply methods to
 * @param {string} chainedMethods - The chained methods to apply
 * @returns {string} The result after applying the methods
 */
const applyMethodChain = (result, chainedMethods) => {
  if (!chainedMethods) return result;

  // Extract method name without parentheses
  const methodName = chainedMethods.replace(/\(\)$/, '');
  let currentResult = result;

  // Apply the method
  if (methodName === 'toUpperCase' && typeof currentResult === 'string') {
    currentResult = currentResult.toUpperCase();
  } else if (methodName === 'toLowerCase' && typeof currentResult === 'string') {
    currentResult = currentResult.toLowerCase();
  } else if (methodName === 'trim' && typeof currentResult === 'string') {
    currentResult = currentResult.trim();
  }
  // Add more method handlers as needed

  return currentResult;
};

/**
 * Helper functions for generating different types of mock data
 */
const helpers = {
  // ID generators
  objectId: ({ args }) => {
    const id = args?.[0];
    return id ? String(id) : new mongoose.Types.ObjectId();
  },
  index: ({ index }) => index ?? "",
  guid: () => faker.string.uuid(),

  // Boolean generator
  boolean: () => faker.datatype.boolean(),

  // Person data generators
  person: ({ args, operation }) => {
    if (typeof faker.person[cleanString(operation)] === "function") {
      return faker.person[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for person: ${operation}`);
    }
  },

  // Company data generators
  company: ({ args, operation }) => {
    if (typeof faker.company[cleanString(operation)] === "function") {
      return faker.company[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for company: ${operation}`);
    }
  },

  // Internet data generators
  internet: ({ args, operation }) => {
    if (typeof faker.internet[cleanString(operation)] === "function") {
      return faker.internet[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for internet: ${operation}`);
    }
  },

  // Phone data generators
  phone: ({ args, operation }) => {
    if (typeof faker.phone[cleanString(operation)] === "function") {
      return faker.phone[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for phone: ${operation}`);
    }
  },

  // Location data generators
  location: ({ args, operation }) => {
    if (typeof faker.location[cleanString(operation)] === "function") {
      return faker.location[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for location: ${operation}`);
    }
  },

  // Lorem ipsum generators
  lorem: ({ args, operation }) => {
    if (typeof faker.lorem[cleanString(operation)] === "function") {
      return faker.lorem[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for lorem: ${operation}`);
    }
  },

  // Date generators
  date: ({ args, operation }) => {
    if (typeof faker.date[cleanString(operation)] === "function") {
      return faker.date[cleanString(operation)](...args)?.toISOString();
    } else {
      throw new Error(`Invalid type for date: ${operation}`);
    }
  },

  // Number generators
  amount: ({ args }) => {
    const [min, max, decimals] = args;
    return faker.finance.amount({
      min: cleanString(min),
      max: cleanString(max),
      decimals: cleanString(decimals),
    });
  },
  floating: ({ args }) => {
    const [min, max] = args;
    return faker.number.float({ min: cleanString(min), max: cleanString(max) });
  },
  integer: ({ args }) => {
    const [min, max] = args;
    return faker.number.int({ min: cleanString(min), max: cleanString(max) });
  },

  // Array and object generators
  random: ({ args }) => faker.helpers.arrayElement(args),
  repeat: ({ args }) => {
    return handleRepeat(...args);
  },

  // Commerce generators
  commerce: ({ args, operation }) => {
    if (typeof faker.commerce[cleanString(operation)] === "function") {
      return faker.commerce[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for commerce: ${operation}`);
    }
  },

  // Image generators
  image: ({ args, operation }) => {
    if (typeof faker.image[cleanString(operation)] === "function") {
      return faker.image[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for image: ${operation}`);
    }
  },

  // Finance generators
  finance: ({ args, operation }) => {
    if (typeof faker.finance[cleanString(operation)] === "function") {
      return faker.finance[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for finance: ${operation}`);
    }
  },

  // System generators
  system: ({ args, operation }) => {
    if (typeof faker.system[cleanString(operation)] === "function") {
      return faker.system[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for system: ${operation}`);
    }
  },

  // Vehicle generators
  vehicle: ({ args, operation }) => {
    if (typeof faker.vehicle[cleanString(operation)] === "function") {
      return faker.vehicle[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for vehicle: ${operation}`);
    }
  },

  // Color generators
  color: ({ args, operation }) => {
    if (typeof faker.color[cleanString(operation)] === "function") {
      return faker.color[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for color: ${operation}`);
    }
  },

  // Word generators
  word: ({ args, operation }) => {
    if (typeof faker.word[cleanString(operation)] === "function") {
      return faker.word[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for word: ${operation}`);
    }
  },

  // Music generators
  music: ({ args, operation }) => {
    if (typeof faker.music[cleanString(operation)] === "function") {
      return faker.music[cleanString(operation)](...args);
    } else {
      throw new Error(`Invalid type for music: ${operation}`);
    }
  },

  // Custom helpers
  timestamp: () => Date.now(),
  oneOf: ({ args }) => faker.helpers.arrayElement(args),
  arrayOf: ({ args }) => {
    const [count, value] = args;
    return Array.from({ length: parseInt(count) }).map(() => value);
  }
};

/**
 * Handle writing a value based on a helper function
 * @param {Object} params - Parameters for the helper function
 * @param {string} params.helperName - Name of the helper function
 * @param {Array} params.trimmedArgs - Arguments for the helper function
 * @param {string} params.operation - Operation to perform
 * @param {number} params.index - Index of the current item
 * @returns {*} Generated value
 */
const handleWrite = ({ helperName, trimmedArgs, operation, index }) => {
  try {
    if (helperName) {
      const action = helperName?.includes(".")
        ? resolveHelperPath(helperName)
        : helpers[helperName];

      const args =
        trimmedArgs.length > 0
          ? trimmedArgs.map((arg) => (isNaN(arg) ? arg : parseFloat(arg)))
          : [];

      if (typeof action === "function") {
        const output = action({
          args,
          index,
          operation:
            helperName === "repeat" ? operation : helperName?.split(".")[1],
        });

        if (isParsable(output)) {
          return JSON.parse(output);
        } else {
          return output;
        }
      } else {
        return helperName;
      }
    } else {
      return helperName;
    }
  } catch (error) {
    console.error("Error handling write:", error);
    return helperName;
  }
};

/**
 * Generate mock data based on a structure
 * @param {Object|Array} structure - Structure to generate data from
 * @param {number} numRecords - Number of records to generate
 * @returns {Object|Array} Generated mock data
 */
const generateMockData = (structure, numRecords) => {
  try {
    /**
     * Generate data for an array element
     * @param {*} operation - Operation to perform
     * @param {number} index - Index of the current item
     * @returns {*} Generated value
     */
    const generateArrayData = (operation, index) => {
      try {
        if (typeof operation === "object") {
          return generateSingleObject(operation, index);
        } else if (typeof operation === "string") {
          const action = operation?.startsWith("{{")
            ? operation?.slice(2, -2)?.trim()
            : "";

          // Check for method chaining (e.g., company.name().toUpperCase())
          const methodChainMatch = action.match(/^(.*?\))\.(.+)$/);

          if (methodChainMatch) {
            // Extract the base function call and the chained methods
            const baseCall = methodChainMatch[1];
            const chainedMethods = methodChainMatch[2];

            // Process the base function call
            const [helperName, ...args] = baseCall
              .split(/[(),]/)
              .filter(Boolean);
            const trimmedArgs = args.map((arg) =>
              arg.trim().replace(/^'|'$/g, "")
            );

            // Get the result from the base function
            let result = handleWrite({ helperName, trimmedArgs, index });

            // Apply chained methods
            result = applyMethodChain(result, chainedMethods);

            return result;
          } else {
            // Regular function call without chaining
            const [helperName, ...args] = action.split(/[(),]/).filter(Boolean);
            const trimmedArgs = args.map((arg) =>
              arg.trim().replace(/^'|'$/g, "")
            );

            return handleWrite({ helperName, trimmedArgs, index });
          }
        } else {
          return operation;
        }
      } catch (error) {
        console.error("Error generating array data:", error);
        return operation;
      }
    };

    /**
     * Generate a single object based on a structure
     * @param {Object} structure - Structure to generate data from
     * @param {number} index - Index of the current item
     * @returns {Object} Generated object
     */
    const generateSingleObject = (structure, index) => {
      return Object.entries(structure)?.reduce((acc, [key, value]) => {
        let newValue;

        if (typeof value === "string") {
          newValue = value.replace(/{{(.*?)}}/g, (obj, helperCall) => {
            // Check for method chaining (e.g., company.name().toUpperCase())
            const methodChainMatch = helperCall.match(/^(.*?\))\.(.+)$/);

            if (methodChainMatch) {
              // Extract the base function call and the chained methods
              const baseCall = methodChainMatch[1];
              const chainedMethods = methodChainMatch[2];

              // Process the base function call
              const [helperName, ...args] = baseCall
                .split(/[(),]/)
                .filter(Boolean);
              const trimmedArgs = args.map((arg) =>
                arg.trim().replace(/^'|'$/g, "")
              );

              // Get the result from the base function
              let result = handleWrite({ helperName, trimmedArgs, index });

              // Apply chained methods
              result = applyMethodChain(result, chainedMethods);

              return result;
            } else {
              // Regular function call without chaining
              const [helperName, ...args] = helperCall
                .split(/[(),]/)
                .filter(Boolean);
              const trimmedArgs = args.map((arg) =>
                arg.trim().replace(/^'|'$/g, "")
              );

              return handleWrite({ helperName, trimmedArgs, index });
            }
          });
        } else if (Array.isArray(value)) {
          let action = value?.[0] ?? "";
          let repeats = 0;
          if (typeof action === "string" && action.includes("repeat")) {
            repeats = parseInt(action.match(/\d+/)?.[0] || "0");
          }
          const operationLength = value?.length;
          let operation = value?.[1] ?? "";

          if (repeats && operationLength <= 2) {
            newValue = Array.from({ length: repeats }).map((_, index) =>
              generateArrayData(operation, index)
            );
          } else {
            const repeatIndex = value?.findIndex((item) =>
              String(item)?.includes("repeat")
            );

            const dataToUse =
              repeatIndex > 0 ? value?.slice(0, repeatIndex) : value;

            newValue = dataToUse.map((op, index) =>
              generateArrayData(op, index)
            );

            if (repeatIndex > -1) {
              action = value?.[repeatIndex] ?? "";
              if (typeof action === "string") {
                repeats = parseInt(action.match(/\d+/)?.[0] || "0");
              }

              operation = value?.[repeatIndex + 1] ?? "";

              const repetition = Array.from({ length: repeats }).map(
                (_, index) => generateArrayData(operation, index)
              );
              newValue = newValue?.concat(repetition);
            }
          }
        } else if (typeof value === "object") {
          newValue = generateSingleObject(value, index);
        } else {
          newValue = value;
        }

        if (isParsable(newValue)) {
          acc[key] = JSON.parse(newValue);
        } else {
          acc[key] = newValue;
        }

        return acc;
      }, {});
    };

    // Handle array or object structure
    if (Array.isArray(structure)) {
      return Array.from({ length: numRecords }).map((_, index) =>
        generateSingleObject(structure[0], index)
      );
    } else {
      return generateSingleObject(structure, 0);
    }
  } catch (error) {
    console.error("Error generating mock data:", error);
    return "No data";
  }
};

module.exports = {
  generateMockData,
};
